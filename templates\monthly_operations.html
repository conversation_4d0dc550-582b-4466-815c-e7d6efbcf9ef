{% extends "base.html" %}

{% block title %}ALEMIS - العمليات الشهرية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">العمليات الشهرية</h2>
        
        <!-- أدوات التصفية -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-1"></i>
                تصفية العمليات
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="month" class="form-label">الشهر</label>
                            <select class="form-select" id="month" name="month">
                                <option value="1" {% if current_month == 1 %}selected{% endif %}>يناير</option>
                                <option value="2" {% if current_month == 2 %}selected{% endif %}>فبراير</option>
                                <option value="3" {% if current_month == 3 %}selected{% endif %}>مارس</option>
                                <option value="4" {% if current_month == 4 %}selected{% endif %}>أبريل</option>
                                <option value="5" {% if current_month == 5 %}selected{% endif %}>مايو</option>
                                <option value="6" {% if current_month == 6 %}selected{% endif %}>يونيو</option>
                                <option value="7" {% if current_month == 7 %}selected{% endif %}>يوليو</option>
                                <option value="8" {% if current_month == 8 %}selected{% endif %}>أغسطس</option>
                                <option value="9" {% if current_month == 9 %}selected{% endif %}>سبتمبر</option>
                                <option value="10" {% if current_month == 10 %}selected{% endif %}>أكتوبر</option>
                                <option value="11" {% if current_month == 11 %}selected{% endif %}>نوفمبر</option>
                                <option value="12" {% if current_month == 12 %}selected{% endif %}>ديسمبر</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="year" class="form-label">السنة</label>
                            <select class="form-select" id="year" name="year">
                                {% for y in range(current_year-2, current_year+3) %}
                                <option value="{{ y }}" {% if y == current_year %}selected{% endif %}>{{ y }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" id="update-operations" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث العمليات
                        </button>
                    </div>
                    {% if session.role == 'gm' %}
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addOperationModal">
                            <i class="fas fa-plus me-1"></i>
                            إضافة عملية جديدة
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- جدول العمليات -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-clipboard-list me-1"></i>
                العمليات الشهرية لشهر {{ month_name }} {{ current_year }}
            </div>
            <div class="card-body">
                {% if operations %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>تاريخ الإضافة</th>
                                {% if session.role == 'gm' %}
                                <th>الإجراءات</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for operation in operations %}
                            <tr>
                                <td>{{ operation.operation_date }}</td>
                                <td>{{ operation.description }}</td>
                                <td>{{ operation.created_at }}</td>
                                {% if session.role == 'gm' %}
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary edit-operation" data-bs-toggle="modal" data-bs-target="#editOperationModal" data-id="{{ operation.id }}" data-date="{{ operation.operation_date }}" data-description="{{ operation.description }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger delete-operation" data-id="{{ operation.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد عمليات مسجلة لهذا الشهر.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal: إضافة عملية جديدة -->
<div class="modal fade" id="addOperationModal" tabindex="-1" aria-labelledby="addOperationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addOperationModalLabel">إضافة عملية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addOperationForm">
                    <div class="mb-3">
                        <label for="operation_date" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="operation_date" name="operation_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveOperation">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal: تعديل عملية -->
<div class="modal fade" id="editOperationModal" tabindex="-1" aria-labelledby="editOperationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editOperationModalLabel">تعديل عملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editOperationForm">
                    <input type="hidden" id="edit_operation_id" name="operation_id">
                    <div class="mb-3">
                        <label for="edit_operation_date" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="edit_operation_date" name="operation_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="updateOperation">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث العمليات عند تغيير الشهر أو السنة
        document.getElementById('update-operations').addEventListener('click', function() {
            const month = document.getElementById('month').value;
            const year = document.getElementById('year').value;
            
            window.location.href = `{{ url_for('monthly_operations') }}?month=${month}&year=${year}`;
        });
        
        // إضافة عملية جديدة
        {% if session.role == 'gm' %}
        document.getElementById('saveOperation').addEventListener('click', function() {
            const operationDate = document.getElementById('operation_date').value;
            const description = document.getElementById('description').value;
            
            if (!operationDate || !description) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            fetch('{{ url_for("add_monthly_operation") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_date: operationDate,
                    description: description,
                    month: {{ current_month }},
                    year: {{ current_year }}
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            });
        });
        
        // تعديل عملية
        const editButtons = document.querySelectorAll('.edit-operation');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const date = this.getAttribute('data-date');
                const description = this.getAttribute('data-description');
                
                document.getElementById('edit_operation_id').value = id;
                document.getElementById('edit_operation_date').value = date;
                document.getElementById('edit_description').value = description;
            });
        });
        
        document.getElementById('updateOperation').addEventListener('click', function() {
            const id = document.getElementById('edit_operation_id').value;
            const operationDate = document.getElementById('edit_operation_date').value;
            const description = document.getElementById('edit_description').value;
            
            if (!operationDate || !description) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }
            
            fetch('{{ url_for("update_monthly_operation") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation_id: id,
                    operation_date: operationDate,
                    description: description
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            });
        });
        
        // حذف عملية
        const deleteButtons = document.querySelectorAll('.delete-operation');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                
                if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
                    fetch('{{ url_for("delete_monthly_operation") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            operation_id: id
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });
        {% endif %}
    });
</script>
{% endblock %}
