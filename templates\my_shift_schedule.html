{% extends "base.html" %}

{% block title %}ALEMIS - جدول دوامي الشهري{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">جدول دوامي الشهري</h2>
        
        <!-- أدوات التصفية -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-1"></i>
                تصفية الجدول
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="month" class="form-label">الشهر</label>
                            <select class="form-select" id="month" name="month">
                                <option value="1" {% if current_month == 1 %}selected{% endif %}>يناير</option>
                                <option value="2" {% if current_month == 2 %}selected{% endif %}>فبراير</option>
                                <option value="3" {% if current_month == 3 %}selected{% endif %}>مارس</option>
                                <option value="4" {% if current_month == 4 %}selected{% endif %}>أبريل</option>
                                <option value="5" {% if current_month == 5 %}selected{% endif %}>مايو</option>
                                <option value="6" {% if current_month == 6 %}selected{% endif %}>يونيو</option>
                                <option value="7" {% if current_month == 7 %}selected{% endif %}>يوليو</option>
                                <option value="8" {% if current_month == 8 %}selected{% endif %}>أغسطس</option>
                                <option value="9" {% if current_month == 9 %}selected{% endif %}>سبتمبر</option>
                                <option value="10" {% if current_month == 10 %}selected{% endif %}>أكتوبر</option>
                                <option value="11" {% if current_month == 11 %}selected{% endif %}>نوفمبر</option>
                                <option value="12" {% if current_month == 12 %}selected{% endif %}>ديسمبر</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="year" class="form-label">السنة</label>
                            <select class="form-select" id="year" name="year">
                                {% for y in range(current_year-2, current_year+3) %}
                                <option value="{{ y }}" {% if y == current_year %}selected{% endif %}>{{ y }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" id="update-schedule" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث الجدول
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- حالة الجدول -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-info-circle me-1"></i>
                حالة الجدول
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>حالة الجدول:</strong> 
                            {% if schedule_status == 'draft' %}
                            <span class="badge bg-warning">مسودة</span>
                            <small class="text-muted d-block mt-1">يمكنك تبديل الدوام مع زملائك في هذه المرحلة</small>
                            {% elif schedule_status == 'manager_approved' %}
                            <span class="badge bg-info">موافقة المدير</span>
                            <small class="text-muted d-block mt-1">تم اعتماد الجدول من قبل المدير، يمكنك تقديم طلب تبديل رسمي</small>
                            {% elif schedule_status == 'hr_approved' %}
                            <span class="badge bg-primary">موافقة الموارد البشرية</span>
                            <small class="text-muted d-block mt-1">تم اعتماد الجدول من قبل الموارد البشرية، يمكنك تقديم طلب تبديل رسمي</small>
                            {% elif schedule_status == 'gm_approved' %}
                            <span class="badge bg-success">معتمد</span>
                            <small class="text-muted d-block mt-1">تم اعتماد الجدول بشكل نهائي، يمكنك تقديم طلب تبديل رسمي</small>
                            {% else %}
                            <span class="badge bg-secondary">غير متوفر</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        {% if schedule_status == 'draft' %}
                        <a href="{{ url_for('direct_shift_swap') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-exchange-alt me-1"></i>
                            تبديل دوام مباشر
                        </a>
                        {% else %}
                        <a href="{{ url_for('new_shift_swap') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-file-alt me-1"></i>
                            تقديم طلب تبديل
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جدول الشفتات -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar me-1"></i>
                جدول دوامي لشهر {{ month_name }} {{ current_year }}
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="width: 200px;">الموظف</th>
                                {% for day in range(1, num_days + 1) %}
                                <th style="width: 40px;" class="text-center {% if day_of_week[day-1] == 5 %}bg-light{% endif %} {% if day_of_week[day-1] == 6 %}bg-light-gray{% endif %}">{{ day }}</th>
                                {% endfor %}
                            </tr>
                            <tr>
                                <th>اليوم</th>
                                {% for day in range(1, num_days + 1) %}
                                <th class="text-center {% if day_of_week[day-1] == 5 %}bg-light{% endif %} {% if day_of_week[day-1] == 6 %}bg-light-gray{% endif %}">
                                    {% if day_of_week[day-1] == 0 %}
                                    السبت
                                    {% elif day_of_week[day-1] == 1 %}
                                    الأحد
                                    {% elif day_of_week[day-1] == 2 %}
                                    الإثنين
                                    {% elif day_of_week[day-1] == 3 %}
                                    الثلاثاء
                                    {% elif day_of_week[day-1] == 4 %}
                                    الأربعاء
                                    {% elif day_of_week[day-1] == 5 %}
                                    الخميس
                                    {% elif day_of_week[day-1] == 6 %}
                                    الجمعة
                                    {% endif %}
                                </th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{ user.first_name }} {{ user.last_name }}</td>
                                {% for day in range(1, num_days + 1) %}
                                <td class="text-center {% if day_of_week[day-1] == 5 %}bg-light{% endif %} {% if day_of_week[day-1] == 6 %}bg-light-gray{% endif %}">
                                    {% if shift_data[day] %}
                                    <span class="badge {% if shift_data[day] == 'morning' %}bg-success{% elif shift_data[day] == 'evening' %}bg-primary{% elif shift_data[day] == 'night' %}bg-dark{% elif shift_data[day] == 'split' %}bg-info{% endif %}" 
                                          {% if schedule_status == 'draft' and day_of_week[day-1] == 6 %}
                                          data-bs-toggle="dropdown" role="button" aria-expanded="false" style="cursor: pointer;"
                                          {% endif %}>
                                        {% if shift_data[day] == 'morning' %}
                                        ص
                                        {% elif shift_data[day] == 'evening' %}
                                        م
                                        {% elif shift_data[day] == 'night' %}
                                        ل
                                        {% elif shift_data[day] == 'split' %}
                                        ف
                                        {% endif %}
                                    </span>
                                    {% if schedule_status == 'draft' and day_of_week[day-1] == 6 %}
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item select-friday-shift" href="#" data-day="{{ day }}" data-shift="morning">صباحي (8ص-4م)</a></li>
                                        <li><a class="dropdown-item select-friday-shift" href="#" data-day="{{ day }}" data-shift="evening">مسائي (4م-12ل)</a></li>
                                        <li><a class="dropdown-item select-friday-shift" href="#" data-day="{{ day }}" data-shift="night">ليلي (12ل-8ص)</a></li>
                                        <li><a class="dropdown-item select-friday-shift" href="#" data-day="{{ day }}" data-shift="none">بدون دوام</a></li>
                                    </ul>
                                    {% endif %}
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <div class="d-flex justify-content-start">
                        <div class="me-3"><span class="badge bg-success">ص</span> دوام صباحي (8ص-4م)</div>
                        <div class="me-3"><span class="badge bg-primary">م</span> دوام مسائي (4م-12ل)</div>
                        <div class="me-3"><span class="badge bg-dark">ل</span> دوام ليلي (12ل-8ص)</div>
                        <div><span class="badge bg-info">ف</span> دوام فترتين (9ص-12ظ، 4م-9م)</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث الجدول عند تغيير الشهر أو السنة
        document.getElementById('update-schedule').addEventListener('click', function() {
            const month = document.getElementById('month').value;
            const year = document.getElementById('year').value;
            
            window.location.href = `{{ url_for('my_shift_schedule') }}?month=${month}&year=${year}`;
        });
        
        // اختيار دوام يوم الجمعة
        const fridayShiftLinks = document.querySelectorAll('.select-friday-shift');
        fridayShiftLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const day = this.getAttribute('data-day');
                const shift = this.getAttribute('data-shift');
                
                fetch('{{ url_for("select_friday_shift") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        day: day,
                        shift: shift,
                        month: {{ current_month }},
                        year: {{ current_year }}
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.error);
                    }
                });
            });
        });
    });
</script>
{% endblock %}
