{% extends "base.html" %}

{% block title %}ALEMIS - تعديل قسم{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">تعديل قسم</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-building me-1"></i>
                نموذج تعديل قسم: {{ dept.name }}
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('edit_department', dept_id=dept.id) }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">اسم القسم</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ dept.name }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="manager_id" class="form-label">مدير القسم</label>
                            <select class="form-select" id="manager_id" name="manager_id">
                                <option value="">اختر مدير القسم (اختياري)</option>
                                {% for manager in managers %}
                                <option value="{{ manager.id }}" {% if dept.manager_id == manager.id %}selected{% endif %}>{{ manager.first_name }} {{ manager.last_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="description" class="form-label">وصف القسم</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ dept.description }}</textarea>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ التغييرات
                            </button>
                            <a href="{{ url_for('departments') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
