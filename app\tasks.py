"""
المهام الخلفية لنظام ALEMIS
Background Tasks for ALEMIS System
"""
import logging
from datetime import datetime, timedelta, date
from typing import List, Dict, Any
from celery import current_task
from .celery_app import celery
from .database import db_manager
from .models import User, LeaveRequest, LeaveBalance, AuditLog, Notification
from .notifications import notification_manager
from .reports import report_generator, ReportType, ReportFilter, ReportFormat
import os
import shutil
import subprocess


logger = logging.getLogger(__name__)


@celery.task(bind=True)
def send_email_notification(self, recipient_email: str, subject: str, message: str, 
                           html_message: str = None, attachments: List[str] = None):
    """إرسال إشعار بريد إلكتروني"""
    try:
        result = notification_manager.channels['email'].send(
            recipient=recipient_email,
            title=subject,
            message=message,
            html_message=html_message,
            attachments=attachments or []
        )
        
        if result:
            logger.info(f"تم إرسال البريد الإلكتروني بنجاح إلى {recipient_email}")
            return {'status': 'success', 'recipient': recipient_email}
        else:
            logger.error(f"فشل في إرسال البريد الإلكتروني إلى {recipient_email}")
            return {'status': 'failed', 'recipient': recipient_email}
            
    except Exception as e:
        logger.error(f"خطأ في إرسال البريد الإلكتروني: {e}")
        self.retry(countdown=60, max_retries=3)


@celery.task(bind=True)
def send_sms_notification(self, phone_number: str, message: str):
    """إرسال رسالة نصية"""
    try:
        result = notification_manager.channels['sms'].send(
            recipient=phone_number,
            title="ALEMIS",
            message=message
        )
        
        if result:
            logger.info(f"تم إرسال الرسالة النصية بنجاح إلى {phone_number}")
            return {'status': 'success', 'recipient': phone_number}
        else:
            logger.error(f"فشل في إرسال الرسالة النصية إلى {phone_number}")
            return {'status': 'failed', 'recipient': phone_number}
            
    except Exception as e:
        logger.error(f"خطأ في إرسال الرسالة النصية: {e}")
        self.retry(countdown=60, max_retries=3)


@celery.task(bind=True)
def generate_report_task(self, report_type: str, filters: Dict[str, Any], 
                        format: str = 'pdf', user_id: int = None):
    """إنتاج تقرير في الخلفية"""
    try:
        # تحديث حالة المهمة
        self.update_state(state='PROGRESS', meta={'progress': 10, 'status': 'بدء إنتاج التقرير'})
        
        # تحويل المرشحات
        from .reports import ReportFilter, ReportType, ReportFormat
        
        report_filter = ReportFilter(
            start_date=datetime.fromisoformat(filters['start_date']).date() if filters.get('start_date') else None,
            end_date=datetime.fromisoformat(filters['end_date']).date() if filters.get('end_date') else None,
            department_id=filters.get('department_id'),
            user_id=filters.get('user_id'),
            leave_type_id=filters.get('leave_type_id'),
            status=filters.get('status'),
            year=filters.get('year'),
            month=filters.get('month')
        )
        
        self.update_state(state='PROGRESS', meta={'progress': 30, 'status': 'جمع البيانات'})
        
        # إنتاج التقرير
        report_data = report_generator.generate(
            report_type=ReportType(report_type),
            filters=report_filter,
            format=ReportFormat(format)
        )
        
        self.update_state(state='PROGRESS', meta={'progress': 80, 'status': 'حفظ التقرير'})
        
        # حفظ التقرير
        filename = f"report_{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{format}"
        filepath = os.path.join('uploads', 'reports', filename)
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'wb') as f:
            f.write(report_data)
        
        self.update_state(state='PROGRESS', meta={'progress': 100, 'status': 'تم إنتاج التقرير'})
        
        # إرسال إشعار للمستخدم
        if user_id:
            with db_manager.session_scope() as session:
                user = session.query(User).filter(User.id == user_id).first()
                if user:
                    notification = Notification(
                        user_id=user_id,
                        title="تم إنتاج التقرير",
                        message=f"تم إنتاج التقرير بنجاح. يمكنك تحميله من الرابط المرفق.",
                        type="success",
                        action_url=f"/download/report/{filename}"
                    )
                    session.add(notification)
        
        return {
            'status': 'success',
            'filename': filename,
            'filepath': filepath,
            'size': os.path.getsize(filepath)
        }
        
    except Exception as e:
        logger.error(f"خطأ في إنتاج التقرير: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise


@celery.task
def cleanup_old_data():
    """تنظيف البيانات القديمة"""
    try:
        with db_manager.session_scope() as session:
            # حذف السجلات القديمة (أكثر من سنة)
            old_date = datetime.now() - timedelta(days=365)
            
            # حذف سجلات التدقيق القديمة
            old_audit_logs = session.query(AuditLog).filter(
                AuditLog.created_at < old_date
            ).count()
            
            session.query(AuditLog).filter(
                AuditLog.created_at < old_date
            ).delete()
            
            # حذف الإشعارات القديمة المقروءة
            old_notifications = session.query(Notification).filter(
                Notification.created_at < old_date,
                Notification.is_read == True
            ).count()
            
            session.query(Notification).filter(
                Notification.created_at < old_date,
                Notification.is_read == True
            ).delete()
            
            session.commit()
            
            logger.info(f"تم حذف {old_audit_logs} سجل تدقيق قديم و {old_notifications} إشعار قديم")
            
            return {
                'status': 'success',
                'deleted_audit_logs': old_audit_logs,
                'deleted_notifications': old_notifications
            }
            
    except Exception as e:
        logger.error(f"خطأ في تنظيف البيانات القديمة: {e}")
        raise


@celery.task
def send_weekly_reports():
    """إرسال التقارير الأسبوعية"""
    try:
        with db_manager.session_scope() as session:
            # الحصول على المدراء
            managers = session.query(User).filter(
                User.role.in_(['admin', 'hr', 'manager', 'gm']),
                User.is_active == True
            ).all()
            
            # إنتاج التقرير الأسبوعي
            end_date = date.today()
            start_date = end_date - timedelta(days=7)
            
            for manager in managers:
                # إنتاج التقرير
                generate_report_task.delay(
                    report_type='weekly_summary',
                    filters={
                        'start_date': start_date.isoformat(),
                        'end_date': end_date.isoformat()
                    },
                    format='pdf',
                    user_id=manager.id
                )
            
            logger.info(f"تم إرسال التقارير الأسبوعية لـ {len(managers)} مدير")
            
            return {
                'status': 'success',
                'managers_count': len(managers)
            }
            
    except Exception as e:
        logger.error(f"خطأ في إرسال التقارير الأسبوعية: {e}")
        raise


@celery.task
def update_leave_balances():
    """تحديث أرصدة الإجازات الشهرية"""
    try:
        with db_manager.session_scope() as session:
            current_year = datetime.now().year
            
            # الحصول على جميع المستخدمين النشطين
            users = session.query(User).filter(User.is_active == True).all()
            
            updated_count = 0
            
            for user in users:
                # تحديث رصيد الإجازة الاعتيادية
                annual_balance = session.query(LeaveBalance).filter(
                    LeaveBalance.user_id == user.id,
                    LeaveBalance.year == current_year,
                    LeaveBalance.leave_type_id == 1  # إجازة اعتيادية
                ).first()
                
                if annual_balance:
                    # إضافة 2.5 يوم شهرياً (30 يوم سنوياً / 12 شهر)
                    annual_balance.allocated_days += 2.5
                    updated_count += 1
            
            session.commit()
            
            logger.info(f"تم تحديث أرصدة الإجازات لـ {updated_count} موظف")
            
            return {
                'status': 'success',
                'updated_users': updated_count
            }
            
    except Exception as e:
        logger.error(f"خطأ في تحديث أرصدة الإجازات: {e}")
        raise


@celery.task
def check_pending_requests():
    """فحص الطلبات المعلقة وإرسال تذكيرات"""
    try:
        with db_manager.session_scope() as session:
            # الحصول على الطلبات المعلقة لأكثر من 3 أيام
            three_days_ago = datetime.now() - timedelta(days=3)
            
            pending_requests = session.query(LeaveRequest).filter(
                LeaveRequest.status.in_(['pending', 'submitted']),
                LeaveRequest.created_at < three_days_ago
            ).all()
            
            reminder_count = 0
            
            for request in pending_requests:
                # إرسال تذكير للمدراء
                managers = session.query(User).filter(
                    User.role.in_(['manager', 'hr', 'gm']),
                    User.is_active == True
                ).all()
                
                for manager in managers:
                    notification = Notification(
                        user_id=manager.id,
                        title="تذكير: طلب إجازة معلق",
                        message=f"طلب إجازة من {request.user.full_name} معلق منذ أكثر من 3 أيام",
                        type="warning",
                        action_url=f"/leave_requests/{request.id}"
                    )
                    session.add(notification)
                    reminder_count += 1
            
            session.commit()
            
            logger.info(f"تم إرسال {reminder_count} تذكير للطلبات المعلقة")
            
            return {
                'status': 'success',
                'pending_requests': len(pending_requests),
                'reminders_sent': reminder_count
            }
            
    except Exception as e:
        logger.error(f"خطأ في فحص الطلبات المعلقة: {e}")
        raise


@celery.task
def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        backup_dir = 'backups'
        os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"alemis_backup_{timestamp}.sql"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # إنشاء النسخة الاحتياطية (مثال لـ PostgreSQL)
        database_url = os.environ.get('DATABASE_URL', '')
        
        if 'postgresql' in database_url:
            # استخراج معلومات الاتصال
            # postgresql://user:password@host:port/database
            import re
            match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', database_url)
            if match:
                user, password, host, port, database = match.groups()
                
                # تشغيل pg_dump
                env = os.environ.copy()
                env['PGPASSWORD'] = password
                
                cmd = [
                    'pg_dump',
                    '-h', host,
                    '-p', port,
                    '-U', user,
                    '-d', database,
                    '-f', backup_path
                ]
                
                result = subprocess.run(cmd, env=env, capture_output=True, text=True)
                
                if result.returncode == 0:
                    # ضغط النسخة الاحتياطية
                    compressed_path = f"{backup_path}.gz"
                    with open(backup_path, 'rb') as f_in:
                        import gzip
                        with gzip.open(compressed_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    # حذف النسخة غير المضغوطة
                    os.remove(backup_path)
                    
                    # حذف النسخ القديمة (الاحتفاظ بآخر 7 نسخ)
                    cleanup_old_backups(backup_dir, keep_count=7)
                    
                    logger.info(f"تم إنشاء النسخة الاحتياطية: {compressed_path}")
                    
                    return {
                        'status': 'success',
                        'backup_file': compressed_path,
                        'size': os.path.getsize(compressed_path)
                    }
                else:
                    logger.error(f"فشل في إنشاء النسخة الاحتياطية: {result.stderr}")
                    raise Exception(result.stderr)
        
        else:
            # نسخ ملف SQLite
            db_path = database_url.replace('sqlite:///', '')
            if os.path.exists(db_path):
                shutil.copy2(db_path, backup_path)
                
                # ضغط النسخة الاحتياطية
                compressed_path = f"{backup_path}.gz"
                with open(backup_path, 'rb') as f_in:
                    import gzip
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                os.remove(backup_path)
                cleanup_old_backups(backup_dir, keep_count=7)
                
                logger.info(f"تم إنشاء النسخة الاحتياطية: {compressed_path}")
                
                return {
                    'status': 'success',
                    'backup_file': compressed_path,
                    'size': os.path.getsize(compressed_path)
                }
            else:
                raise Exception("ملف قاعدة البيانات غير موجود")
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        raise


def cleanup_old_backups(backup_dir: str, keep_count: int = 7):
    """حذف النسخ الاحتياطية القديمة"""
    try:
        backup_files = []
        for filename in os.listdir(backup_dir):
            if filename.startswith('alemis_backup_') and filename.endswith('.gz'):
                filepath = os.path.join(backup_dir, filename)
                backup_files.append((filepath, os.path.getmtime(filepath)))
        
        # ترتيب حسب التاريخ (الأحدث أولاً)
        backup_files.sort(key=lambda x: x[1], reverse=True)
        
        # حذف النسخ الزائدة
        for filepath, _ in backup_files[keep_count:]:
            os.remove(filepath)
            logger.info(f"تم حذف النسخة الاحتياطية القديمة: {filepath}")
            
    except Exception as e:
        logger.error(f"خطأ في حذف النسخ الاحتياطية القديمة: {e}")
