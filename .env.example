# إعدادات التطبيق الأساسية
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///alemis.db
DATABASE_POOL_SIZE=10
DATABASE_POOL_TIMEOUT=20
DATABASE_POOL_RECYCLE=3600

# إعدادات الأمان
SECURITY_PASSWORD_SALT=your-password-salt-here
JWT_SECRET_KEY=your-jwt-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here

# إعدادات البريد الإلكتروني
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USE_SSL=False
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_DEFAULT_SENDER=<EMAIL>

# إعدادات Redis
REDIS_URL=redis://localhost:6379/0
CACHE_REDIS_URL=redis://localhost:6379/1
RATELIMIT_STORAGE_URL=redis://localhost:6379/2
CELERY_BROKER_URL=redis://localhost:6379/3
CELERY_RESULT_BACKEND=redis://localhost:6379/4

# إعدادات التسجيل
LOG_LEVEL=INFO
LOG_FILE=logs/alemis.log
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5

# إعدادات المراقبة
SENTRY_DSN=your-sentry-dsn-here
ANALYTICS_ENABLED=True

# إعدادات الرسائل النصية
SMS_API_URL=https://api.sms-provider.com/send
SMS_API_KEY=your-sms-api-key
SMS_SENDER_NAME=ALEMIS

# إعدادات الإشعارات المدفوعة
FIREBASE_KEY=your-firebase-key-here

# إعدادات الأمان المتقدمة
FORCE_HTTPS=False
ENABLE_2FA=True
ENABLE_EMAIL_NOTIFICATIONS=True
ENABLE_SMS_NOTIFICATIONS=False
ENABLE_PUSH_NOTIFICATIONS=True
ENABLE_AUDIT_LOG=True

# إعدادات الأعمال
DEFAULT_ANNUAL_LEAVE_DAYS=30
DEFAULT_SICK_LEAVE_DAYS=15
MAX_CONSECUTIVE_LEAVE_DAYS=14
MIN_ADVANCE_NOTICE_DAYS=3

# إعدادات التحديد
RATELIMIT_DEFAULT=100 per hour

# إعدادات التطبيق
APP_VERSION=2.0.0
API_TITLE=ALEMIS API
API_VERSION=v1
