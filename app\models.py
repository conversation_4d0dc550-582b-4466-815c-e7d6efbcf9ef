"""
نماذج قاعدة البيانات المحسنة
Enhanced Database Models
"""
from datetime import datetime, date, timezone
from typing import Optional, List, Dict, Any
import enum

# دالة مساعدة للحصول على الوقت الحالي بـ UTC
def utcnow():
    """الحصول على الوقت الحالي بـ UTC"""
    return datetime.now(timezone.utc)

try:
    from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Boolean, ForeignKey, JSON, Enum, Float
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import relationship, validates
    from sqlalchemy.ext.hybrid import hybrid_property
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    # إنشاء بدائل بسيطة إذا لم تكن SQLAlchemy متاحة
    SQLALCHEMY_AVAILABLE = False

    class Column:
        def __init__(self, *args, **kwargs):
            pass

    class declarative_base:
        def __init__(self):
            pass

    Integer = String = Text = DateTime = Date = Boolean = ForeignKey = JSON = Enum = Float = None

try:
    from werkzeug.security import generate_password_hash, check_password_hash
    WERKZEUG_AVAILABLE = True
except ImportError:
    WERKZEUG_AVAILABLE = False

    def generate_password_hash(password):
        return password  # بديل بسيط

    def check_password_hash(hash, password):
        return hash == password  # بديل بسيط

if SQLALCHEMY_AVAILABLE:
    Base = declarative_base()
else:
    class Base:
        pass


class UserRole(enum.Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"
    HR = "hr"
    MANAGER = "manager"
    GM = "gm"
    EMPLOYEE = "employee"


class RequestStatus(enum.Enum):
    """حالات الطلبات"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


class LeaveStatus(enum.Enum):
    """حالات الإجازات"""
    DRAFT = "draft"
    SUBMITTED = "submitted"
    MANAGER_APPROVED = "manager_approved"
    HR_APPROVED = "hr_approved"
    GM_APPROVED = "gm_approved"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


class User(Base):
    """نموذج المستخدم المحسن"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    role = Column(Enum(UserRole), nullable=False, default=UserRole.EMPLOYEE)
    department_id = Column(Integer, ForeignKey('departments.id'), nullable=True)
    
    # Security fields
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    two_factor_enabled = Column(Boolean, default=False, nullable=False)
    two_factor_secret = Column(String(32), nullable=True)
    backup_codes = Column(JSON, nullable=True)
    
    # Profile fields
    phone = Column(String(20), nullable=True)
    address = Column(Text, nullable=True)
    birth_date = Column(Date, nullable=True)
    hire_date = Column(Date, nullable=True)
    employee_id = Column(String(20), unique=True, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=utcnow, nullable=False)
    updated_at = Column(DateTime, default=utcnow, onupdate=utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)
    last_activity = Column(DateTime, nullable=True)
    
    # Password reset
    reset_token = Column(String(100), nullable=True)
    reset_token_expires = Column(DateTime, nullable=True)
    
    # Relationships
    department = relationship("Department", back_populates="users")
    leave_requests = relationship("LeaveRequest", back_populates="user")
    leave_balances = relationship("LeaveBalance", back_populates="user")
    audit_logs = relationship("AuditLog", back_populates="user")
    
    @hybrid_property
    def full_name(self):
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}"
    
    def set_password(self, password: str):
        """تعيين كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'role': self.role.value,
            'department_id': self.department_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class Department(Base):
    """نموذج القسم"""
    __tablename__ = 'departments'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    manager_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=utcnow, nullable=False)
    updated_at = Column(DateTime, default=utcnow, onupdate=utcnow, nullable=False)
    
    # Relationships
    users = relationship("User", back_populates="department")
    manager = relationship("User", foreign_keys=[manager_id])


class LeaveType(Base):
    """نموذج نوع الإجازة"""
    __tablename__ = 'leave_types'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    default_days = Column(Integer, default=0, nullable=False)
    max_days_per_request = Column(Integer, nullable=True)
    requires_approval = Column(Boolean, default=True, nullable=False)
    requires_document = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Business rules
    min_advance_notice = Column(Integer, default=1, nullable=False)  # أيام
    max_consecutive_days = Column(Integer, nullable=True)
    can_be_negative = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=utcnow, nullable=False)
    updated_at = Column(DateTime, default=utcnow, onupdate=utcnow, nullable=False)
    
    # Relationships
    leave_requests = relationship("LeaveRequest", back_populates="leave_type")
    leave_balances = relationship("LeaveBalance", back_populates="leave_type")


class LeaveRequest(Base):
    """نموذج طلب الإجازة المحسن"""
    __tablename__ = 'leave_requests'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    leave_type_id = Column(Integer, ForeignKey('leave_types.id'), nullable=False)
    
    # Request details
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    days_requested = Column(Integer, nullable=False)
    reason = Column(Text, nullable=True)
    emergency_contact = Column(String(100), nullable=True)
    
    # Status and approvals
    status = Column(Enum(LeaveStatus), default=LeaveStatus.DRAFT, nullable=False)
    manager_approval = Column(Boolean, nullable=True)
    manager_comment = Column(Text, nullable=True)
    manager_approved_at = Column(DateTime, nullable=True)
    manager_approved_by = Column(Integer, ForeignKey('users.id'), nullable=True)
    
    hr_approval = Column(Boolean, nullable=True)
    hr_comment = Column(Text, nullable=True)
    hr_approved_at = Column(DateTime, nullable=True)
    hr_approved_by = Column(Integer, ForeignKey('users.id'), nullable=True)
    
    gm_approval = Column(Boolean, nullable=True)
    gm_comment = Column(Text, nullable=True)
    gm_approved_at = Column(DateTime, nullable=True)
    gm_approved_by = Column(Integer, ForeignKey('users.id'), nullable=True)
    
    # Documents
    document_path = Column(String(255), nullable=True)
    additional_documents = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=utcnow, nullable=False)
    updated_at = Column(DateTime, default=utcnow, onupdate=utcnow, nullable=False)
    submitted_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="leave_requests", foreign_keys=[user_id])
    leave_type = relationship("LeaveType", back_populates="leave_requests")
    manager_approver = relationship("User", foreign_keys=[manager_approved_by])
    hr_approver = relationship("User", foreign_keys=[hr_approved_by])
    gm_approver = relationship("User", foreign_keys=[gm_approved_by])
    
    @validates('start_date', 'end_date')
    def validate_dates(self, key, value):
        """التحقق من صحة التواريخ"""
        if key == 'end_date' and hasattr(self, 'start_date') and self.start_date:
            if value < self.start_date:
                raise ValueError("تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية")
        return value
    
    @hybrid_property
    def duration_days(self):
        """مدة الإجازة بالأيام"""
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days + 1
        return 0
    
    def to_dict(self) -> Dict[str, Any]:
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'leave_type_id': self.leave_type_id,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'days_requested': self.days_requested,
            'reason': self.reason,
            'status': self.status.value,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class LeaveBalance(Base):
    """نموذج رصيد الإجازات"""
    __tablename__ = 'leave_balances'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    leave_type_id = Column(Integer, ForeignKey('leave_types.id'), nullable=False)
    year = Column(Integer, nullable=False)
    
    # Balance details
    allocated_days = Column(Float, default=0, nullable=False)
    used_days = Column(Float, default=0, nullable=False)
    pending_days = Column(Float, default=0, nullable=False)
    carried_forward = Column(Float, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=utcnow, nullable=False)
    updated_at = Column(DateTime, default=utcnow, onupdate=utcnow, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="leave_balances")
    leave_type = relationship("LeaveType", back_populates="leave_balances")
    
    @hybrid_property
    def available_days(self):
        """الأيام المتاحة"""
        return self.allocated_days + self.carried_forward - self.used_days - self.pending_days


class AuditLog(Base):
    """نموذج سجل التدقيق"""
    __tablename__ = 'audit_logs'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(Integer, nullable=True)
    old_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(255), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=utcnow, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")


class Notification(Base):
    """نموذج الإشعارات"""
    __tablename__ = 'notifications'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    type = Column(String(50), nullable=False)  # info, warning, error, success
    is_read = Column(Boolean, default=False, nullable=False)
    action_url = Column(String(255), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=utcnow, nullable=False)
    read_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User")


class SystemSetting(Base):
    """نموذج إعدادات النظام"""
    __tablename__ = 'system_settings'
    
    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    data_type = Column(String(20), default='string', nullable=False)  # string, integer, boolean, json
    is_public = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=utcnow, nullable=False)
    updated_at = Column(DateTime, default=utcnow, onupdate=utcnow, nullable=False)
