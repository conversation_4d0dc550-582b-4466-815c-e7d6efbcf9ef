#!/usr/bin/env python
"""
خادم تشغيل نظام ALEMIS
ALEMIS System Server Runner
"""
import os
import sys
import signal
import logging
from datetime import datetime
from app import create_app
from app.monitoring import structured_logger


def setup_logging():
    """إعداد نظام التسجيل"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/server.log', encoding='utf-8')
        ]
    )


def signal_handler(signum, frame):
    """معالج إشارات النظام"""
    logger = logging.getLogger(__name__)
    logger.info(f"تم استلام إشارة {signum}. إيقاف الخادم...")
    sys.exit(0)


def check_environment():
    """فحص متطلبات البيئة"""
    logger = logging.getLogger(__name__)
    
    # فحص Python version
    if sys.version_info < (3, 9):
        logger.error("يتطلب Python 3.9 أو أحدث")
        sys.exit(1)
    
    # فحص متغيرات البيئة المطلوبة
    required_env_vars = ['SECRET_KEY']
    missing_vars = []
    
    for var in required_env_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(f"متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        logger.info("سيتم استخدام القيم الافتراضية")
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['logs', 'uploads', 'backups', 'static/uploads']
    for directory in required_dirs:
        os.makedirs(directory, exist_ok=True)
    
    logger.info("✅ تم فحص البيئة بنجاح")


def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║     🏥 ALEMIS - نظام إدارة إجازات الموظفين في المختبرات      ║
    ║                                                              ║
    ║     Enhanced Laboratory Employee Leave Management System     ║
    ║                                                              ║
    ║     Version: 2.0.0                                          ║
    ║     Company: شركة العميس الطبية                              ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_system_info(app):
    """طباعة معلومات النظام"""
    logger = logging.getLogger(__name__)
    
    print("\n" + "="*60)
    print("📊 معلومات النظام")
    print("="*60)
    
    # معلومات التطبيق
    print(f"🚀 وضع التشغيل: {app.config.get('FLASK_ENV', 'production')}")
    print(f"🔧 وضع التطوير: {'مفعل' if app.debug else 'معطل'}")
    print(f"🗄️ قاعدة البيانات: {app.config.get('DATABASE_URL', 'sqlite:///alemis.db')}")
    print(f"📧 خادم البريد: {app.config.get('MAIL_SERVER', 'غير محدد')}")
    print(f"🔄 Redis: {app.config.get('REDIS_URL', 'غير محدد')}")
    
    # معلومات الأمان
    print(f"🔒 HTTPS إجباري: {'مفعل' if app.config.get('FORCE_HTTPS') else 'معطل'}")
    print(f"🔐 المصادقة الثنائية: {'مفعل' if app.config.get('ENABLE_2FA') else 'معطل'}")
    
    # معلومات الميزات
    print(f"📧 إشعارات البريد: {'مفعل' if app.config.get('ENABLE_EMAIL_NOTIFICATIONS') else 'معطل'}")
    print(f"📱 إشعارات SMS: {'مفعل' if app.config.get('ENABLE_SMS_NOTIFICATIONS') else 'معطل'}")
    print(f"🔔 إشعارات Push: {'مفعل' if app.config.get('ENABLE_PUSH_NOTIFICATIONS') else 'معطل'}")
    print(f"📋 سجل التدقيق: {'مفعل' if app.config.get('ENABLE_AUDIT_LOG') else 'معطل'}")
    
    print("="*60)
    
    # روابط مهمة
    print("\n🔗 الروابط المهمة:")
    print(f"   📱 التطبيق الرئيسي: http://localhost:5000")
    print(f"   📚 توثيق API: http://localhost:5000/api/v1/docs")
    print(f"   ❤️ فحص الصحة: http://localhost:5000/health")
    
    if app.config.get('FLASK_ENV') == 'development':
        print(f"   📊 Grafana: http://localhost:3000")
        print(f"   🔍 Prometheus: http://localhost:9090")
        print(f"   📈 Kibana: http://localhost:5601")
    
    print("\n👥 الحسابات الافتراضية:")
    print("   🔑 المدير: admin / admin123")
    print("   👤 الموارد البشرية: hr / admin123")
    print("   🏢 المدير العام: gm / admin123")
    print("   🧪 مدير المختبر: manager / admin123")
    
    print("\n" + "="*60)


def run_development_server(app):
    """تشغيل خادم التطوير"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🚀 بدء تشغيل خادم التطوير...")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=True,
            use_debugger=True,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        logger.error(f"خطأ في تشغيل خادم التطوير: {e}")
        sys.exit(1)


def run_production_server(app):
    """تشغيل خادم الإنتاج"""
    logger = logging.getLogger(__name__)
    
    try:
        import gunicorn.app.wsgiapp as wsgi
        
        logger.info("🚀 بدء تشغيل خادم الإنتاج...")
        
        # إعداد Gunicorn
        sys.argv = [
            'gunicorn',
            '--config', 'gunicorn.conf.py',
            'app:app'
        ]
        
        wsgi.run()
        
    except ImportError:
        logger.error("Gunicorn غير متاح. استخدم خادم التطوير بدلاً من ذلك")
        run_development_server(app)
    except Exception as e:
        logger.error(f"خطأ في تشغيل خادم الإنتاج: {e}")
        sys.exit(1)


def main():
    """الدالة الرئيسية"""
    # إعداد معالجات الإشارات
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # إعداد التسجيل
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # طباعة الشعار
        print_banner()
        
        # فحص البيئة
        check_environment()
        
        # إنشاء التطبيق
        logger.info("🔄 إنشاء التطبيق...")
        app = create_app()
        
        # طباعة معلومات النظام
        print_system_info(app)
        
        # تحديد وضع التشغيل
        env = os.environ.get('FLASK_ENV', 'production')
        
        if env == 'development':
            logger.info("🔧 تشغيل في وضع التطوير")
            run_development_server(app)
        else:
            logger.info("🏭 تشغيل في وضع الإنتاج")
            run_production_server(app)
            
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
