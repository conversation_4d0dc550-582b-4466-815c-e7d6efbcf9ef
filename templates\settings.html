{% extends "base.html" %}

{% block title %}ALEMIS - الإعدادات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">إعدادات النظام</h2>
        
        <div class="row">
            <div class="col-md-3">
                <div class="list-group mb-4">
                    <a href="#general" class="list-group-item list-group-item-action active" data-bs-toggle="list">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات عامة
                    </a>
                    <a href="#leave-types" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="fas fa-list-alt me-2"></i>
                        أنواع الإجازات
                    </a>
                    <a href="#email" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="fas fa-envelope me-2"></i>
                        إعدادات البريد الإلكتروني
                    </a>
                    <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="fas fa-database me-2"></i>
                        النسخ الاحتياطي
                    </a>
                    <a href="#system" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="fas fa-server me-2"></i>
                        معلومات النظام
                    </a>
                </div>
            </div>
            <div class="col-md-9">
                <div class="tab-content">
                    <!-- General Settings -->
                    <div class="tab-pane fade show active" id="general">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-cog me-1"></i>
                                إعدادات عامة
                            </div>
                            <div class="card-body">
                                <form>
                                    <div class="mb-3">
                                        <label for="system_name" class="form-label">اسم النظام</label>
                                        <input type="text" class="form-control" id="system_name" value="ALEMIS - نظام إدارة إجازات الموظفين في المختبرات">
                                    </div>
                                    <div class="mb-3">
                                        <label for="company_name" class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" id="company_name" value="المختبرات المتقدمة">
                                    </div>
                                    <div class="mb-3">
                                        <label for="logo" class="form-label">شعار الشركة</label>
                                        <input type="file" class="form-control" id="logo">
                                    </div>
                                    <div class="mb-3">
                                        <label for="language" class="form-label">اللغة الافتراضية</label>
                                        <select class="form-select" id="language">
                                            <option value="ar" selected>العربية</option>
                                            <option value="en">الإنجليزية</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                        <select class="form-select" id="timezone">
                                            <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai">دبي (GMT+4)</option>
                                            <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                            <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                                        </select>
                                    </div>
                                    <button type="button" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Leave Types -->
                    <div class="tab-pane fade" id="leave-types">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-list-alt me-1"></i>
                                    أنواع الإجازات
                                </div>
                                <button type="button" class="btn btn-light btn-sm">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة نوع جديد
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>اسم الإجازة</th>
                                                <th>الوصف</th>
                                                <th>الأيام الافتراضية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>إجازة اعتيادية</td>
                                                <td>الإجازة السنوية المدفوعة</td>
                                                <td>30</td>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>إجازة مرضية</td>
                                                <td>إجازة للظروف الصحية</td>
                                                <td>15</td>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>إجازة طارئة</td>
                                                <td>إجازة للظروف الطارئة</td>
                                                <td>5</td>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>إجازة بدون راتب</td>
                                                <td>إجازة غير مدفوعة</td>
                                                <td>0</td>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>إجازة أمومة</td>
                                                <td>إجازة للأمهات الجدد</td>
                                                <td>70</td>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>إجازة حج</td>
                                                <td>إجازة لأداء فريضة الحج</td>
                                                <td>15</td>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>إجازة تغطية</td>
                                                <td>إجازة لتغطية موظف آخر</td>
                                                <td>0</td>
                                                <td>
                                                    <button type="button" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Email Settings -->
                    <div class="tab-pane fade" id="email">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-envelope me-1"></i>
                                إعدادات البريد الإلكتروني
                            </div>
                            <div class="card-body">
                                <form>
                                    <div class="mb-3">
                                        <label for="smtp_server" class="form-label">خادم SMTP</label>
                                        <input type="text" class="form-control" id="smtp_server" value="smtp.example.com">
                                    </div>
                                    <div class="mb-3">
                                        <label for="smtp_port" class="form-label">منفذ SMTP</label>
                                        <input type="number" class="form-control" id="smtp_port" value="587">
                                    </div>
                                    <div class="mb-3">
                                        <label for="smtp_username" class="form-label">اسم المستخدم</label>
                                        <input type="text" class="form-control" id="smtp_username" value="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="smtp_password" class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" id="smtp_password" value="********">
                                    </div>
                                    <div class="mb-3">
                                        <label for="from_email" class="form-label">البريد الإلكتروني المرسل</label>
                                        <input type="email" class="form-control" id="from_email" value="<EMAIL>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="from_name" class="form-label">اسم المرسل</label>
                                        <input type="text" class="form-control" id="from_name" value="نظام ALEMIS">
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="use_ssl" checked>
                                        <label class="form-check-label" for="use_ssl">استخدام SSL/TLS</label>
                                    </div>
                                    <button type="button" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ الإعدادات
                                    </button>
                                    <button type="button" class="btn btn-secondary">
                                        <i class="fas fa-paper-plane me-1"></i>
                                        اختبار الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Backup Settings -->
                    <div class="tab-pane fade" id="backup">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-database me-1"></i>
                                النسخ الاحتياطي
                            </div>
                            <div class="card-body">
                                <div class="mb-4">
                                    <h5>النسخ الاحتياطي اليدوي</h5>
                                    <p>قم بإنشاء نسخة احتياطية من قاعدة البيانات يدوياً.</p>
                                    <button type="button" class="btn btn-primary">
                                        <i class="fas fa-download me-1"></i>
                                        إنشاء نسخة احتياطية
                                    </button>
                                </div>
                                <hr>
                                <div class="mb-4">
                                    <h5>النسخ الاحتياطي التلقائي</h5>
                                    <form>
                                        <div class="mb-3 form-check">
                                            <input type="checkbox" class="form-check-input" id="auto_backup" checked>
                                            <label class="form-check-label" for="auto_backup">تفعيل النسخ الاحتياطي التلقائي</label>
                                        </div>
                                        <div class="mb-3">
                                            <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                            <select class="form-select" id="backup_frequency">
                                                <option value="daily">يومي</option>
                                                <option value="weekly" selected>أسبوعي</option>
                                                <option value="monthly">شهري</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="backup_time" class="form-label">وقت النسخ الاحتياطي</label>
                                            <input type="time" class="form-control" id="backup_time" value="02:00">
                                        </div>
                                        <div class="mb-3">
                                            <label for="backup_retention" class="form-label">الاحتفاظ بالنسخ الاحتياطية (بالأيام)</label>
                                            <input type="number" class="form-control" id="backup_retention" value="30">
                                        </div>
                                        <button type="button" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ الإعدادات
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- System Information -->
                    <div class="tab-pane fade" id="system">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-server me-1"></i>
                                معلومات النظام
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table">
                                        <tbody>
                                            <tr>
                                                <th style="width: 200px;">إصدار النظام</th>
                                                <td>1.0.0</td>
                                            </tr>
                                            <tr>
                                                <th>تاريخ التثبيت</th>
                                                <td>2023-06-01</td>
                                            </tr>
                                            <tr>
                                                <th>آخر تحديث</th>
                                                <td>2023-06-15</td>
                                            </tr>
                                            <tr>
                                                <th>إصدار PHP</th>
                                                <td>8.1.0</td>
                                            </tr>
                                            <tr>
                                                <th>إصدار قاعدة البيانات</th>
                                                <td>SQLite 3.36.0</td>
                                            </tr>
                                            <tr>
                                                <th>حجم قاعدة البيانات</th>
                                                <td>2.5 MB</td>
                                            </tr>
                                            <tr>
                                                <th>عدد المستخدمين</th>
                                                <td>25</td>
                                            </tr>
                                            <tr>
                                                <th>عدد طلبات الإجازة</th>
                                                <td>150</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-primary">
                                        <i class="fas fa-sync me-1"></i>
                                        تحديث النظام
                                    </button>
                                    <button type="button" class="btn btn-secondary">
                                        <i class="fas fa-broom me-1"></i>
                                        تنظيف النظام
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
