{% extends "base.html" %}

{% block title %}ALEMIS - طلبات التغطية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4 title-with-red-line">طلبات التغطية</h2>
        <div class="red-line-animated"></div>
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-exchange-alt me-1"></i>
                    قائمة طلبات التغطية
                </div>
                <a href="{{ url_for('new_coverage') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    طلب تغطية جديد
                </a>
            </div>
            <div class="card-body">
                {% if coverage_requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                                <th>الموظف</th>
                                {% endif %}
                                <th>تاريخ التغطية</th>
                                <th>فترة الدوام</th>
                                <th>نوع التغطية</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                                <th>موافقة المدير</th>
                                <th>موافقة الموارد البشرية</th>
                                <th>تاريخ الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for coverage in coverage_requests %}
                            <tr>
                                {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                                <td>{{ coverage.first_name }} {{ coverage.last_name }}</td>
                                {% endif %}
                                <td>{{ coverage.coverage_date }}</td>
                                <td>
                                    {% if coverage.coverage_type == 'morning' %}
                                    دوام صباحي (8ص-4م)
                                    {% elif coverage.coverage_type == 'evening' %}
                                    دوام مسائي (4م-12ل)
                                    {% elif coverage.coverage_type == 'night' %}
                                    دوام ليلي (12ل-8ص)
                                    {% endif %}
                                </td>
                                <td>{{ coverage.coverage_reason }}</td>
                                <td>{{ coverage.description }}</td>
                                <td>
                                    {% if coverage.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif coverage.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif coverage.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if coverage.manager_approval == 1 %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif coverage.manager_approval == -1 %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% else %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if coverage.hr_approval == 1 %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif coverage.hr_approval == -1 %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% else %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% endif %}
                                </td>
                                <td>{{ coverage.created_at }}</td>
                                <td>
                                    {% if session.role == 'manager' and coverage.manager_approval == 0 %}
                                    <button type="button" class="btn btn-sm btn-success approve-coverage" data-id="{{ coverage.id }}" data-type="manager" data-action="approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-coverage" data-id="{{ coverage.id }}" data-type="manager" data-action="reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}

                                    {% if session.role == 'hr' and coverage.manager_approval == 1 and coverage.hr_approval == 0 %}
                                    <button type="button" class="btn btn-sm btn-success approve-coverage" data-id="{{ coverage.id }}" data-type="hr" data-action="approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-coverage" data-id="{{ coverage.id }}" data-type="hr" data-action="reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}

                                    {% if coverage.user_id == session.user_id and coverage.status == 'pending' %}
                                    <button type="button" class="btn btn-sm btn-danger cancel-coverage" data-id="{{ coverage.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد طلبات تغطية حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الموافقة على طلب التغطية
        const approveButtons = document.querySelectorAll('.approve-coverage');
        approveButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');

                if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
                    fetch('{{ url_for("approve_coverage") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            coverage_id: id,
                            approval_type: type,
                            action: 'approve'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });

        // رفض طلب التغطية
        const rejectButtons = document.querySelectorAll('.reject-coverage');
        rejectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');

                if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
                    fetch('{{ url_for("approve_coverage") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            coverage_id: id,
                            approval_type: type,
                            action: 'reject'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });

        // إلغاء طلب التغطية
        const cancelButtons = document.querySelectorAll('.cancel-coverage');
        cancelButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');

                if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                    fetch('{{ url_for("cancel_coverage") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            coverage_id: id
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });
    });
</script>
{% endblock %}
