<div class="request-card">
    <div class="request-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <i class="fas fa-calendar-plus me-2"></i>
        طلب تغطية
    </div>
    <div class="request-body">
        <div class="request-info">
            <div>
                <strong>رقم الطلب:</strong> #{{ request.id }}
            </div>
            <div>
                <span class="badge status-badge bg-{{ status_badge }}">{{ status_text }}</span>
            </div>
        </div>
        
        <div class="request-details">
            <div class="row">
                <div class="col-6">
                    <small class="text-muted">الموظف المطلوب تغطيته</small>
                    <div><strong>{{ request.first_name }} {{ request.last_name }}</strong></div>
                </div>
                <div class="col-6">
                    <small class="text-muted">تاريخ التغطية</small>
                    <div><strong>{{ request.coverage_date }}</strong></div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-6">
                    <small class="text-muted">نوع الشفت</small>
                    <div><strong>
                        {% if request.coverage_type == 'morning' %}
                            صباحي (8ص-4م)
                        {% elif request.coverage_type == 'evening' %}
                            مسائي (4م-12ل)
                        {% elif request.coverage_type == 'night' %}
                            ليلي (12ل-8ص)
                        {% elif request.coverage_type == 'split' %}
                            فترتين (9ص-12ظ، 4م-9م)
                        {% else %}
                            {{ request.coverage_type }}
                        {% endif %}
                    </strong></div>
                </div>
                <div class="col-6">
                    <small class="text-muted">تاريخ التقديم</small>
                    <div><strong>{{ request.created_at[:10] }}</strong></div>
                </div>
            </div>
            
            {% if request.coverage_reason %}
            <div class="mt-3">
                <small class="text-muted">سبب التغطية</small>
                <div>{{ request.coverage_reason }}</div>
            </div>
            {% endif %}

            {% if request.description %}
            <div class="mt-3">
                <small class="text-muted">الوصف</small>
                <div>{{ request.description }}</div>
            </div>
            {% endif %}

            {% if request.reason %}
            <div class="mt-3">
                <small class="text-muted">السبب</small>
                <div>{{ request.reason }}</div>
            </div>
            {% endif %}
            
            {% if request.manager_notes %}
            <div class="mt-3">
                <small class="text-muted">ملاحظات المدير</small>
                <div class="alert alert-info mb-0">{{ request.manager_notes }}</div>
            </div>
            {% endif %}
        </div>
        
        <div class="mt-3 d-flex justify-content-between align-items-center">
            <small class="text-muted">
                <i class="fas fa-clock me-1"></i>
                {{ request.created_at }}
            </small>
            {% if status == 'approved' %}
            <a href="{{ url_for('export_request_pdf', request_type='coverage', request_id=request.id) }}" 
               class="btn btn-sm btn-outline-success">
                <i class="fas fa-download me-1"></i>تصدير PDF
            </a>
            {% endif %}
        </div>
    </div>
</div>
