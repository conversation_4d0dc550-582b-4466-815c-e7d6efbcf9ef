"""
تكوين Celery للمهام الخلفية
Celery Configuration for Background Tasks
"""
import os
from celery import Celery
from celery.schedules import crontab
from datetime import timedelta


def make_celery(app):
    """إنشاء مثيل Celery مع Flask"""
    celery = Celery(
        app.import_name,
        backend=app.config.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/4'),
        broker=app.config.get('CELERY_BROKER_URL', 'redis://localhost:6379/3')
    )
    
    # تكوين Celery
    celery.conf.update(
        # إعدادات عامة
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='Asia/Riyadh',
        enable_utc=True,
        
        # إعدادات النتائج
        result_expires=3600,
        result_backend_transport_options={
            'master_name': 'mymaster',
            'visibility_timeout': 3600,
        },
        
        # إعدادات المهام
        task_routes={
            'app.tasks.send_email': {'queue': 'email'},
            'app.tasks.send_sms': {'queue': 'sms'},
            'app.tasks.generate_report': {'queue': 'reports'},
            'app.tasks.cleanup_old_data': {'queue': 'maintenance'},
        },
        
        # إعدادات العمال
        worker_prefetch_multiplier=1,
        task_acks_late=True,
        worker_max_tasks_per_child=1000,
        
        # المهام المجدولة
        beat_schedule={
            # تنظيف البيانات القديمة يومياً في الساعة 2:00 صباحاً
            'cleanup-old-data': {
                'task': 'app.tasks.cleanup_old_data',
                'schedule': crontab(hour=2, minute=0),
            },
            
            # إرسال تقارير أسبوعية كل يوم أحد في الساعة 9:00 صباحاً
            'weekly-reports': {
                'task': 'app.tasks.send_weekly_reports',
                'schedule': crontab(hour=9, minute=0, day_of_week=0),
            },
            
            # تحديث أرصدة الإجازات شهرياً في اليوم الأول من كل شهر
            'update-leave-balances': {
                'task': 'app.tasks.update_leave_balances',
                'schedule': crontab(hour=1, minute=0, day_of_month=1),
            },
            
            # فحص الطلبات المعلقة كل ساعة
            'check-pending-requests': {
                'task': 'app.tasks.check_pending_requests',
                'schedule': crontab(minute=0),
            },
            
            # نسخ احتياطي يومي في الساعة 3:00 صباحاً
            'daily-backup': {
                'task': 'app.tasks.create_backup',
                'schedule': crontab(hour=3, minute=0),
            },
        },
    )
    
    class ContextTask(celery.Task):
        """مهمة مع سياق Flask"""
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    return celery


# إنشاء مثيل Celery للاستخدام المباشر
celery = Celery('alemis')

# تكوين أساسي إذا لم يكن Flask متاحاً
celery.conf.update(
    broker_url=os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/3'),
    result_backend=os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/4'),
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Riyadh',
    enable_utc=True,
)
