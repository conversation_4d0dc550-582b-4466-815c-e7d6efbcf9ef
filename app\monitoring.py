"""
نظام المراقبة والتسجيل المتقدم
Advanced Monitoring and Logging System
"""
import os
import sys
import logging
import logging.handlers
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from functools import wraps
import structlog
from pythonjsonlogger import jsonlogger
from flask import request, g, current_app, session
import sentry_sdk
from sentry_sdk.integrations.flask import FlaskIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from .models import AuditLog
from .database import db_manager


class StructuredLogger:
    """نظام التسجيل المنظم"""
    
    def __init__(self, app=None):
        self.app = app
        self.logger = None
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة نظام التسجيل"""
        self.app = app
        
        # إعداد مجلد السجلات
        log_dir = os.path.dirname(app.config.get('LOG_FILE', 'logs/alemis.log'))
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # إعداد التسجيل المنظم
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer(ensure_ascii=False)
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # إعداد logger الرئيسي
        self.logger = structlog.get_logger("alemis")
        
        # إعداد معالجات التسجيل
        self._setup_handlers(app)
        
        # تسجيل معالجات Flask
        app.before_request(self._before_request)
        app.after_request(self._after_request)
        app.teardown_appcontext(self._teardown_logging)
    
    def _setup_handlers(self, app):
        """إعداد معالجات التسجيل"""
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
        
        # معالج الملف مع التدوير
        file_handler = logging.handlers.RotatingFileHandler(
            app.config.get('LOG_FILE', 'logs/alemis.log'),
            maxBytes=app.config.get('LOG_MAX_BYTES', 10485760),  # 10MB
            backupCount=app.config.get('LOG_BACKUP_COUNT', 5),
            encoding='utf-8'
        )
        
        # تنسيق JSON للملف
        json_formatter = jsonlogger.JsonFormatter(
            '%(asctime)s %(name)s %(levelname)s %(message)s',
            ensure_ascii=False
        )
        file_handler.setFormatter(json_formatter)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler(sys.stdout)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        
        # إضافة المعالجات
        root_logger.addHandler(file_handler)
        if app.debug:
            root_logger.addHandler(console_handler)
    
    def _before_request(self):
        """معالج ما قبل الطلب"""
        g.request_start_time = datetime.utcnow()
        g.request_id = self._generate_request_id()
        
        # تسجيل بداية الطلب
        self.logger.info(
            "request_started",
            request_id=g.request_id,
            method=request.method,
            path=request.path,
            remote_addr=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            user_id=session.get('user_id') if session else None
        )
    
    def _after_request(self, response):
        """معالج ما بعد الطلب"""
        if hasattr(g, 'request_start_time'):
            duration = (datetime.utcnow() - g.request_start_time).total_seconds()
            
            # تسجيل انتهاء الطلب
            self.logger.info(
                "request_completed",
                request_id=getattr(g, 'request_id', 'unknown'),
                method=request.method,
                path=request.path,
                status_code=response.status_code,
                duration=duration,
                content_length=response.content_length,
                user_id=session.get('user_id') if session else None
            )
        
        return response
    
    def _teardown_logging(self, exception):
        """تنظيف التسجيل"""
        if exception:
            self.logger.error(
                "request_exception",
                request_id=getattr(g, 'request_id', 'unknown'),
                exception=str(exception),
                traceback=traceback.format_exc()
            )
    
    def _generate_request_id(self) -> str:
        """إنتاج معرف فريد للطلب"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def log_user_action(self, action: str, details: Dict[str, Any] = None):
        """تسجيل إجراء المستخدم"""
        user_id = session.get('user_id') if session else None
        
        self.logger.info(
            "user_action",
            user_id=user_id,
            action=action,
            details=details or {},
            request_id=getattr(g, 'request_id', 'unknown'),
            ip_address=request.remote_addr if request else None
        )
    
    def log_security_event(self, event_type: str, details: Dict[str, Any] = None):
        """تسجيل حدث أمني"""
        self.logger.warning(
            "security_event",
            event_type=event_type,
            details=details or {},
            request_id=getattr(g, 'request_id', 'unknown'),
            ip_address=request.remote_addr if request else None,
            user_agent=request.headers.get('User-Agent') if request else None
        )
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """تسجيل خطأ"""
        self.logger.error(
            "application_error",
            error=str(error),
            error_type=type(error).__name__,
            traceback=traceback.format_exc(),
            context=context or {},
            request_id=getattr(g, 'request_id', 'unknown')
        )


class AuditLogger:
    """نظام تسجيل التدقيق"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def log_action(self, user_id: Optional[int], action: str, resource_type: str,
                   resource_id: Optional[int] = None, old_values: Dict[str, Any] = None,
                   new_values: Dict[str, Any] = None, ip_address: str = None,
                   user_agent: str = None):
        """تسجيل إجراء في سجل التدقيق"""
        try:
            with self.db_manager.session_scope() as session:
                audit_log = AuditLog(
                    user_id=user_id,
                    action=action,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    old_values=old_values,
                    new_values=new_values,
                    ip_address=ip_address or (request.remote_addr if request else None),
                    user_agent=user_agent or (request.headers.get('User-Agent') if request else None),
                    created_at=datetime.utcnow()
                )
                session.add(audit_log)
                session.flush()
                
        except Exception as e:
            logging.error(f"خطأ في تسجيل التدقيق: {e}")
    
    def get_user_actions(self, user_id: int, limit: int = 100) -> List[AuditLog]:
        """الحصول على إجراءات المستخدم"""
        with self.db_manager.session_scope() as session:
            return session.query(AuditLog).filter(
                AuditLog.user_id == user_id
            ).order_by(AuditLog.created_at.desc()).limit(limit).all()
    
    def get_resource_history(self, resource_type: str, resource_id: int) -> List[AuditLog]:
        """الحصول على تاريخ المورد"""
        with self.db_manager.session_scope() as session:
            return session.query(AuditLog).filter(
                AuditLog.resource_type == resource_type,
                AuditLog.resource_id == resource_id
            ).order_by(AuditLog.created_at.desc()).all()


class HealthChecker:
    """فاحص صحة النظام"""
    
    def __init__(self, app=None):
        self.app = app
        self.checks = {}
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة فاحص الصحة"""
        self.app = app
        
        # تسجيل الفحوصات الافتراضية
        self.register_check('database', self._check_database)
        self.register_check('redis', self._check_redis)
        self.register_check('disk_space', self._check_disk_space)
        self.register_check('memory', self._check_memory)
    
    def register_check(self, name: str, check_func):
        """تسجيل فحص صحة"""
        self.checks[name] = check_func
    
    def run_checks(self) -> Dict[str, Any]:
        """تشغيل جميع الفحوصات"""
        results = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'checks': {}
        }
        
        overall_healthy = True
        
        for name, check_func in self.checks.items():
            try:
                check_result = check_func()
                results['checks'][name] = check_result
                
                if not check_result.get('healthy', False):
                    overall_healthy = False
                    
            except Exception as e:
                results['checks'][name] = {
                    'healthy': False,
                    'error': str(e)
                }
                overall_healthy = False
        
        results['status'] = 'healthy' if overall_healthy else 'unhealthy'
        return results
    
    def _check_database(self) -> Dict[str, Any]:
        """فحص قاعدة البيانات"""
        try:
            with self.db_manager.session_scope() as session:
                session.execute('SELECT 1')
                return {
                    'healthy': True,
                    'message': 'قاعدة البيانات تعمل بشكل طبيعي'
                }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }
    
    def _check_redis(self) -> Dict[str, Any]:
        """فحص Redis"""
        try:
            from .cache import cache_manager
            if cache_manager.redis_client:
                cache_manager.redis_client.ping()
                return {
                    'healthy': True,
                    'message': 'Redis يعمل بشكل طبيعي'
                }
            else:
                return {
                    'healthy': False,
                    'message': 'Redis غير متصل'
                }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }
    
    def _check_disk_space(self) -> Dict[str, Any]:
        """فحص مساحة القرص"""
        try:
            import shutil
            total, used, free = shutil.disk_usage('/')
            free_percent = (free / total) * 100
            
            return {
                'healthy': free_percent > 10,  # تحذير إذا كانت المساحة أقل من 10%
                'free_space_percent': free_percent,
                'free_space_gb': free / (1024**3),
                'message': f'المساحة المتاحة: {free_percent:.1f}%'
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }
    
    def _check_memory(self) -> Dict[str, Any]:
        """فحص الذاكرة"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            
            return {
                'healthy': memory.percent < 90,  # تحذير إذا كان الاستخدام أكثر من 90%
                'memory_percent': memory.percent,
                'available_gb': memory.available / (1024**3),
                'message': f'استخدام الذاكرة: {memory.percent:.1f}%'
            }
        except ImportError:
            return {
                'healthy': True,
                'message': 'psutil غير متاح لفحص الذاكرة'
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }


class SentryIntegration:
    """تكامل Sentry لمراقبة الأخطاء"""
    
    def __init__(self, app=None):
        self.app = app
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة Sentry"""
        dsn = app.config.get('SENTRY_DSN')
        
        if dsn:
            sentry_sdk.init(
                dsn=dsn,
                integrations=[
                    FlaskIntegration(transaction_style='endpoint'),
                    SqlalchemyIntegration()
                ],
                traces_sample_rate=0.1,
                environment=app.config.get('FLASK_ENV', 'production'),
                release=app.config.get('APP_VERSION', '1.0.0')
            )
            
            # إعداد السياق الافتراضي
            with sentry_sdk.configure_scope() as scope:
                scope.set_tag("application", "alemis")


# مُزخرفات المراقبة
def monitor_performance(func_name: str = None):
    """مُزخرف لمراقبة الأداء"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = datetime.utcnow()
            function_name = func_name or f.__name__
            
            try:
                result = f(*args, **kwargs)
                duration = (datetime.utcnow() - start_time).total_seconds()
                
                structured_logger.logger.info(
                    "function_performance",
                    function=function_name,
                    duration=duration,
                    success=True
                )
                
                return result
                
            except Exception as e:
                duration = (datetime.utcnow() - start_time).total_seconds()
                
                structured_logger.logger.error(
                    "function_error",
                    function=function_name,
                    duration=duration,
                    error=str(e),
                    success=False
                )
                
                raise
        
        return decorated_function
    return decorator


def audit_action(action: str, resource_type: str):
    """مُزخرف لتسجيل التدقيق"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = session.get('user_id') if session else None
            
            # تنفيذ الدالة
            result = f(*args, **kwargs)
            
            # تسجيل الإجراء
            audit_logger.log_action(
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                new_values={'result': str(result) if result else None}
            )
            
            return result
        
        return decorated_function
    return decorator


# إنشاء مثيلات الأنظمة
structured_logger = StructuredLogger()
audit_logger = AuditLogger(db_manager)
health_checker = HealthChecker()
sentry_integration = SentryIntegration()


def init_monitoring_systems(app):
    """تهيئة أنظمة المراقبة"""
    structured_logger.init_app(app)
    health_checker.init_app(app)
    sentry_integration.init_app(app)
    
    # إضافة مسار فحص الصحة
    @app.route('/health')
    def health_check():
        return health_checker.run_checks()
