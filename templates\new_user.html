{% extends "base.html" %}

{% block title %}ALEMIS - إضافة مستخدم جديد{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">إضافة مستخدم جديد</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-user-plus me-1"></i>
                نموذج إضافة مستخدم
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('new_user') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="first_name" class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="last_name" class="form-label">الاسم الأخير</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="col-md-6">
                            <label for="role" class="form-label">الدور</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="" selected disabled>اختر الدور</option>
                                <option value="employee">موظف</option>
                                <option value="manager">مدير قسم</option>
                                <option value="hr">موارد بشرية</option>
                                <option value="gm">مدير عام</option>
                                {% if user.role == 'admin' %}
                                <option value="admin">مدير النظام</option>
                                {% endif %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id" required>
                                <option value="" selected disabled>اختر القسم</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}">{{ department.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ
                            </button>
                            <a href="{{ url_for('users') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
