{% extends "base.html" %}

{% block title %}{{ page_title }} - شركة العميس الطبية{% endblock %}

{% block extra_css %}
<style>
    .request-card {
        border: 1px solid #e3e6f0;
        border-radius: 10px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        background: white;
    }

    .request-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .request-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 10px 10px 0 0;
        font-weight: bold;
    }

    .request-body {
        padding: 20px;
    }

    .request-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .request-details {
        background: #f8f9fc;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
    }

    .status-badge {
        font-size: 0.9em;
        padding: 8px 15px;
        border-radius: 20px;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 15px;
    }

    .stats-summary {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .filter-tabs {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-pills .nav-link {
        border-radius: 25px;
        margin: 0 5px;
        font-weight: 500;
    }

    .nav-pills .nav-link.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="mb-3">
                <i class="fas fa-list-alt me-3"></i>
                {{ page_title }}
            </h1>
            <p class="lead mb-0">عرض جميع الطلبات بحالة: <span class="badge bg-{{ status_badge }}">{{ status_text }}</span></p>
        </div>
    </div>

    <!-- Statistics Summary -->
    <div class="stats-summary">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-calendar-alt fa-2x text-primary me-3"></i>
                    <div>
                        <h4 class="mb-0">{{ leave_requests|length }}</h4>
                        <small class="text-muted">طلبات إجازة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-calendar-plus fa-2x text-success me-3"></i>
                    <div>
                        <h4 class="mb-0">{{ coverage_requests|length }}</h4>
                        <small class="text-muted">طلبات تغطية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-exchange-alt fa-2x text-warning me-3"></i>
                    <div>
                        <h4 class="mb-0">{{ shift_swap_requests|length }}</h4>
                        <small class="text-muted">طلبات تبديل</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <ul class="nav nav-pills justify-content-center" id="requestTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab">
                    <i class="fas fa-list me-2"></i>جميع الطلبات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="leaves-tab" data-bs-toggle="pill" data-bs-target="#leaves" type="button" role="tab">
                    <i class="fas fa-calendar-alt me-2"></i>طلبات الإجازة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="coverage-tab" data-bs-toggle="pill" data-bs-target="#coverage" type="button" role="tab">
                    <i class="fas fa-calendar-plus me-2"></i>طلبات التغطية
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="swaps-tab" data-bs-toggle="pill" data-bs-target="#swaps" type="button" role="tab">
                    <i class="fas fa-exchange-alt me-2"></i>طلبات التبديل
                </button>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="requestTabsContent">
        <!-- All Requests Tab -->
        <div class="tab-pane fade show active" id="all" role="tabpanel">
            {% set all_requests = [] %}
            {% for leave in leave_requests %}
                {% set _ = all_requests.append(('leave', leave)) %}
            {% endfor %}
            {% for coverage in coverage_requests %}
                {% set _ = all_requests.append(('coverage', coverage)) %}
            {% endfor %}
            {% for swap in shift_swap_requests %}
                {% set _ = all_requests.append(('swap', swap)) %}
            {% endfor %}

            {% if all_requests %}
                <div class="row">
                    {% for request_type, request in all_requests %}
                        <div class="col-md-6 col-lg-4">
                            {% if request_type == 'leave' %}
                                {% include 'partials/leave_request_card.html' %}
                            {% elif request_type == 'coverage' %}
                                {% include 'partials/coverage_request_card.html' %}
                            {% elif request_type == 'swap' %}
                                {% include 'partials/swap_request_card.html' %}
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h4>لا توجد طلبات</h4>
                    <p>لا توجد طلبات بحالة "{{ status_text }}" حالياً</p>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Leave Requests Tab -->
        <div class="tab-pane fade" id="leaves" role="tabpanel">
            {% if leave_requests %}
                <div class="row">
                    {% for request in leave_requests %}
                        <div class="col-md-6 col-lg-4">
                            {% include 'partials/leave_request_card.html' %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-calendar-alt"></i>
                    <h4>لا توجد طلبات إجازة</h4>
                    <p>لا توجد طلبات إجازة بحالة "{{ status_text }}" حالياً</p>
                </div>
            {% endif %}
        </div>

        <!-- Coverage Requests Tab -->
        <div class="tab-pane fade" id="coverage" role="tabpanel">
            {% if coverage_requests %}
                <div class="row">
                    {% for request in coverage_requests %}
                        <div class="col-md-6 col-lg-4">
                            {% include 'partials/coverage_request_card.html' %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-calendar-plus"></i>
                    <h4>لا توجد طلبات تغطية</h4>
                    <p>لا توجد طلبات تغطية بحالة "{{ status_text }}" حالياً</p>
                </div>
            {% endif %}
        </div>

        <!-- Swap Requests Tab -->
        <div class="tab-pane fade" id="swaps" role="tabpanel">
            {% if shift_swap_requests %}
                <div class="row">
                    {% for request in shift_swap_requests %}
                        <div class="col-md-6 col-lg-4">
                            {% include 'partials/swap_request_card.html' %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-exchange-alt"></i>
                    <h4>لا توجد طلبات تبديل</h4>
                    <p>لا توجد طلبات تبديل بحالة "{{ status_text }}" حالياً</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Back to Dashboard -->
    <div class="text-center mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary btn-lg">
            <i class="fas fa-arrow-right me-2"></i>العودة للوحة التحكم
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تفعيل التبويبات
    var triggerTabList = [].slice.call(document.querySelectorAll('#requestTabs button'))
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl)
        
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault()
            tabTrigger.show()
        })
    })
</script>
{% endblock %}
