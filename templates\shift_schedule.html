{% extends "base.html" %}

{% block title %}ALEMIS - جدول الشفتات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4 title-with-red-line">جدول الشفتات الشهري</h2>
        <div class="red-line-animated"></div>

        <!-- أدوات التصفية -->
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom">
                <i class="fas fa-filter me-1"></i>
                تصفية الجدول
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="month" class="form-label">الشهر</label>
                            <select class="form-select" id="month" name="month">
                                <option value="1" {% if current_month == 1 %}selected{% endif %}>يناير</option>
                                <option value="2" {% if current_month == 2 %}selected{% endif %}>فبراير</option>
                                <option value="3" {% if current_month == 3 %}selected{% endif %}>مارس</option>
                                <option value="4" {% if current_month == 4 %}selected{% endif %}>أبريل</option>
                                <option value="5" {% if current_month == 5 %}selected{% endif %}>مايو</option>
                                <option value="6" {% if current_month == 6 %}selected{% endif %}>يونيو</option>
                                <option value="7" {% if current_month == 7 %}selected{% endif %}>يوليو</option>
                                <option value="8" {% if current_month == 8 %}selected{% endif %}>أغسطس</option>
                                <option value="9" {% if current_month == 9 %}selected{% endif %}>سبتمبر</option>
                                <option value="10" {% if current_month == 10 %}selected{% endif %}>أكتوبر</option>
                                <option value="11" {% if current_month == 11 %}selected{% endif %}>نوفمبر</option>
                                <option value="12" {% if current_month == 12 %}selected{% endif %}>ديسمبر</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="year" class="form-label">السنة</label>
                            <select class="form-select" id="year" name="year">
                                {% for y in range(current_year-2, current_year+3) %}
                                <option value="{{ y }}" {% if y == current_year %}selected{% endif %}>{{ y }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" id="update-schedule" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث الجدول
                        </button>
                    </div>
                    {% if session.role in ['manager', 'admin'] and schedule_status == 'draft' %}
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" id="generate-schedule" class="btn btn-success">
                            <i class="fas fa-calendar-plus me-1"></i>
                            إنشاء جدول جديد
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="red-line"></div>

        <!-- حالة الجدول -->
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom">
                <i class="fas fa-info-circle me-1"></i>
                حالة الجدول
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>حالة الجدول:</strong>
                            {% if schedule_status == 'draft' %}
                            <span class="badge bg-warning">مسودة</span>
                            {% elif schedule_status == 'manager_approved' %}
                            <span class="badge bg-info">موافقة المدير</span>
                            {% elif schedule_status == 'hr_approved' %}
                            <span class="badge bg-primary">موافقة الموارد البشرية</span>
                            {% elif schedule_status == 'gm_approved' %}
                            <span class="badge bg-success">معتمد</span>
                            {% else %}
                            <span class="badge bg-secondary">غير متوفر</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        {% if session.role == 'manager' and schedule_status == 'draft' %}
                        <button type="button" class="btn btn-primary btn-sm" id="approve-manager">
                            <i class="fas fa-check me-1"></i>
                            موافقة المدير
                        </button>
                        {% endif %}

                        {% if session.role == 'hr' and schedule_status == 'manager_approved' %}
                        <button type="button" class="btn btn-primary btn-sm" id="approve-hr">
                            <i class="fas fa-check me-1"></i>
                            موافقة الموارد البشرية
                        </button>
                        {% endif %}

                        {% if session.role == 'gm' and schedule_status == 'hr_approved' %}
                        <button type="button" class="btn btn-success btn-sm" id="approve-gm">
                            <i class="fas fa-check-double me-1"></i>
                            اعتماد الجدول
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الشفتات -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar me-1"></i>
                جدول الشفتات لشهر {{ month_name }} {{ current_year }}
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="width: 200px;">الموظف</th>
                                {% for day in range(1, num_days + 1) %}
                                <th style="width: 40px;" class="text-center {% if day_of_week[day-1] == 5 %}bg-light{% endif %}">{{ day }}</th>
                                {% endfor %}
                            </tr>
                            <tr>
                                <th>اليوم</th>
                                {% for day in range(1, num_days + 1) %}
                                <th class="text-center {% if day_of_week[day-1] == 5 %}bg-light{% endif %}">
                                    {% if day_of_week[day-1] == 0 %}
                                    السبت
                                    {% elif day_of_week[day-1] == 1 %}
                                    الأحد
                                    {% elif day_of_week[day-1] == 2 %}
                                    الإثنين
                                    {% elif day_of_week[day-1] == 3 %}
                                    الثلاثاء
                                    {% elif day_of_week[day-1] == 4 %}
                                    الأربعاء
                                    {% elif day_of_week[day-1] == 5 %}
                                    الخميس
                                    {% elif day_of_week[day-1] == 6 %}
                                    الجمعة
                                    {% endif %}
                                </th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>{{ employee.first_name }} {{ employee.last_name }}</td>
                                {% for day in range(1, num_days + 1) %}
                                <td class="text-center {% if day_of_week[day-1] == 5 %}bg-light{% endif %} {% if day_of_week[day-1] == 6 %}bg-warning-subtle{% endif %}">
                                    {% if day_of_week[day-1] == 6 %}
                                        <!-- يوم الجمعة - يوم إجازة -->
                                        {% if employee.id == friday_employees.morning and shift_data[employee.id][day] == 'morning' %}
                                        <span class="badge bg-success">ص</span>
                                        {% elif employee.id in friday_employees.evening and shift_data[employee.id][day] == 'evening' %}
                                        <span class="badge bg-primary">م</span>
                                        {% elif employee.id == friday_employees.night and shift_data[employee.id][day] == 'night' %}
                                        <span class="badge bg-dark">ل</span>
                                        {% else %}
                                        <span class="badge bg-secondary">إجازة</span>
                                        {% endif %}
                                    {% else %}
                                        {% if shift_data[employee.id][day] %}
                                        <span class="badge {% if shift_data[employee.id][day] == 'morning' %}bg-success{% elif shift_data[employee.id][day] == 'evening' %}bg-primary{% elif shift_data[employee.id][day] == 'night' %}bg-dark{% elif shift_data[employee.id][day] == 'split' %}bg-info{% endif %}"
                                              {% if schedule_status == 'draft' and session.role == 'manager' %}
                                              data-bs-toggle="dropdown" role="button" aria-expanded="false" style="cursor: pointer;"
                                              {% endif %}>
                                            {% if shift_data[employee.id][day] == 'morning' %}
                                            ص
                                            {% elif shift_data[employee.id][day] == 'evening' %}
                                            م
                                            {% elif shift_data[employee.id][day] == 'night' %}
                                            ل
                                            {% elif shift_data[employee.id][day] == 'split' %}
                                            ف
                                            {% endif %}
                                        </span>
                                        {% if schedule_status == 'draft' and session.role == 'manager' %}
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item change-shift" href="#" data-user="{{ employee.id }}" data-day="{{ day }}" data-shift="morning">صباحي (8ص-4م)</a></li>
                                            <li><a class="dropdown-item change-shift" href="#" data-user="{{ employee.id }}" data-day="{{ day }}" data-shift="evening">مسائي (4م-12ل)</a></li>
                                            <li><a class="dropdown-item change-shift" href="#" data-user="{{ employee.id }}" data-day="{{ day }}" data-shift="night">ليلي (12ل-8ص)</a></li>
                                            <li><a class="dropdown-item change-shift" href="#" data-user="{{ employee.id }}" data-day="{{ day }}" data-shift="split">فترتين (9ص-12ظ، 4م-9م)</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item change-shift" href="#" data-user="{{ employee.id }}" data-day="{{ day }}" data-shift="none">بدون دوام</a></li>
                                        </ul>
                                        {% endif %}
                                        {% endif %}
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <div class="d-flex justify-content-start">
                        <div class="me-3"><span class="badge bg-success">ص</span> دوام صباحي (8ص-4م)</div>
                        <div class="me-3"><span class="badge bg-primary">م</span> دوام مسائي (4م-12ل)</div>
                        <div class="me-3"><span class="badge bg-dark">ل</span> دوام ليلي (12ل-8ص)</div>
                        <div><span class="badge bg-info">ف</span> دوام فترتين (9ص-12ظ، 4م-9م)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول التغطيات يوم الجمعة -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar-plus me-1"></i>
                جدول تغطيات يوم الجمعة
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>صباحي (8ص-4م)</th>
                                <th>مسائي (4م-12ل)</th>
                                <th>ليلي (12ل-8ص)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for friday in fridays %}
                            <tr>
                                <td>{{ friday.date }}</td>
                                <td>
                                    <select class="form-select friday-coverage" data-date="{{ friday.date }}" data-shift="morning" {% if schedule_status != 'draft' or session.role != 'manager' %}disabled{% endif %}>
                                        <option value="">-- اختر موظف --</option>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}" {% if friday.morning_employee_id == employee.id %}selected{% endif %}>{{ employee.first_name }} {{ employee.last_name }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td>
                                    <select class="form-select friday-coverage" data-date="{{ friday.date }}" data-shift="evening" {% if schedule_status != 'draft' or session.role != 'manager' %}disabled{% endif %}>
                                        <option value="">-- اختر موظف --</option>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}" {% if friday.evening_employee_id == employee.id %}selected{% endif %}>{{ employee.first_name }} {{ employee.last_name }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td>
                                    <select class="form-select friday-coverage" data-date="{{ friday.date }}" data-shift="night" {% if schedule_status != 'draft' or session.role != 'manager' %}disabled{% endif %}>
                                        <option value="">-- اختر موظف --</option>
                                        {% for employee in employees %}
                                        <option value="{{ employee.id }}" {% if friday.night_employee_id == employee.id %}selected{% endif %}>{{ employee.first_name }} {{ employee.last_name }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- طلبات تبديل الدوام المباشرة المعلقة -->
        {% if schedule_status == 'draft' and session.role == 'manager' and pending_direct_swaps %}
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <i class="fas fa-exchange-alt me-1"></i>
                طلبات تبديل الدوام المباشرة المعلقة
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    هذه الطلبات تم تقديمها من قبل الموظفين لتبديل الدوام قبل اعتماد الجدول. يرجى مراجعتها والموافقة عليها أو رفضها.
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>التبديل مع</th>
                                <th>نوع التبديل</th>
                                <th>التفاصيل</th>
                                <th>السبب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for swap in pending_direct_swaps %}
                            <tr>
                                <td>{{ swap.first_name }} {{ swap.last_name }}</td>
                                <td>{{ swap.swap_with_first_name }} {{ swap.swap_with_last_name }}</td>
                                <td>
                                    {% if swap.swap_type == 'day_swap' %}
                                    تبديل يوم كامل
                                    {% elif swap.swap_type == 'shift_swap' %}
                                    تبديل شفت محدد
                                    {% endif %}
                                </td>
                                <td>
                                    {% if swap.swap_type == 'day_swap' %}
                                    تبديل يوم {{ swap.my_day }} مع يوم {{ swap.other_day }}
                                    {% elif swap.swap_type == 'shift_swap' %}
                                    تبديل شفت {{ swap.my_shift_name }} في يوم {{ swap.my_day }} مع شفت {{ swap.other_shift_name }} في يوم {{ swap.other_day }}
                                    {% endif %}
                                </td>
                                <td>{{ swap.reason }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-success approve-direct-swap" data-id="{{ swap.id }}">
                                        <i class="fas fa-check"></i> موافقة
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-direct-swap" data-id="{{ swap.id }}">
                                        <i class="fas fa-times"></i> رفض
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- طلبات تغطية يوم الجمعة المعلقة -->
        {% if schedule_status == 'draft' and session.role == 'manager' and pending_friday_requests %}
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <i class="fas fa-calendar-day me-1"></i>
                طلبات تغطية يوم الجمعة المعلقة
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    هذه الطلبات تم تقديمها من قبل الموظفين للتغطية في يوم الجمعة. يرجى مراجعتها والموافقة عليها أو رفضها.
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>التاريخ</th>
                                <th>الشفت</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in pending_friday_requests %}
                            <tr>
                                <td>{{ request.first_name }} {{ request.last_name }}</td>
                                <td>{{ request.date }}</td>
                                <td>
                                    {% if request.shift == 'morning' %}
                                    صباحي (8ص-4م)
                                    {% elif request.shift == 'evening' %}
                                    مسائي (4م-12ل)
                                    {% elif request.shift == 'night' %}
                                    ليلي (12ل-8ص)
                                    {% endif %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-success approve-friday-request" data-id="{{ request.id }}">
                                        <i class="fas fa-check"></i> موافقة
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-friday-request" data-id="{{ request.id }}">
                                        <i class="fas fa-times"></i> رفض
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث الجدول عند تغيير الشهر أو السنة
        document.getElementById('update-schedule').addEventListener('click', function() {
            const month = document.getElementById('month').value;
            const year = document.getElementById('year').value;

            window.location.href = `{{ url_for('shift_schedule') }}?month=${month}&year=${year}`;
        });

        // إنشاء جدول جديد
        {% if session.role in ['manager', 'admin'] %}
        document.getElementById('generate-schedule').addEventListener('click', function() {
            if (confirm('هل أنت متأكد من إنشاء جدول جديد؟ سيتم حذف الجدول الحالي إذا كان موجوداً.')) {
                const month = document.getElementById('month').value;
                const year = document.getElementById('year').value;

                fetch('{{ url_for("generate_shift_schedule") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        month: month,
                        year: year
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إنشاء الجدول بنجاح');
                        window.location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.error);
                    }
                });
            }
        });
        {% endif %}

        // تغيير الشفت
        const changeShiftLinks = document.querySelectorAll('.change-shift');
        changeShiftLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const userId = this.getAttribute('data-user');
                const day = this.getAttribute('data-day');
                const shift = this.getAttribute('data-shift');

                fetch('{{ url_for("update_shift") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        day: day,
                        shift: shift,
                        month: "{{ current_month }}",
                        year: "{{ current_year }}"
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.error);
                    }
                });
            });
        });

        // تحديث تغطيات يوم الجمعة
        const fridayCoverageSelects = document.querySelectorAll('.friday-coverage');
        fridayCoverageSelects.forEach(select => {
            select.addEventListener('change', function() {
                const date = this.getAttribute('data-date');
                const shift = this.getAttribute('data-shift');
                const employeeId = this.value;

                fetch('{{ url_for("update_friday_coverage") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        date: date,
                        shift: shift,
                        employee_id: employeeId
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تم التحديث بنجاح
                    } else {
                        alert('حدث خطأ: ' + data.error);
                    }
                });
            });
        });

        // الموافقة على طلبات التبديل المباشرة
        const approveDirectSwapButtons = document.querySelectorAll('.approve-direct-swap');
        approveDirectSwapButtons.forEach(button => {
            button.addEventListener('click', function() {
                const swapId = this.getAttribute('data-id');

                if (confirm('هل أنت متأكد من الموافقة على طلب التبديل؟')) {
                    fetch('{{ url_for("approve_direct_swap") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            swap_id: swapId,
                            action: 'approve'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });

        // رفض طلبات التبديل المباشرة
        const rejectDirectSwapButtons = document.querySelectorAll('.reject-direct-swap');
        rejectDirectSwapButtons.forEach(button => {
            button.addEventListener('click', function() {
                const swapId = this.getAttribute('data-id');

                if (confirm('هل أنت متأكد من رفض طلب التبديل؟')) {
                    fetch('{{ url_for("approve_direct_swap") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            swap_id: swapId,
                            action: 'reject'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });

        // الموافقة على طلبات تغطية يوم الجمعة
        const approveFridayRequestButtons = document.querySelectorAll('.approve-friday-request');
        approveFridayRequestButtons.forEach(button => {
            button.addEventListener('click', function() {
                const requestId = this.getAttribute('data-id');

                if (confirm('هل أنت متأكد من الموافقة على طلب التغطية؟')) {
                    fetch('{{ url_for("approve_friday_request") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            request_id: requestId,
                            action: 'approve'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });

        // رفض طلبات تغطية يوم الجمعة
        const rejectFridayRequestButtons = document.querySelectorAll('.reject-friday-request');
        rejectFridayRequestButtons.forEach(button => {
            button.addEventListener('click', function() {
                const requestId = this.getAttribute('data-id');

                if (confirm('هل أنت متأكد من رفض طلب التغطية؟')) {
                    fetch('{{ url_for("approve_friday_request") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            request_id: requestId,
                            action: 'reject'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });

        // موافقة المدير
        {% if session.role == 'manager' %}
        const approveManagerBtn = document.getElementById('approve-manager');
        if (approveManagerBtn) {
            approveManagerBtn.addEventListener('click', function() {
                if (confirm('هل أنت متأكد من الموافقة على الجدول؟')) {
                    fetch('{{ url_for("approve_shift_schedule") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            month: "{{ current_month }}",
                            year: "{{ current_year }}",
                            approval_type: 'manager'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        }
        {% endif %}

        // موافقة الموارد البشرية
        {% if session.role == 'hr' %}
        const approveHrBtn = document.getElementById('approve-hr');
        if (approveHrBtn) {
            approveHrBtn.addEventListener('click', function() {
                if (confirm('هل أنت متأكد من الموافقة على الجدول؟')) {
                    fetch('{{ url_for("approve_shift_schedule") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            month: "{{ current_month }}",
                            year: "{{ current_year }}",
                            approval_type: 'hr'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        }
        {% endif %}

        // اعتماد المدير العام
        {% if session.role == 'gm' %}
        const approveGmBtn = document.getElementById('approve-gm');
        if (approveGmBtn) {
            approveGmBtn.addEventListener('click', function() {
                if (confirm('هل أنت متأكد من اعتماد الجدول؟')) {
                    fetch('{{ url_for("approve_shift_schedule") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            month: "{{ current_month }}",
                            year: "{{ current_year }}",
                            approval_type: 'gm'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        }
        {% endif %}
    });
</script>
{% endblock %}
