global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # مراقبة Prometheus نفسه
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # مراقبة تطبيق ALEMIS
  - job_name: 'alemis-app'
    static_configs:
      - targets: ['app:5000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # مراقبة PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  # مراقبة Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # مراقبة Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']

  # مراقبة النظام
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
