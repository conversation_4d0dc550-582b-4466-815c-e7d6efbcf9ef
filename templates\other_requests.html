{% extends "base.html" %}

{% block title %}ALEMIS - الطلبات الأخرى{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">الطلبات الأخرى</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-clipboard-list me-1"></i>
                    قائمة الطلبات الأخرى
                </div>
                <a href="{{ url_for('new_other_request') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    طلب جديد
                </a>
            </div>
            <div class="card-body">
                {% if other_requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                                <th>الموظف</th>
                                {% endif %}
                                <th>العنوان</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                                <th>موافقة المدير</th>
                                <th>موافقة الموارد البشرية</th>
                                <th>موافقة المدير العام</th>
                                <th>تاريخ الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in other_requests %}
                            <tr>
                                {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                                <td>{{ request.first_name }} {{ request.last_name }}</td>
                                {% endif %}
                                <td>{{ request.title }}</td>
                                <td>{{ request.description }}</td>
                                <td>
                                    {% if request.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif request.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif request.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if request.manager_approval == 1 %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif request.manager_approval == -1 %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% else %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if request.hr_approval == 1 %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif request.hr_approval == -1 %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% else %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if request.gm_approval == 1 %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif request.gm_approval == -1 %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% else %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% endif %}
                                </td>
                                <td>{{ request.created_at }}</td>
                                <td>
                                    {% if session.role == 'manager' and request.manager_approval == 0 %}
                                    <button type="button" class="btn btn-sm btn-success approve-request" data-id="{{ request.id }}" data-type="manager" data-action="approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-request" data-id="{{ request.id }}" data-type="manager" data-action="reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    
                                    {% if session.role == 'hr' and request.manager_approval == 1 and request.hr_approval == 0 %}
                                    <button type="button" class="btn btn-sm btn-success approve-request" data-id="{{ request.id }}" data-type="hr" data-action="approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-request" data-id="{{ request.id }}" data-type="hr" data-action="reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    
                                    {% if session.role == 'gm' and request.hr_approval == 1 and request.gm_approval == 0 %}
                                    <button type="button" class="btn btn-sm btn-success approve-request" data-id="{{ request.id }}" data-type="gm" data-action="approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-request" data-id="{{ request.id }}" data-type="gm" data-action="reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    
                                    {% if request.user_id == session.user_id and request.status == 'pending' %}
                                    <button type="button" class="btn btn-sm btn-danger cancel-request" data-id="{{ request.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد طلبات أخرى حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الموافقة على الطلب
        const approveButtons = document.querySelectorAll('.approve-request');
        approveButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');
                
                if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
                    fetch('{{ url_for("approve_other_request") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            request_id: id,
                            approval_type: type,
                            action: 'approve'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });
        
        // رفض الطلب
        const rejectButtons = document.querySelectorAll('.reject-request');
        rejectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');
                
                if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
                    fetch('{{ url_for("approve_other_request") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            request_id: id,
                            approval_type: type,
                            action: 'reject'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });
        
        // إلغاء الطلب
        const cancelButtons = document.querySelectorAll('.cancel-request');
        cancelButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                
                if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                    fetch('{{ url_for("cancel_other_request") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            request_id: id
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });
    });
</script>
{% endblock %}
