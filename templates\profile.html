{% extends "base.html" %}

{% block title %}ALEMIS - الملف الشخصي{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
<style>
    .profile-header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 6s ease-in-out infinite;
    }

    .profile-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -5%;
        width: 150px;
        height: 150px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite reverse;
    }

    .profile-avatar-container {
        width: 150px;
        height: 150px;
        margin: 0 auto 1.5rem;
        border-radius: 50%;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        border: 4px solid rgba(255, 255, 255, 0.2);
        position: relative;
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
    }

    .profile-avatar-container::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #0ea5e9, #06b6d4, #0891b2, #0e7490);
        border-radius: 50%;
        z-index: -1;
        animation: rotate 3s linear infinite;
    }

    .profile-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
    }

    .profile-name {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--medical-dark);
        margin-bottom: 0.5rem;
    }

    .profile-role {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .role-admin {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        color: white;
    }

    .role-hr {
        background: linear-gradient(135deg, #7c3aed, #6d28d9);
        color: white;
    }

    .role-manager {
        background: linear-gradient(135deg, #059669, #047857);
        color: white;
    }

    .role-gm {
        background: linear-gradient(135deg, #d97706, #b45309);
        color: white;
    }

    .role-employee {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        color: white;
    }

    .info-table {
        background: rgba(14, 165, 233, 0.02);
        border-radius: 15px;
        overflow: hidden;
        border: 2px solid rgba(14, 165, 233, 0.1);
    }

    .info-table th {
        background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(14, 165, 233, 0.05));
        color: var(--medical-dark);
        font-weight: 600;
        padding: 1rem;
        border: none;
        border-bottom: 1px solid rgba(14, 165, 233, 0.1);
        width: 40%;
    }

    .info-table td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid rgba(14, 165, 233, 0.1);
        color: var(--medical-secondary);
    }

    .info-table tr:hover {
        background: rgba(14, 165, 233, 0.05);
    }

    .action-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        border: 2px solid rgba(14, 165, 233, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #0ea5e9, #06b6d4, #0891b2);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .action-card:hover::before {
        transform: scaleX(1);
    }

    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(14, 165, 233, 0.2);
        border-color: var(--medical-primary);
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--medical-dark);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<!-- Profile Header -->
<div class="profile-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="profile-icon">
                    <i class="fas fa-user-circle" style="font-size: 3rem; margin-bottom: 1rem; color: rgba(255,255,255,0.8);"></i>
                </div>
                <h1 style="font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); margin-bottom: 0.5rem;">الملف الشخصي</h1>
                <p style="font-size: 1.1rem; opacity: 0.9; font-weight: 300;">إدارة المعلومات الشخصية والإعدادات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="profile-stats">
                    <div class="stat-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>ملف آمن ومحمي</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- Profile Card -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-pulse">
                            <i class="fas fa-id-card medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">معلومات الملف الشخصي</h5>
                            <small class="opacity-75">البيانات الشخصية ومعلومات العمل</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <div class="profile-avatar-container">
                                <img src="https://ui-avatars.com/api/?name={{ user.first_name }}+{{ user.last_name }}&background=0ea5e9&color=fff&size=200" alt="{{ user.first_name }} {{ user.last_name }}" class="profile-avatar">
                            </div>
                            <h4 class="profile-name">{{ user.first_name }} {{ user.last_name }}</h4>
                            <div class="profile-role
                                {% if user.role == 'admin' %}role-admin
                                {% elif user.role == 'hr' %}role-hr
                                {% elif user.role == 'manager' %}role-manager
                                {% elif user.role == 'gm' %}role-gm
                                {% else %}role-employee{% endif %}">
                                {% if user.role == 'admin' %}
                                    <i class="fas fa-crown"></i>مدير النظام
                                {% elif user.role == 'hr' %}
                                    <i class="fas fa-users"></i>موارد بشرية
                                {% elif user.role == 'manager' %}
                                    <i class="fas fa-user-tie"></i>مدير قسم
                                {% elif user.role == 'gm' %}
                                    <i class="fas fa-chess-king"></i>مدير عام
                                {% else %}
                                    <i class="fas fa-user"></i>موظف
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="section-title">
                                        <div class="section-icon">
                                            <i class="fas fa-info-circle"></i>
                                        </div>
                                        معلومات أساسية
                                    </div>
                                    <table class="table info-table">
                                        <tr>
                                            <th><i class="fas fa-user me-2 text-primary"></i>اسم المستخدم</th>
                                            <td>{{ user.username }}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-envelope me-2 text-primary"></i>البريد الإلكتروني</th>
                                            <td>{{ user.email }}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-id-badge me-2 text-primary"></i>الاسم الأول</th>
                                            <td>{{ user.first_name }}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-id-badge me-2 text-primary"></i>الاسم الأخير</th>
                                            <td>{{ user.last_name }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <div class="section-title">
                                        <div class="section-icon">
                                            <i class="fas fa-briefcase"></i>
                                        </div>
                                        معلومات العمل
                                    </div>
                                    <table class="table info-table">
                                        <tr>
                                            <th><i class="fas fa-building me-2 text-info"></i>القسم</th>
                                            <td>
                                                {% if user.department_id %}
                                                    {% set dept_id = user.department_id %}
                                                    {% if dept_id == 1 %}
                                                        المختبر الكيميائي
                                                    {% elif dept_id == 2 %}
                                                        المختبر البيولوجي
                                                    {% elif dept_id == 3 %}
                                                        مختبر الأبحاث
                                                    {% elif dept_id == 4 %}
                                                        الإدارة
                                                    {% elif dept_id == 5 %}
                                                        الموارد البشرية
                                                    {% else %}
                                                        غير محدد
                                                    {% endif %}
                                                {% else %}
                                                    غير محدد
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-user-tag me-2 text-info"></i>الدور</th>
                                            <td>
                                                {% if user.role == 'admin' %}
                                                    مدير النظام
                                                {% elif user.role == 'hr' %}
                                                    موارد بشرية
                                                {% elif user.role == 'manager' %}
                                                    مدير قسم
                                                {% elif user.role == 'gm' %}
                                                    مدير عام
                                                {% else %}
                                                    موظف
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-calendar-plus me-2 text-info"></i>تاريخ الانضمام</th>
                                            <td>{{ user.date_joined }}</td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-toggle-on me-2 text-info"></i>الحالة</th>
                                            <td>
                                                {% if user.is_active %}
                                                    <span class="badge" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">
                                                        <i class="fas fa-check-circle me-1"></i>نشط
                                                    </span>
                                                {% else %}
                                                    <span class="badge" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                                        <i class="fas fa-times-circle me-1"></i>غير نشط
                                                    </span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="action-card">
                                        <div class="section-title">
                                            <div class="section-icon">
                                                <i class="fas fa-edit"></i>
                                            </div>
                                            تعديل البيانات الشخصية
                                        </div>
                                        <p class="text-muted mb-3">يمكنك طلب تعديل بياناتك الشخصية من هنا</p>

                                        <!-- عرض الطلبات المعلقة -->
                                        {% if pending_requests %}
                                        <div class="medical-alert medical-alert-warning mb-3">
                                            <div class="alert-icon">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                            <div class="alert-content">
                                                <h6 class="mb-1">طلبات معلقة</h6>
                                                <p class="mb-2">لديك طلبات تعديل معلقة بانتظار الموافقة:</p>
                                                <ul class="mb-0">
                                                    {% for request in pending_requests %}
                                                    <li>
                                                        {% if request.request_type == 'username' %}
                                                        تغيير اسم المستخدم من <strong>{{ request.current_value }}</strong> إلى <strong>{{ request.new_value }}</strong>
                                                        {% elif request.request_type == 'password' %}
                                                        تغيير كلمة المرور
                                                        {% elif request.request_type == 'email' %}
                                                        تغيير البريد الإلكتروني من <strong>{{ request.current_value }}</strong> إلى <strong>{{ request.new_value }}</strong>
                                                        {% elif request.request_type == 'personal_info' %}
                                                        تغيير المعلومات الشخصية
                                                        {% endif %}
                                                        <small class="text-muted">({{ request.created_at }})</small>
                                                    </li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                        </div>
                                        {% endif %}

                                        <button class="btn medical-btn-advanced medical-shadow-glow-hover w-100" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                                            <i class="fas fa-edit me-1 medical-icon-advanced"></i> تعديل البيانات
                                        </button>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="action-card">
                                        <div class="section-title">
                                            <div class="section-icon">
                                                <i class="fas fa-shield-alt"></i>
                                            </div>
                                            الأمان
                                        </div>
                                        <p class="text-muted mb-3">تغيير كلمة المرور لحماية حسابك</p>
                                        <button class="btn medical-btn-advanced w-100" style="background: linear-gradient(135deg, #f59e0b, #d97706);" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                            <i class="fas fa-key me-1 medical-icon-advanced"></i> تغيير كلمة المرور
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل البيانات الشخصية -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content medical-card medical-border-animated">
            <div class="modal-header" style="background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white;">
                <h5 class="modal-title d-flex align-items-center" id="editProfileModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    تعديل البيانات الشخصية
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <form action="{{ url_for('edit_profile') }}" method="POST">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        سيتم إرسال طلب التعديل إلى مدير المختبر للموافقة عليه.
                    </div>

                    <ul class="nav nav-tabs mb-3" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">المعلومات الشخصية</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="account-tab" data-bs-toggle="tab" data-bs-target="#account" type="button" role="tab" aria-controls="account" aria-selected="false">معلومات الحساب</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="profileTabsContent">
                        <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="first_name" class="form-label">الاسم الأول</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="last_name" class="form-label">الاسم الأخير</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}" required>
                                </div>
                            </div>
                            <input type="hidden" name="request_type" value="personal_info">
                        </div>

                        <div class="tab-pane fade" id="account" role="tabpanel" aria-labelledby="account-tab">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}">
                                <div class="form-text">تغيير اسم المستخدم سيتطلب موافقة مدير المختبر.</div>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}">
                            </div>
                            <input type="hidden" name="request_type" value="account_info">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب التعديل</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                    </div>

                    <div class="text-end" style="padding-top: 1rem; border-top: 1px solid rgba(14, 165, 233, 0.1);">
                        <button type="button" class="btn medical-btn-advanced" style="background: linear-gradient(135deg, #6b7280, #4b5563);" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </button>
                        <button type="submit" class="btn medical-btn-advanced medical-shadow-glow-hover ms-2">
                            <i class="fas fa-paper-plane me-1"></i>إرسال طلب التعديل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal تغيير كلمة المرور -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content medical-card medical-border-animated">
            <div class="modal-header" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">
                <h5 class="modal-title d-flex align-items-center" id="changePasswordModalLabel">
                    <i class="fas fa-key me-2"></i>
                    تغيير كلمة المرور
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <form action="{{ url_for('change_password_request') }}" method="POST">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        سيتم إرسال طلب تغيير كلمة المرور إلى مدير المختبر للموافقة عليه.
                    </div>

                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>

                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>

                    <div class="mb-3">
                        <label for="password_reason" class="form-label">سبب تغيير كلمة المرور</label>
                        <textarea class="form-control" id="password_reason" name="reason" rows="3" required></textarea>
                    </div>

                    <div class="text-end" style="padding-top: 1rem; border-top: 1px solid rgba(245, 158, 11, 0.1);">
                        <button type="button" class="btn medical-btn-advanced" style="background: linear-gradient(135deg, #6b7280, #4b5563);" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>إلغاء
                        </button>
                        <button type="submit" class="btn medical-btn-advanced ms-2" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-key me-1"></i>إرسال طلب تغيير كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
