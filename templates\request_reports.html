{% extends "base.html" %}

{% block title %}ALEMIS - تقارير الطلبات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">تقارير الطلبات</h2>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-chart-bar me-1"></i>
                    إحصائيات الطلبات
                </div>
                <div>
                    <button class="btn btn-light btn-sm" id="exportPdf">
                        <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card mb-3">
                            <div class="card-body text-center">
                                <h5 class="card-title">طلبات الإجازات</h5>
                                <div class="display-4 mb-2">{{ leave_stats.total }}</div>
                                <div class="text-muted">إجمالي الطلبات</div>
                                <hr>
                                <div class="row">
                                    <div class="col">
                                        <span class="badge bg-success">{{ leave_stats.approved }}</span>
                                        <div class="small">تمت الموافقة</div>
                                    </div>
                                    <div class="col">
                                        <span class="badge bg-danger">{{ leave_stats.rejected }}</span>
                                        <div class="small">مرفوضة</div>
                                    </div>
                                    <div class="col">
                                        <span class="badge bg-warning">{{ leave_stats.pending }}</span>
                                        <div class="small">معلقة</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card mb-3">
                            <div class="card-body text-center">
                                <h5 class="card-title">طلبات التغطية</h5>
                                <div class="display-4 mb-2">{{ coverage_stats.total }}</div>
                                <div class="text-muted">إجمالي الطلبات</div>
                                <hr>
                                <div class="row">
                                    <div class="col">
                                        <span class="badge bg-success">{{ coverage_stats.approved }}</span>
                                        <div class="small">تمت الموافقة</div>
                                    </div>
                                    <div class="col">
                                        <span class="badge bg-danger">{{ coverage_stats.rejected }}</span>
                                        <div class="small">مرفوضة</div>
                                    </div>
                                    <div class="col">
                                        <span class="badge bg-warning">{{ coverage_stats.pending }}</span>
                                        <div class="small">معلقة</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card mb-3">
                            <div class="card-body text-center">
                                <h5 class="card-title">طلبات تبديل الدوام</h5>
                                <div class="display-4 mb-2">{{ swap_stats.total }}</div>
                                <div class="text-muted">إجمالي الطلبات</div>
                                <hr>
                                <div class="row">
                                    <div class="col">
                                        <span class="badge bg-success">{{ swap_stats.approved }}</span>
                                        <div class="small">تمت الموافقة</div>
                                    </div>
                                    <div class="col">
                                        <span class="badge bg-danger">{{ swap_stats.rejected }}</span>
                                        <div class="small">مرفوضة</div>
                                    </div>
                                    <div class="col">
                                        <span class="badge bg-warning">{{ swap_stats.pending }}</span>
                                        <div class="small">معلقة</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">توزيع الطلبات حسب الشهر</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="monthlyChart" height="250"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">توزيع الطلبات حسب النوع</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="typeChart" height="250"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar-alt me-1"></i>
                الجدول الشهري
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">الشهر</span>
                            <select class="form-select" id="monthSelect">
                                <option value="1" {% if current_month == 1 %}selected{% endif %}>يناير</option>
                                <option value="2" {% if current_month == 2 %}selected{% endif %}>فبراير</option>
                                <option value="3" {% if current_month == 3 %}selected{% endif %}>مارس</option>
                                <option value="4" {% if current_month == 4 %}selected{% endif %}>أبريل</option>
                                <option value="5" {% if current_month == 5 %}selected{% endif %}>مايو</option>
                                <option value="6" {% if current_month == 6 %}selected{% endif %}>يونيو</option>
                                <option value="7" {% if current_month == 7 %}selected{% endif %}>يوليو</option>
                                <option value="8" {% if current_month == 8 %}selected{% endif %}>أغسطس</option>
                                <option value="9" {% if current_month == 9 %}selected{% endif %}>سبتمبر</option>
                                <option value="10" {% if current_month == 10 %}selected{% endif %}>أكتوبر</option>
                                <option value="11" {% if current_month == 11 %}selected{% endif %}>نوفمبر</option>
                                <option value="12" {% if current_month == 12 %}selected{% endif %}>ديسمبر</option>
                            </select>
                            <span class="input-group-text">السنة</span>
                            <select class="form-select" id="yearSelect">
                                {% for year in range(current_year-1, current_year+2) %}
                                <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                            <button class="btn btn-primary" id="loadSchedule">
                                <i class="fas fa-search me-1"></i> عرض
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-success" id="downloadSchedule">
                            <i class="fas fa-download me-1"></i> تحميل الجدول
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="scheduleTable">
                        <thead class="table-primary">
                            <tr>
                                <th>الموظف</th>
                                {% for day in range(1, days_in_month + 1) %}
                                <th>{{ day }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>{{ employee.first_name }} {{ employee.last_name }}</td>
                                {% for day in range(1, days_in_month + 1) %}
                                <td class="schedule-cell" data-employee="{{ employee.id }}" data-day="{{ day }}">
                                    {% set shift = get_shift(employee.id, day) %}
                                    {% if shift %}
                                    <span class="badge {% if shift == 'morning' %}bg-success{% elif shift == 'evening' %}bg-warning{% elif shift == 'night' %}bg-dark{% else %}bg-secondary{% endif %}">
                                        {% if shift == 'morning' %}ص{% elif shift == 'evening' %}م{% elif shift == 'night' %}ل{% else %}{{ shift }}{% endif %}
                                    </span>
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إعداد الرسم البياني الشهري
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'طلبات الإجازات',
                    data: {{ monthly_leave_data|tojson }},
                    backgroundColor: 'rgba(75, 192, 192, 0.6)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }, {
                    label: 'طلبات التغطية',
                    data: {{ monthly_coverage_data|tojson }},
                    backgroundColor: 'rgba(153, 102, 255, 0.6)',
                    borderColor: 'rgba(153, 102, 255, 1)',
                    borderWidth: 1
                }, {
                    label: 'طلبات تبديل الدوام',
                    data: {{ monthly_swap_data|tojson }},
                    backgroundColor: 'rgba(255, 159, 64, 0.6)',
                    borderColor: 'rgba(255, 159, 64, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // إعداد الرسم البياني للأنواع
        const typeCtx = document.getElementById('typeChart').getContext('2d');
        const typeChart = new Chart(typeCtx, {
            type: 'pie',
            data: {
                labels: ['طلبات الإجازات', 'طلبات التغطية', 'طلبات تبديل الدوام'],
                datasets: [{
                    data: [{{ leave_stats.total }}, {{ coverage_stats.total }}, {{ swap_stats.total }}],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(153, 102, 255, 0.6)',
                        'rgba(255, 159, 64, 0.6)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true
            }
        });

        // تصدير التقرير كملف PDF
        document.getElementById('exportPdf').addEventListener('click', function() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF('p', 'mm', 'a4');

            doc.addFont('assets/fonts/NotoSansArabic-Regular.ttf', 'NotoSansArabic', 'normal');
            doc.setFont('NotoSansArabic');
            doc.setR2L(true);

            doc.text('تقرير الطلبات', 105, 10, { align: 'center' });

            // تحويل الرسومات البيانية إلى صور
            html2canvas(document.getElementById('monthlyChart')).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                doc.addImage(imgData, 'PNG', 10, 20, 90, 50);

                html2canvas(document.getElementById('typeChart')).then(canvas => {
                    const imgData = canvas.toDataURL('image/png');
                    doc.addImage(imgData, 'PNG', 110, 20, 90, 50);

                    // إضافة الإحصائيات
                    doc.text('إحصائيات الطلبات', 105, 80, { align: 'center' });

                    doc.text('طلبات الإجازات:', 190, 90, { align: 'right' });
                    doc.text(`إجمالي: ${leave_stats.total}`, 190, 95, { align: 'right' });
                    doc.text(`تمت الموافقة: ${leave_stats.approved}`, 190, 100, { align: 'right' });
                    doc.text(`مرفوضة: ${leave_stats.rejected}`, 190, 105, { align: 'right' });
                    doc.text(`معلقة: ${leave_stats.pending}`, 190, 110, { align: 'right' });

                    doc.text('طلبات التغطية:', 120, 90, { align: 'right' });
                    doc.text(`إجمالي: ${coverage_stats.total}`, 120, 95, { align: 'right' });
                    doc.text(`تمت الموافقة: ${coverage_stats.approved}`, 120, 100, { align: 'right' });
                    doc.text(`مرفوضة: ${coverage_stats.rejected}`, 120, 105, { align: 'right' });
                    doc.text(`معلقة: ${coverage_stats.pending}`, 120, 110, { align: 'right' });

                    doc.text('طلبات تبديل الدوام:', 50, 90, { align: 'right' });
                    doc.text(`إجمالي: ${swap_stats.total}`, 50, 95, { align: 'right' });
                    doc.text(`تمت الموافقة: ${swap_stats.approved}`, 50, 100, { align: 'right' });
                    doc.text(`مرفوضة: ${swap_stats.rejected}`, 50, 105, { align: 'right' });
                    doc.text(`معلقة: ${swap_stats.pending}`, 50, 110, { align: 'right' });

                    doc.save('تقرير_الطلبات.pdf');
                });
            });
        });

        // تحميل الجدول الشهري
        document.getElementById('downloadSchedule').addEventListener('click', function() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF('l', 'mm', 'a4');

            doc.addFont('assets/fonts/NotoSansArabic-Regular.ttf', 'NotoSansArabic', 'normal');
            doc.setFont('NotoSansArabic');
            doc.setR2L(true);

            const month = document.getElementById('monthSelect').options[document.getElementById('monthSelect').selectedIndex].text;
            const year = document.getElementById('yearSelect').value;

            doc.text(`الجدول الشهري - ${month} ${year}`, 150, 10, { align: 'center' });

            html2canvas(document.getElementById('scheduleTable')).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                doc.addImage(imgData, 'PNG', 10, 20, 280, 150);
                doc.save(`الجدول_الشهري_${month}_${year}.pdf`);
            });
        });

        // تحميل الجدول عند تغيير الشهر أو السنة
        document.getElementById('loadSchedule').addEventListener('click', function() {
            const month = document.getElementById('monthSelect').value;
            const year = document.getElementById('yearSelect').value;
            window.location.href = `/request_reports?month=${month}&year=${year}`;
        });
    });
</script>
{% endblock %}
