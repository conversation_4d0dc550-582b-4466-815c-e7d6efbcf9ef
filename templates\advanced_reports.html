{% extends "base.html" %}

{% block title %}ALEMIS - التقارير المتقدمة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">التقارير المتقدمة</h2>

        <!-- أدوات التصفية -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-1"></i>
                تصفية البيانات
            </div>
            <div class="card-body">
                <form id="filter-form">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="report-type" class="form-label">نوع التقرير</label>
                                <select class="form-select" id="report-type" name="report_type">
                                    <option value="leaves">تقرير الإجازات</option>
                                    <option value="attendance">تقرير الحضور والغياب</option>
                                    <option value="shifts">تقرير الشفتات</option>
                                    <option value="coverage">تقرير التغطيات</option>
                                    <option value="employee">تقرير الموظفين</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="department" class="form-label">القسم</label>
                                <select class="form-select" id="department" name="department">
                                    <option value="all">جميع الأقسام</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}">{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="date-range" class="form-label">الفترة الزمنية</label>
                                <select class="form-select" id="date-range" name="date_range">
                                    <option value="month">الشهر الحالي</option>
                                    <option value="quarter">الربع الحالي</option>
                                    <option value="year">السنة الحالية</option>
                                    <option value="custom">فترة مخصصة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" id="generate-report" class="btn btn-primary">
                                <i class="fas fa-sync-alt me-1"></i>
                                إنشاء التقرير
                            </button>
                        </div>
                    </div>

                    <div id="custom-date-range" class="row mt-3" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start-date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start-date" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end-date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end-date" name="end_date">
                            </div>
                        </div>
                    </div>

                    <div id="additional-filters" class="row mt-3" style="display: none;">
                        <div class="col-md-12">
                            <h5>خيارات متقدمة</h5>
                        </div>

                        <!-- خيارات تقرير الإجازات -->
                        <div id="leaves-filters" class="col-md-12" style="display: none;">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="leave-type" class="form-label">نوع الإجازة</label>
                                        <select class="form-select" id="leave-type" name="leave_type">
                                            <option value="all">جميع الأنواع</option>
                                            <option value="annual">إجازة اعتيادية</option>
                                            <option value="sick">إجازة مرضية</option>
                                            <option value="emergency">إجازة اضطرارية</option>
                                            <option value="unpaid">إجازة بدون راتب</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="leave-status" class="form-label">حالة الإجازة</label>
                                        <select class="form-select" id="leave-status" name="leave_status">
                                            <option value="all">جميع الحالات</option>
                                            <option value="pending">قيد الانتظار</option>
                                            <option value="approved">تمت الموافقة</option>
                                            <option value="rejected">مرفوض</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="min-days" class="form-label">الحد الأدنى للأيام</label>
                                        <input type="number" class="form-control" id="min-days" name="min_days" min="1" value="1">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- خيارات تقرير الشفتات -->
                        <div id="shifts-filters" class="col-md-12" style="display: none;">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="shift-type" class="form-label">نوع الشفت</label>
                                        <select class="form-select" id="shift-type" name="shift_type">
                                            <option value="all">جميع الأنواع</option>
                                            <option value="morning">صباحي</option>
                                            <option value="evening">مسائي</option>
                                            <option value="night">ليلي</option>
                                            <option value="split">فترتين</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="day-type" class="form-label">نوع اليوم</label>
                                        <select class="form-select" id="day-type" name="day_type">
                                            <option value="all">جميع الأيام</option>
                                            <option value="weekday">أيام العمل</option>
                                            <option value="weekend">عطلة نهاية الأسبوع</option>
                                            <option value="holiday">العطل الرسمية</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="include-swaps" name="include_swaps">
                                            <label class="form-check-label" for="include-swaps">
                                                تضمين التبديلات
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- نتائج التقرير -->
        <div class="card mb-4" id="report-results" style="display: none;">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-chart-bar me-1"></i>
                    <span id="report-title">نتائج التقرير</span>
                </div>
                <div>
                    <div class="dropdown">
                        <button class="btn btn-light btn-sm dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item export-option" href="#" data-format="pdf"><i class="fas fa-file-pdf me-2 text-danger"></i>PDF</a></li>
                            <li><a class="dropdown-item export-option" href="#" data-format="excel"><i class="fas fa-file-excel me-2 text-success"></i>Excel</a></li>
                            <li><a class="dropdown-item export-option" href="#" data-format="csv"><i class="fas fa-file-csv me-2 text-primary"></i>CSV</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item export-option" href="#" data-format="print"><i class="fas fa-print me-2"></i>طباعة</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- ملخص التقرير -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 id="summary-total" class="display-4">0</h3>
                                <p class="text-muted">إجمالي السجلات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 id="summary-approved" class="display-4 text-success">0</h3>
                                <p class="text-muted">تمت الموافقة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 id="summary-pending" class="display-4 text-warning">0</h3>
                                <p class="text-muted">قيد الانتظار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 id="summary-rejected" class="display-4 text-danger">0</h3>
                                <p class="text-muted">مرفوض</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسم البياني -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <canvas id="report-chart" height="300"></canvas>
                    </div>
                </div>

                <!-- جدول البيانات -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="report-table">
                        <thead id="report-table-head">
                            <!-- سيتم إنشاء رؤوس الجدول ديناميكياً -->
                        </thead>
                        <tbody id="report-table-body">
                            <!-- سيتم إنشاء صفوف الجدول ديناميكياً -->
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span>عرض <span id="page-start">1</span>-<span id="page-end">10</span> من <span id="total-records">0</span> سجل</span>
                    </div>
                    <div>
                        <nav aria-label="Page navigation">
                            <ul class="pagination">
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                <li class="page-item"><a class="page-link" href="#">2</a></li>
                                <li class="page-item"><a class="page-link" href="#">3</a></li>
                                <li class="page-item">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدولة التقارير -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar-alt me-1"></i>
                جدولة التقارير
            </div>
            <div class="card-body">
                <form id="schedule-form">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="schedule-report" class="form-label">التقرير</label>
                                <select class="form-select" id="schedule-report" name="schedule_report">
                                    <option value="leaves">تقرير الإجازات</option>
                                    <option value="attendance">تقرير الحضور والغياب</option>
                                    <option value="shifts">تقرير الشفتات</option>
                                    <option value="coverage">تقرير التغطيات</option>
                                    <option value="employee">تقرير الموظفين</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="schedule-frequency" class="form-label">التكرار</label>
                                <select class="form-select" id="schedule-frequency" name="schedule_frequency">
                                    <option value="daily">يومي</option>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="monthly" selected>شهري</option>
                                    <option value="quarterly">ربع سنوي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="schedule-format" class="form-label">صيغة التقرير</label>
                                <select class="form-select" id="schedule-format" name="schedule_format">
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="schedule-email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="schedule-email" name="schedule_email" placeholder="أدخل البريد الإلكتروني لاستلام التقرير">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="schedule-name" class="form-label">اسم الجدولة</label>
                                <input type="text" class="form-control" id="schedule-name" name="schedule_name" placeholder="أدخل اسماً وصفياً للجدولة">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calendar-plus me-1"></i>
                        جدولة التقرير
                    </button>
                </form>

                <hr>

                <h5 class="mt-4">التقارير المجدولة</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم الجدولة</th>
                                <th>التقرير</th>
                                <th>التكرار</th>
                                <th>البريد الإلكتروني</th>
                                <th>آخر إرسال</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>تقرير الإجازات الشهري</td>
                                <td>تقرير الإجازات</td>
                                <td>شهري</td>
                                <td><EMAIL></td>
                                <td>01/06/2023</td>
                                <td>
                                    <button class="btn btn-sm btn-danger delete-schedule" data-id="1">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>تقرير الحضور الأسبوعي</td>
                                <td>تقرير الحضور والغياب</td>
                                <td>أسبوعي</td>
                                <td><EMAIL></td>
                                <td>15/06/2023</td>
                                <td>
                                    <button class="btn btn-sm btn-danger delete-schedule" data-id="2">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء حقول التاريخ المخصصة
        document.getElementById('date-range').addEventListener('change', function() {
            const customDateRange = document.getElementById('custom-date-range');
            if (this.value === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
            }
        });

        // إظهار/إخفاء الخيارات المتقدمة حسب نوع التقرير
        document.getElementById('report-type').addEventListener('change', function() {
            const additionalFilters = document.getElementById('additional-filters');
            const leavesFilters = document.getElementById('leaves-filters');
            const shiftsFilters = document.getElementById('shifts-filters');

            additionalFilters.style.display = 'flex';

            if (this.value === 'leaves') {
                leavesFilters.style.display = 'block';
                shiftsFilters.style.display = 'none';
            } else if (this.value === 'shifts') {
                leavesFilters.style.display = 'none';
                shiftsFilters.style.display = 'block';
            } else {
                leavesFilters.style.display = 'none';
                shiftsFilters.style.display = 'none';
            }
        });

        // إنشاء التقرير
        document.getElementById('generate-report').addEventListener('click', function() {
            const reportType = document.getElementById('report-type').value;
            const reportResults = document.getElementById('report-results');
            const reportTitle = document.getElementById('report-title');

            // عرض قسم نتائج التقرير
            reportResults.style.display = 'block';

            // تعيين عنوان التقرير
            if (reportType === 'leaves') {
                reportTitle.textContent = 'تقرير الإجازات';
            } else if (reportType === 'attendance') {
                reportTitle.textContent = 'تقرير الحضور والغياب';
            } else if (reportType === 'shifts') {
                reportTitle.textContent = 'تقرير الشفتات';
            } else if (reportType === 'coverage') {
                reportTitle.textContent = 'تقرير التغطيات';
            } else if (reportType === 'employee') {
                reportTitle.textContent = 'تقرير الموظفين';
            }

            // تحديث ملخص التقرير (بيانات تجريبية)
            document.getElementById('summary-total').textContent = '120';
            document.getElementById('summary-approved').textContent = '85';
            document.getElementById('summary-pending').textContent = '25';
            document.getElementById('summary-rejected').textContent = '10';

            // إنشاء رؤوس الجدول حسب نوع التقرير
            const tableHead = document.getElementById('report-table-head');
            const tableBody = document.getElementById('report-table-body');

            if (reportType === 'leaves') {
                tableHead.innerHTML = `
                    <tr>
                        <th>الموظف</th>
                        <th>نوع الإجازة</th>
                        <th>من تاريخ</th>
                        <th>إلى تاريخ</th>
                        <th>عدد الأيام</th>
                        <th>الحالة</th>
                        <th>تاريخ الطلب</th>
                    </tr>
                `;

                // إنشاء بيانات تجريبية
                tableBody.innerHTML = '';
                for (let i = 1; i <= 10; i++) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>موظف ${i}</td>
                        <td>إجازة اعتيادية</td>
                        <td>01/07/2023</td>
                        <td>05/07/2023</td>
                        <td>5</td>
                        <td><span class="badge bg-success">تمت الموافقة</span></td>
                        <td>15/06/2023</td>
                    `;
                    tableBody.appendChild(row);
                }
            } else if (reportType === 'shifts') {
                tableHead.innerHTML = `
                    <tr>
                        <th>الموظف</th>
                        <th>التاريخ</th>
                        <th>اليوم</th>
                        <th>الشفت</th>
                        <th>وقت البداية</th>
                        <th>وقت النهاية</th>
                        <th>الحالة</th>
                    </tr>
                `;

                // إنشاء بيانات تجريبية
                tableBody.innerHTML = '';
                for (let i = 1; i <= 10; i++) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>موظف ${i}</td>
                        <td>0${i}/07/2023</td>
                        <td>الأحد</td>
                        <td>صباحي</td>
                        <td>08:00</td>
                        <td>16:00</td>
                        <td><span class="badge bg-success">حاضر</span></td>
                    `;
                    tableBody.appendChild(row);
                }
            }

            // تحديث عدد السجلات
            document.getElementById('total-records').textContent = '120';
            document.getElementById('page-start').textContent = '1';
            document.getElementById('page-end').textContent = '10';

            // إنشاء الرسم البياني
            const ctx = document.getElementById('report-chart').getContext('2d');

            // تدمير الرسم البياني السابق إذا كان موجوداً
            if (window.reportChart) {
                window.reportChart.destroy();
            }

            if (reportType === 'leaves') {
                window.reportChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'إجازة اعتيادية',
                            data: [12, 19, 3, 5, 2, 3],
                            backgroundColor: '#4e73df'
                        }, {
                            label: 'إجازة مرضية',
                            data: [5, 10, 8, 7, 12, 9],
                            backgroundColor: '#1cc88a'
                        }, {
                            label: 'إجازة اضطرارية',
                            data: [2, 4, 1, 3, 5, 2],
                            backgroundColor: '#36b9cc'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: 'توزيع الإجازات حسب النوع والشهر',
                                font: {
                                    family: 'Tajawal'
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            },
                            x: {
                                ticks: {
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            }
                        }
                    }
                });
            } else if (reportType === 'shifts') {
                window.reportChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: ['صباحي', 'مسائي', 'ليلي', 'فترتين'],
                        datasets: [{
                            data: [45, 30, 20, 5],
                            backgroundColor: [
                                '#4e73df',
                                '#1cc88a',
                                '#36b9cc',
                                '#f6c23e'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: 'توزيع الشفتات حسب النوع',
                                font: {
                                    family: 'Tajawal'
                                }
                            }
                        }
                    }
                });
            } else {
                // إنشاء رسم بياني افتراضي لأنواع التقارير الأخرى
                window.reportChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'البيانات',
                            data: [12, 19, 3, 5, 2, 3],
                            backgroundColor: '#4e73df'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: 'بيانات التقرير',
                                font: {
                                    family: 'Tajawal'
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            },
                            x: {
                                ticks: {
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });

        // تصدير التقرير
        document.querySelectorAll('.export-option').forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const format = this.getAttribute('data-format');
                alert(`سيتم تصدير التقرير بصيغة ${format} قريباً`);
            });
        });

        // جدولة التقرير
        document.getElementById('schedule-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم جدولة التقرير بنجاح');
            this.reset();
        });

        // حذف جدولة
        document.querySelectorAll('.delete-schedule').forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذه الجدولة؟')) {
                    alert(`تم حذف الجدولة رقم ${id} بنجاح`);
                    this.closest('tr').remove();
                }
            });
        });
    });
</script>
{% endblock %}
