{% extends "base.html" %}

{% block title %}ALEMIS - إدارة الأقسام{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">إدارة الأقسام</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-building me-1"></i>
                    قائمة الأقسام
                </div>
                {% if user.role in ['admin', 'hr'] %}
                <a href="{{ url_for('new_department') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    إضافة قسم جديد
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="row">
                    {% for department in departments %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 department-card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">{{ department.name }}</h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text">{{ department.description }}</p>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <strong>المدير:</strong>
                                        {% if department.manager_first_name %}
                                        {{ department.manager_first_name }} {{ department.manager_last_name }}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <span class="department-employee-count">{{ department.employee_count }}</span>
                                        <small class="text-muted">موظف</small>
                                    </div>
                                </div>
                                {% if user.role in ['admin', 'hr'] %}
                                <div class="text-end">
                                    <a href="{{ url_for('edit_department', dept_id=department.id) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
