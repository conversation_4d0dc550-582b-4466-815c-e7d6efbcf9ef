{% extends "base.html" %}

{% block title %}ALEMIS - التقارير التفاعلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">التقارير التفاعلية</h2>
        
        <!-- أدوات التصفية -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-1"></i>
                تصفية البيانات
            </div>
            <div class="card-body">
                <form id="filter-form">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="report-type" class="form-label">نوع التقرير</label>
                                <select class="form-select" id="report-type" name="report_type">
                                    <option value="leaves">الإجازات</option>
                                    <option value="coverage">التغطيات</option>
                                    <option value="shifts">الشفتات</option>
                                    <option value="attendance">الحضور والغياب</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="department" class="form-label">القسم</label>
                                <select class="form-select" id="department" name="department">
                                    <option value="all">جميع الأقسام</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}">{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="date-range" class="form-label">الفترة الزمنية</label>
                                <select class="form-select" id="date-range" name="date_range">
                                    <option value="month">الشهر الحالي</option>
                                    <option value="quarter">الربع الحالي</option>
                                    <option value="year">السنة الحالية</option>
                                    <option value="custom">فترة مخصصة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" id="update-report" class="btn btn-primary">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث التقرير
                            </button>
                        </div>
                    </div>
                    
                    <div id="custom-date-range" class="row mt-3" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start-date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start-date" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end-date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end-date" name="end_date">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- لوحة المعلومات -->
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-pie me-1"></i>
                        <span id="chart1-title">توزيع الإجازات حسب النوع</span>
                    </div>
                    <div class="card-body">
                        <canvas id="chart1" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-bar me-1"></i>
                        <span id="chart2-title">الإجازات الشهرية</span>
                    </div>
                    <div class="card-body">
                        <canvas id="chart2" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-line me-1"></i>
                        <span id="chart3-title">اتجاهات الإجازات على مدار العام</span>
                    </div>
                    <div class="card-body">
                        <canvas id="chart3" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- مؤشرات الأداء الرئيسية -->
        <div class="row">
            <div class="col-md-3">
                <div class="card mb-4 text-center">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-calendar-check me-1"></i>
                        إجمالي الإجازات
                    </div>
                    <div class="card-body">
                        <h3 id="kpi1" class="display-4">0</h3>
                        <p class="text-muted">خلال الفترة المحددة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card mb-4 text-center">
                    <div class="card-header bg-success text-white">
                        <i class="fas fa-percentage me-1"></i>
                        نسبة الموافقة
                    </div>
                    <div class="card-body">
                        <h3 id="kpi2" class="display-4">0%</h3>
                        <p class="text-muted">على طلبات الإجازات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card mb-4 text-center">
                    <div class="card-header bg-info text-white">
                        <i class="fas fa-clock me-1"></i>
                        متوسط وقت الموافقة
                    </div>
                    <div class="card-body">
                        <h3 id="kpi3" class="display-4">0</h3>
                        <p class="text-muted">ساعة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card mb-4 text-center">
                    <div class="card-header bg-warning text-white">
                        <i class="fas fa-user-clock me-1"></i>
                        متوسط أيام الإجازة
                    </div>
                    <div class="card-body">
                        <h3 id="kpi4" class="display-4">0</h3>
                        <p class="text-muted">يوم لكل موظف</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- خيارات التصدير -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-file-export me-1"></i>
                تصدير التقرير
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="export-format" class="form-label">صيغة التصدير</label>
                            <select class="form-select" id="export-format">
                                <option value="pdf">PDF</option>
                                <option value="excel">Excel</option>
                                <option value="csv">CSV</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="button" id="export-report" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>
                            تصدير التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء حقول التاريخ المخصصة
        document.getElementById('date-range').addEventListener('change', function() {
            const customDateRange = document.getElementById('custom-date-range');
            if (this.value === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
            }
        });
        
        // تهيئة الرسوم البيانية
        const ctx1 = document.getElementById('chart1').getContext('2d');
        const ctx2 = document.getElementById('chart2').getContext('2d');
        const ctx3 = document.getElementById('chart3').getContext('2d');
        
        // رسم بياني دائري - توزيع الإجازات حسب النوع
        const chart1 = new Chart(ctx1, {
            type: 'pie',
            data: {
                labels: ['سنوية', 'مرضية', 'اضطرارية', 'بدون راتب', 'أخرى'],
                datasets: [{
                    data: [12, 8, 5, 2, 3],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#dda20a',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
        
        // رسم بياني شريطي - الإجازات الشهرية
        const chart2 = new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'عدد الإجازات',
                    data: [15, 10, 12, 8, 20, 18],
                    backgroundColor: '#4e73df',
                    borderColor: '#4e73df',
                    borderWidth: 1
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
        
        // رسم بياني خطي - اتجاهات الإجازات
        const chart3 = new Chart(ctx3, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'إجازات سنوية',
                    data: [5, 7, 10, 8, 12, 15, 18, 20, 15, 10, 8, 12],
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    fill: true,
                    tension: 0.3
                }, {
                    label: 'إجازات مرضية',
                    data: [3, 2, 4, 6, 5, 3, 2, 4, 7, 8, 5, 3],
                    borderColor: '#1cc88a',
                    backgroundColor: 'rgba(28, 200, 138, 0.1)',
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
        
        // تحديث التقرير
        document.getElementById('update-report').addEventListener('click', function() {
            // هنا يمكن إضافة كود لجلب البيانات من الخادم وتحديث الرسوم البيانية
            
            // تحديث مؤشرات الأداء الرئيسية (بيانات تجريبية)
            document.getElementById('kpi1').textContent = '45';
            document.getElementById('kpi2').textContent = '85%';
            document.getElementById('kpi3').textContent = '6';
            document.getElementById('kpi4').textContent = '12';
            
            // تنبيه المستخدم
            alert('تم تحديث التقرير بنجاح');
        });
        
        // تصدير التقرير
        document.getElementById('export-report').addEventListener('click', function() {
            const format = document.getElementById('export-format').value;
            alert(`سيتم تصدير التقرير بصيغة ${format} قريبًا`);
        });
    });
</script>
{% endblock %}
