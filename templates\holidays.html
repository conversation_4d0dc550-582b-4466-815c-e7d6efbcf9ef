{% extends "base.html" %}

{% block title %}ALEMIS - العطل الرسمية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4 title-with-red-line">العطل الرسمية</h2>
        <div class="red-line-animated"></div>
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-calendar-alt me-1"></i>
                    قائمة العطل الرسمية
                </div>
                {% if user.role in ['admin', 'hr', 'gm'] %}
                <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
                    <i class="fas fa-plus me-1"></i>
                    إضافة عطلة جديدة
                </button>
                {% endif %}
            </div>
            <div class="card-body">
                <!-- أدوات البحث والتصفية -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="بحث عن عطلة...">
                            <button class="btn btn-primary" type="button" id="searchButton">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <select class="form-select" id="yearFilter">
                            <option value="all">جميع السنوات</option>
                            {% for year in years %}
                            <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- جدول العطل الرسمية -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="holidaysTable">
                        <thead>
                            <tr>
                                <th>اسم العطلة</th>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>متكررة سنوياً</th>
                                {% if user.role in ['admin', 'hr', 'gm'] %}
                                <th>الإجراءات</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for holiday in holidays %}
                            <tr>
                                <td>{{ holiday.name }}</td>
                                <td>{{ holiday.date }}</td>
                                <td>{{ holiday.description }}</td>
                                <td>
                                    {% if holiday.recurring %}
                                    <span class="badge bg-success">نعم</span>
                                    {% else %}
                                    <span class="badge bg-secondary">لا</span>
                                    {% endif %}
                                </td>
                                {% if user.role in ['admin', 'hr', 'gm'] %}
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#editHolidayModal{{ holiday.id }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="{{ url_for('delete_holiday', holiday_id=holiday.id) }}" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذه العطلة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- ملخص العطل -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي العطل</h5>
                                <h2 class="mb-0">{{ holidays|length }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">العطل المتكررة</h5>
                                <h2 class="mb-0">
                                    {% set recurring_count = 0 %}
                                    {% for holiday in holidays %}
                                        {% if holiday.recurring %}
                                            {% set recurring_count = recurring_count + 1 %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ recurring_count }}
                                </h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">عطل العام الحالي</h5>
                                <h2 class="mb-0">
                                    {% set current_year_count = 0 %}
                                    {% for holiday in holidays %}
                                        {% if holiday.date.startswith(current_year|string) or holiday.recurring %}
                                            {% set current_year_count = current_year_count + 1 %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ current_year_count }}
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal: Add Holiday -->
{% if user.role in ['admin', 'hr', 'gm'] %}
<div class="modal fade" id="addHolidayModal" tabindex="-1" aria-labelledby="addHolidayModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addHolidayModalLabel">إضافة عطلة رسمية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" action="{{ url_for('add_holiday') }}" id="addHolidayForm">
                    <div class="mb-3">
                        <label for="holiday_name" class="form-label">اسم العطلة</label>
                        <input type="text" class="form-control" id="holiday_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="holiday_date" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="holiday_date" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="holiday_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="holiday_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="holiday_recurring" name="recurring">
                            <label class="form-check-label" for="holiday_recurring">
                                متكررة سنوياً
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="addHolidayForm" class="btn btn-primary">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Modals: Edit Holiday -->
{% for holiday in holidays %}
<div class="modal fade" id="editHolidayModal{{ holiday.id }}" tabindex="-1" aria-labelledby="editHolidayModalLabel{{ holiday.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editHolidayModalLabel{{ holiday.id }}">تعديل عطلة رسمية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" action="{{ url_for('edit_holiday', holiday_id=holiday.id) }}" id="editHolidayForm{{ holiday.id }}">
                    <div class="mb-3">
                        <label for="edit_holiday_name{{ holiday.id }}" class="form-label">اسم العطلة</label>
                        <input type="text" class="form-control" id="edit_holiday_name{{ holiday.id }}" name="name" value="{{ holiday.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_holiday_date{{ holiday.id }}" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="edit_holiday_date{{ holiday.id }}" name="date" value="{{ holiday.date }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_holiday_description{{ holiday.id }}" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_holiday_description{{ holiday.id }}" name="description" rows="3">{{ holiday.description }}</textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_holiday_recurring{{ holiday.id }}" name="recurring" {% if holiday.recurring %}checked{% endif %}>
                            <label class="form-check-label" for="edit_holiday_recurring{{ holiday.id }}">
                                متكررة سنوياً
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="editHolidayForm{{ holiday.id }}" class="btn btn-primary">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // البحث في جدول العطل
        const searchInput = document.getElementById('searchInput');
        const searchButton = document.getElementById('searchButton');
        const yearFilter = document.getElementById('yearFilter');
        const holidaysTable = document.getElementById('holidaysTable');
        const tableRows = holidaysTable.querySelectorAll('tbody tr');

        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const yearValue = yearFilter.value;

            tableRows.forEach(row => {
                const name = row.cells[0].textContent.toLowerCase();
                const date = row.cells[1].textContent;
                const description = row.cells[2].textContent.toLowerCase();
                const recurring = row.cells[3].textContent.includes('نعم');

                const yearMatch = yearValue === 'all' || date.startsWith(yearValue) || recurring;
                const searchMatch = name.includes(searchTerm) || description.includes(searchTerm);

                if (searchMatch && yearMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        searchButton.addEventListener('click', filterTable);
        searchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                filterTable();
            }
        });

        yearFilter.addEventListener('change', filterTable);
    });
</script>
{% endblock %}
