/* Custom CSS for ALEMIS - Enhanced Design */

/* Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

:root {
    --primary-color: #60a5fa;
    --primary-dark: #3b82f6;
    --primary-light: #93c5fd;
    --secondary-color: #E30613;
    --secondary-dark: #b10510;
    --secondary-light: #ff1a27;
    --warning-color: #f59e0b;
    --danger-color: #E30613;
    --info-color: #60a5fa;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --gray-color: #64748b;
    --gray-light: #e2e8f0;
    --body-bg: #f1f5f9;
    --card-bg: #ffffff;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --border-radius: 0.5rem;
    --transition-speed: 0.3s;
}

body {
    font-family: 'Tajawal', 'Cairo', sans-serif;
    background-color: var(--body-bg);
    background-image:
        linear-gradient(rgba(241, 245, 249, 0.9), rgba(241, 245, 249, 0.9)),
        repeating-linear-gradient(45deg, rgba(96, 165, 250, 0.03) 0px, rgba(96, 165, 250, 0.03) 2px, transparent 2px, transparent 10px),
        repeating-linear-gradient(-45deg, rgba(227, 6, 19, 0.03) 0px, rgba(227, 6, 19, 0.03) 2px, transparent 2px, transparent 10px);
    background-attachment: fixed;
    color: var(--dark-color);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-light);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

h2 {
    font-size: 1.75rem;
    position: relative;
    padding-bottom: 0.5rem;
}

h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

/* Navbar - تحسينات القائمة العلوية */
.navbar {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    padding: 0.75rem 0;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.75rem;
    letter-spacing: -0.5px;
    position: relative;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: translateY(-2px);
}

.navbar-dark .navbar-brand {
    color: white;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color)) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color)) !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    padding: 0.75rem 1.25rem;
    transition: all var(--transition-speed);
    position: relative;
    margin: 0 0.15rem;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.navbar-dark .navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: -1;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.navbar-dark .navbar-nav .nav-link:hover::before {
    transform: translateX(0);
}

.navbar-dark .navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: white;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-dark .navbar-nav .nav-link:hover::after {
    width: 70%;
}

.nav-link-hover {
    animation: navPulse 0.5s ease;
}

@keyframes navPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.nav-link-active {
    animation: navClick 0.3s ease;
}

@keyframes navClick {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.dropdown-menu {
    border: none;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    min-width: 200px;
    animation: dropdownFadeIn 0.3s ease;
}

/* تنسيقات قائمة الإشعارات */
.notification-dropdown {
    width: 320px;
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
}

.dropdown-header {
    font-weight: 700;
    color: var(--primary-color);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    text-align: center;
}

.notification-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item .dropdown-item {
    padding: 0.75rem 1rem;
    white-space: normal;
    display: flex;
    align-items: center;
}

.notification-item .dropdown-item i {
    font-size: 1.25rem;
    margin-left: 0.75rem;
    width: 24px;
    text-align: center;
}

.notification-text {
    font-size: 0.9rem;
    line-height: 1.4;
}

#notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    animation: pulse 2s infinite;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    padding: 0.75rem 1.25rem;
    border-radius: calc(var(--border-radius) - 0.25rem);
    transition: all var(--transition-speed);
    font-weight: 500;
    position: relative;
    margin-bottom: 0.25rem;
}

.dropdown-item:hover {
    background-color: var(--primary-light);
    color: white;
    transform: translateX(5px);
}

.dropdown-item i {
    margin-left: 0.5rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover i {
    transform: scale(1.2);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: all var(--transition-speed);
    overflow: hidden;
    background-color: var(--card-bg);
}

.card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-5px);
}

.card-header {
    border-bottom: none;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: white;
}

.card-header.bg-secondary {
    background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color)) !important;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

/* Dashboard Cards */
.card-body .fa-3x {
    opacity: 0.8;
    transition: all var(--transition-speed);
}

.card:hover .fa-3x {
    opacity: 1;
    transform: scale(1.1);
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-success {
    background-color: var(--secondary-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: all var(--transition-speed);
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(96, 165, 250, 0.3);
}

.btn-success {
    background-color: var(--secondary-color);
}

.btn-success:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(227, 6, 19, 0.3);
}

.btn-warning {
    background-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(227, 6, 19, 0.3);
}

/* تنسيقات نافذة تأكيد الحذف */
.modal-header.bg-danger {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark)) !important;
}

.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow-hover);
}

.modal-body .text-danger {
    font-weight: 600;
    background-color: rgba(239, 68, 68, 0.1);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    border-right: 3px solid var(--danger-color);
}

.btn-info {
    background-color: var(--info-color);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* تحسينات النماذج */
.form-control, .form-select {
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-light);
    transition: all var(--transition-speed);
    background-color: rgba(255, 255, 255, 0.8);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.25rem rgba(96, 165, 250, 0.25);
    background-color: white;
    transform: translateY(-2px);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    position: relative;
    display: inline-block;
}

.form-label:after {
    content: '';
    position: absolute;
    bottom: -3px;
    right: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.form-group.focused .form-label:after,
.mb-3.focused .form-label:after {
    width: 100%;
}

.form-floating > label {
    right: 0;
    left: auto;
    padding-right: 1rem;
}

/* تحسينات خانات الاختيار */
.checkbox-enhanced .form-check-input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
    transition: all 0.3s ease;
    position: relative;
    border-color: var(--gray-color);
}

.checkbox-enhanced .form-check-input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    animation: checkboxPulse 0.3s ease;
}

@keyframes checkboxPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* تحسينات أزرار الراديو */
.radio-enhanced .form-check-input[type="radio"] {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0.125rem;
    transition: all 0.3s ease;
    position: relative;
    border-color: var(--gray-color);
}

.radio-enhanced .form-check-input[type="radio"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    animation: radioPulse 0.3s ease;
}

@keyframes radioPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* تحسينات الجداول */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.02);
}

.table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    padding: 1rem;
    border-bottom: 2px solid var(--primary-light);
    position: relative;
}

.table th:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-light), var(--primary-dark));
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-light);
    transition: all 0.2s ease;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
    background-color: rgba(0, 0, 0, 0.01);
}

.table-hover > tbody > tr:hover > * {
    background-color: rgba(96, 165, 250, 0.05);
    transform: translateX(5px);
}

/* تأثيرات الجدول المتقدمة */
.table-enhanced {
    box-shadow: var(--card-shadow);
}

.table-enhanced thead th {
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-enhanced tbody tr {
    transition: all 0.3s ease;
}

.table-enhanced tbody tr:hover {
    transform: scale(1.01);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 5;
    position: relative;
}

/* تأثيرات الفرز */
.sortable {
    cursor: pointer;
    position: relative;
}

.sortable:after {
    content: '⇅';
    position: absolute;
    right: 10px;
    opacity: 0.3;
    font-size: 0.8rem;
}

.sort-asc:after {
    content: '↑';
    opacity: 1;
}

.sort-desc:after {
    content: '↓';
    opacity: 1;
}

/* تحسينات الشارات */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 600;
    border-radius: 50rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.badge:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
}

.badge:hover:before {
    left: 100%;
}

.badge.bg-success {
    background: linear-gradient(45deg, var(--secondary-color), var(--secondary-light)) !important;
}

.badge.bg-warning {
    background: linear-gradient(45deg, var(--warning-color), #fdba74) !important;
}

.badge.bg-danger {
    background: linear-gradient(45deg, var(--danger-color), #fca5a5) !important;
}

.badge.bg-info {
    background: linear-gradient(45deg, var(--info-color), #67e8f9) !important;
}

.badge.bg-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light)) !important;
}

/* تحسينات التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1.25rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    animation: alertSlideIn 0.5s ease;
}

@keyframes alertSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
}

.alert-success:before {
    background-color: var(--secondary-color);
}

.alert-warning:before {
    background-color: var(--warning-color);
}

.alert-danger:before {
    background-color: var(--danger-color);
}

.alert-info:before {
    background-color: var(--info-color);
}

.alert-primary:before {
    background-color: var(--primary-color);
}

.alert-dismissible .btn-close {
    padding: 1.25rem;
    transition: all 0.3s ease;
}

.alert-dismissible .btn-close:hover {
    transform: rotate(90deg);
}

/* تم إزالة تنسيقات القائمة الجانبية */

/* تحسينات تذييل الصفحة */
footer {
    background: linear-gradient(to right, var(--light-color), #f8fafc);
    margin-top: 3rem;
    box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

footer:before {
    content: '';
    position: absolute;
    top: -3px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color), var(--info-color));
}

footer h5 {
    font-weight: 700;
    margin-bottom: 1.25rem;
    position: relative;
    display: inline-block;
}

footer h5:after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

footer a {
    color: var(--dark-color);
    text-decoration: none;
    transition: all var(--transition-speed);
    display: block;
    padding: 0.25rem 0;
    position: relative;
}

footer a:before {
    content: '→';
    opacity: 0;
    margin-left: 5px;
    transition: all 0.3s ease;
    transform: translateX(10px);
    display: inline-block;
}

footer a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

footer a:hover:before {
    opacity: 1;
    transform: translateX(0);
}

.footer-bottom {
    background-color: rgba(0, 0, 0, 0.03);
    padding: 1rem 0;
    text-align: center;
    font-weight: 500;
}

/* RTL specific adjustments */
.dropdown-menu-end {
    left: 0;
    right: auto;
}

.me-1, .me-2, .me-3, .me-4 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.ms-1, .ms-2, .ms-3, .ms-4 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

.me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

.ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

/* تأثيرات الانتقال والحركة */
.page-loaded {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-out {
    animation: fadeOut 0.5s ease-in-out;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.card-hover {
    transform: translateY(-8px);
    box-shadow: var(--card-shadow-hover);
}

.btn-clicked {
    animation: btnPulse 0.3s ease-in-out;
}

@keyframes btnPulse {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.btn-hover {
    animation: btnHover 0.5s ease;
}

@keyframes btnHover {
    0% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
    100% { transform: translateY(0); }
}

.btn-group .btn {
    margin: 0 2px;
    position: relative;
    overflow: hidden;
}

.btn-group .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1;
}

.btn-group .btn:hover::before {
    transform: translateX(0);
}

.btn-group .btn i {
    position: relative;
    z-index: 2;
}

/* تحسينات صفحة تسجيل الدخول */
.login-container {
    max-width: 450px;
    margin: 5rem auto;
    padding: 2.5rem;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    animation: slideUp 0.5s ease-in-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-logo {
    text-align: center;
    margin-bottom: 2rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.login-logo img {
    max-width: 150px;
    margin-bottom: 1rem;
}

.login-title {
    text-align: center;
    margin-bottom: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    position: relative;
}

.login-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

/* تحسينات حقول التاريخ */
.date-enhanced {
    position: relative;
    padding-right: 2.5rem !important;
}

.date-icon {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    color: var(--gray-color);
    pointer-events: none;
    transition: all 0.3s ease;
}

.date-focused .date-icon {
    color: var(--primary-color);
    transform: translateY(-50%) scale(1.2);
}

/* تأثيرات التمرير */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.scroll-reveal-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.8s ease;
}

.scroll-reveal-left.revealed {
    opacity: 1;
    transform: translateX(0);
}

.scroll-reveal-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.8s ease;
}

.scroll-reveal-right.revealed {
    opacity: 1;
    transform: translateX(0);
}

.scroll-reveal-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s ease;
}

.scroll-reveal-scale.revealed {
    opacity: 1;
    transform: scale(1);
}

/* تحسينات لوحة التحكم */
.dashboard-stats .card {
    transition: transform var(--transition-speed);
    position: relative;
    overflow: hidden;
}

.dashboard-stats .card:hover {
    transform: translateY(-5px);
}

.dashboard-stats .card:after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.dashboard-stats .card:hover:after {
    opacity: 1;
}

/* Custom styles for profile page */
.profile-header {
    background-color: var(--light-color);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 2.5rem;
    box-shadow: var(--card-shadow);
}

.profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Custom styles for calendar */
.calendar-container {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
}

/* أنماط يوم الجمعة */
.bg-warning-subtle {
    background-color: #fff3cd !important;
}

/* أنماط يوم الخميس */
.bg-light {
    background-color: #f0f8ff !important;
}

.calendar-day {
    height: 120px;
    border: 1px solid var(--gray-light);
    padding: 0.75rem;
    transition: all var(--transition-speed);
    overflow-y: auto;
}

.calendar-day:hover {
    background-color: rgba(96, 165, 250, 0.05);
}

.calendar-day.today {
    background-color: rgba(96, 165, 250, 0.1);
    border: 2px solid var(--primary-color);
}

.calendar-day.has-leave {
    background-color: rgba(16, 185, 129, 0.1);
}

.calendar-day.has-holiday {
    background-color: rgba(239, 68, 68, 0.1);
}

.calendar-event {
    padding: 0.25rem 0.5rem;
    margin-bottom: 0.25rem;
    border-radius: calc(var(--border-radius) - 0.25rem);
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.calendar-event.leave {
    background-color: rgba(16, 185, 129, 0.2);
    border-left: 3px solid var(--secondary-color);
}

.calendar-event.holiday {
    background-color: rgba(239, 68, 68, 0.2);
    border-left: 3px solid var(--danger-color);
}

.calendar-event.permission {
    background-color: rgba(245, 158, 11, 0.2);
    border-left: 3px solid var(--warning-color);
}

/* Custom styles for reports */
.report-filter {
    background-color: var(--light-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
}

.report-chart {
    height: 350px;
    margin-bottom: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.5rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .card-body h2 {
        font-size: 1.8rem;
    }

    .calendar-day {
        height: 100px;
    }
}
