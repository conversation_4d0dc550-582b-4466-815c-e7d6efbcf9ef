{% extends 'base.html' %}

{% block title %}لوحة التحكم الإشرافية{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">لوحة التحكم الإشرافية</h1>
    
    <div class="row">
        <!-- Statistics Cards -->
        <div class="col-md-12 mb-4">
            <div class="row">
                <div class="col-md-4">
                    <div class="card text-white bg-primary mb-3">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي الموظفين</h5>
                            <p class="card-text display-4">{{ employee_count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-white bg-success mb-3">
                        <div class="card-body">
                            <h5 class="card-title">الموظفين في إجازة اليوم</h5>
                            <p class="card-text display-4">{{ on_leave_count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-white bg-info mb-3">
                        <div class="card-body">
                            <h5 class="card-title">طلبات معلقة</h5>
                            <p class="card-text display-4">{{ pending_leave_requests|length + pending_coverage_requests|length + pending_shift_swaps|length }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Pending Leave Requests -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">طلبات الإجازة المعلقة</h5>
                </div>
                <div class="card-body">
                    {% if pending_leave_requests %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    {% if user.role in ['admin', 'hr', 'gm'] %}
                                    <th>القسم</th>
                                    {% endif %}
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>السبب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in pending_leave_requests %}
                                <tr>
                                    <td>{{ request.first_name }} {{ request.last_name }}</td>
                                    {% if user.role in ['admin', 'hr', 'gm'] %}
                                    <td>{{ request.department_name }}</td>
                                    {% endif %}
                                    <td>{{ request.leave_type_name }}</td>
                                    <td>{{ request.start_date }}</td>
                                    <td>{{ request.end_date }}</td>
                                    <td>{{ request.reason }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-success approve-leave" data-id="{{ request.id }}">موافقة</button>
                                        <button class="btn btn-sm btn-danger reject-leave" data-id="{{ request.id }}">رفض</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">لا توجد طلبات إجازة معلقة</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Pending Coverage Requests -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">طلبات التغطية المعلقة</h5>
                </div>
                <div class="card-body">
                    {% if pending_coverage_requests %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    {% if user.role in ['admin', 'hr', 'gm'] %}
                                    <th>القسم</th>
                                    {% endif %}
                                    <th>تاريخ التغطية</th>
                                    <th>الشفت</th>
                                    <th>السبب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in pending_coverage_requests %}
                                <tr>
                                    <td>{{ request.first_name }} {{ request.last_name }}</td>
                                    {% if user.role in ['admin', 'hr', 'gm'] %}
                                    <td>{{ request.department_name }}</td>
                                    {% endif %}
                                    <td>{{ request.coverage_date }}</td>
                                    <td>{{ request.shift_type }}</td>
                                    <td>{{ request.reason }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-success approve-coverage" data-id="{{ request.id }}">موافقة</button>
                                        <button class="btn btn-sm btn-danger reject-coverage" data-id="{{ request.id }}">رفض</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">لا توجد طلبات تغطية معلقة</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Pending Shift Swap Requests -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">طلبات تبديل الشفتات المعلقة</h5>
                </div>
                <div class="card-body">
                    {% if pending_shift_swaps %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>مقدم الطلب</th>
                                    <th>الموظف المطلوب التبديل معه</th>
                                    {% if user.role in ['admin', 'hr', 'gm'] %}
                                    <th>القسم</th>
                                    {% endif %}
                                    <th>نوع التبديل</th>
                                    <th>التاريخ</th>
                                    <th>السبب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in pending_shift_swaps %}
                                <tr>
                                    <td>{{ request.requester_first_name }} {{ request.requester_last_name }}</td>
                                    <td>{{ request.target_first_name }} {{ request.target_last_name }}</td>
                                    {% if user.role in ['admin', 'hr', 'gm'] %}
                                    <td>{{ request.department_name }}</td>
                                    {% endif %}
                                    <td>{{ request.swap_type }}</td>
                                    <td>{{ request.swap_date }}</td>
                                    <td>{{ request.reason }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-success approve-swap" data-id="{{ request.id }}">موافقة</button>
                                        <button class="btn btn-sm btn-danger reject-swap" data-id="{{ request.id }}">رفض</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">لا توجد طلبات تبديل شفتات معلقة</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Approve leave request
        $('.approve-leave').click(function() {
            var requestId = $(this).data('id');
            $.ajax({
                url: '/leave/approve/' + requestId,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert('تمت الموافقة على طلب الإجازة بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                }
            });
        });
        
        // Reject leave request
        $('.reject-leave').click(function() {
            var requestId = $(this).data('id');
            $.ajax({
                url: '/leave/reject/' + requestId,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert('تم رفض طلب الإجازة بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                }
            });
        });
        
        // Approve coverage request
        $('.approve-coverage').click(function() {
            var requestId = $(this).data('id');
            $.ajax({
                url: '/coverage/approve/' + requestId,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert('تمت الموافقة على طلب التغطية بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                }
            });
        });
        
        // Reject coverage request
        $('.reject-coverage').click(function() {
            var requestId = $(this).data('id');
            $.ajax({
                url: '/coverage/reject/' + requestId,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert('تم رفض طلب التغطية بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                }
            });
        });
        
        // Approve shift swap request
        $('.approve-swap').click(function() {
            var requestId = $(this).data('id');
            $.ajax({
                url: '/approve_direct_swap',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({request_id: requestId}),
                success: function(response) {
                    if (response.success) {
                        alert('تمت الموافقة على طلب تبديل الشفت بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                }
            });
        });
        
        // Reject shift swap request
        $('.reject-swap').click(function() {
            var requestId = $(this).data('id');
            $.ajax({
                url: '/reject_direct_swap',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({request_id: requestId}),
                success: function(response) {
                    if (response.success) {
                        alert('تم رفض طلب تبديل الشفت بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + response.error);
                    }
                }
            });
        });
    });
</script>
{% endblock %}
