import sqlite3

def check_tables():
    conn = sqlite3.connect('alemis.db')
    cursor = conn.cursor()
    
    # التحقق من جدول coverage_days
    print("Checking coverage_days table:")
    try:
        cursor.execute('PRAGMA table_info(coverage_days)')
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
    except Exception as e:
        print(f"Error: {e}")
    
    # التحقق من جدول schedule_assignments
    print("\nChecking schedule_assignments table:")
    try:
        cursor.execute('PRAGMA table_info(schedule_assignments)')
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
    except Exception as e:
        print(f"Error: {e}")
    
    # التحقق من جدول monthly_schedules
    print("\nChecking monthly_schedules table:")
    try:
        cursor.execute('PRAGMA table_info(monthly_schedules)')
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
    except Exception as e:
        print(f"Error: {e}")
    
    conn.close()

if __name__ == "__main__":
    check_tables()
