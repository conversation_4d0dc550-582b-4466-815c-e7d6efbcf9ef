{% extends "base.html" %}

{% block title %}موافقة على طلب الإجازة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-check-circle me-1"></i>
                    موافقة على طلب الإجازة
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>تفاصيل الطلب:</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>رقم الطلب:</strong></td>
                                    <td>{{ leave_request.id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>نوع الإجازة:</strong></td>
                                    <td>{{ leave_request.leave_type_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ البداية:</strong></td>
                                    <td>{{ leave_request.start_date }}</td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ النهاية:</strong></td>
                                    <td>{{ leave_request.end_date }}</td>
                                </tr>
                                <tr>
                                    <td><strong>السبب:</strong></td>
                                    <td>{{ leave_request.reason }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>حالة الموافقات:</h5>
                            <div class="approval-status">
                                <div class="mb-2">
                                    <span class="badge {{ 'bg-success' if leave_request.manager_approval == 1 else 'bg-secondary' }}">
                                        مدير المختبر: {{ 'تمت الموافقة' if leave_request.manager_approval == 1 else 'في الانتظار' }}
                                    </span>
                                </div>
                                <div class="mb-2">
                                    <span class="badge {{ 'bg-success' if leave_request.hr_approval == 1 else 'bg-secondary' }}">
                                        الموارد البشرية: {{ 'تمت الموافقة' if leave_request.hr_approval == 1 else 'في الانتظار' }}
                                    </span>
                                </div>
                                <div class="mb-2">
                                    <span class="badge {{ 'bg-success' if leave_request.hospital_manager_approval == 1 else 'bg-secondary' }}">
                                        مدير المستشفى: {{ 'تمت الموافقة' if leave_request.hospital_manager_approval == 1 else 'في الانتظار' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="POST">
                        <div class="row">
                            <div class="col-md-12">
                                <label for="comment" class="form-label">تعليق أو ملاحظة (اختياري)</label>
                                <textarea class="form-control" id="comment" name="comment" rows="3" 
                                          placeholder="أضف تعليقاً أو ملاحظة للموظف..."></textarea>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-success me-2">
                                    <i class="fas fa-check me-1"></i>
                                    موافقة
                                </button>
                                <a href="{{ url_for('leave_requests') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
