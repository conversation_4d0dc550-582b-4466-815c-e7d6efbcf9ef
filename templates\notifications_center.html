{% extends "base.html" %}

{% block title %}ALEMIS - مركز الإشعارات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4 title-with-red-line">
            <i class="fas fa-bell me-2"></i>
            مركز الإشعارات
        </h2>
        <div class="red-line-animated"></div>
    </div>
</div>

<!-- فلاتر الإشعارات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom">
                <i class="fas fa-filter me-1"></i>
                فلترة الإشعارات
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="notification-type" class="form-label">نوع الإشعار</label>
                        <select class="form-select" id="notification-type">
                            <option value="">جميع الأنواع</option>
                            <option value="leave_request">طلبات الإجازة</option>
                            <option value="coverage_request">طلبات التغطية</option>
                            <option value="shift_swap">تبديل الدوام</option>
                            <option value="urgent">عاجل</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="notification-status" class="form-label">الحالة</label>
                        <select class="form-select" id="notification-status">
                            <option value="">جميع الحالات</option>
                            <option value="unread">غير مقروءة</option>
                            <option value="read">مقروءة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="notification-priority" class="form-label">الأولوية</label>
                        <select class="form-select" id="notification-priority">
                            <option value="">جميع الأولويات</option>
                            <option value="high">عالية</option>
                            <option value="medium">متوسطة</option>
                            <option value="low">منخفضة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button class="btn btn-primary" onclick="filterNotifications()">
                                <i class="fas fa-search me-1"></i>
                                فلترة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الإشعارات -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-info text-white mb-4 card-red-accent">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-75 small">إجمالي الإشعارات</div>
                        <div class="text-lg fw-bold" id="total-notifications">{{ notifications_stats.total }}</div>
                    </div>
                    <div class="fa-2x opacity-50">
                        <i class="fas fa-bell"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4 card-red-accent">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-75 small">غير مقروءة</div>
                        <div class="text-lg fw-bold" id="unread-notifications">{{ notifications_stats.unread }}</div>
                    </div>
                    <div class="fa-2x opacity-50">
                        <i class="fas fa-envelope"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white mb-4 card-red-accent">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-75 small">عاجلة</div>
                        <div class="text-lg fw-bold" id="urgent-notifications">{{ notifications_stats.urgent }}</div>
                    </div>
                    <div class="fa-2x opacity-50">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4 card-red-accent">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-75 small">اليوم</div>
                        <div class="text-lg fw-bold" id="today-notifications">{{ notifications_stats.today }}</div>
                    </div>
                    <div class="fa-2x opacity-50">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الإشعارات -->
<div class="row">
    <div class="col-12">
        <div class="card card-red-accent">
            <div class="card-header bg-dark text-white red-border-bottom d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-list me-1"></i>
                    قائمة الإشعارات
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-light me-2" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-1"></i>
                        تحديد الكل كمقروء
                    </button>
                    <button class="btn btn-sm btn-outline-light" onclick="deleteAllRead()">
                        <i class="fas fa-trash me-1"></i>
                        حذف المقروءة
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="notifications-list" id="notifications-list">
                    {% for notification in notifications %}
                    <div class="notification-item {{ 'unread' if not notification.is_read else 'read' }} {{ notification.priority }}" 
                         data-id="{{ notification.id }}" 
                         data-type="{{ notification.type }}" 
                         data-priority="{{ notification.priority }}">
                        <div class="d-flex align-items-start p-3 border-bottom">
                            <div class="notification-icon me-3">
                                {% if notification.type == 'leave_request' %}
                                    <i class="fas fa-calendar-alt text-primary"></i>
                                {% elif notification.type == 'coverage_request' %}
                                    <i class="fas fa-exchange-alt text-success"></i>
                                {% elif notification.type == 'shift_swap' %}
                                    <i class="fas fa-sync-alt text-warning"></i>
                                {% elif notification.type == 'urgent' %}
                                    <i class="fas fa-exclamation-triangle text-danger"></i>
                                {% else %}
                                    <i class="fas fa-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div class="notification-content flex-grow-1">
                                <div class="notification-title fw-bold">{{ notification.title }}</div>
                                <div class="notification-message text-muted">{{ notification.message }}</div>
                                <div class="notification-meta small text-muted mt-1">
                                    <span class="me-3">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </span>
                                    <span class="priority-badge badge bg-{{ 'danger' if notification.priority == 'high' else 'warning' if notification.priority == 'medium' else 'secondary' }}">
                                        {{ 'عالية' if notification.priority == 'high' else 'متوسطة' if notification.priority == 'medium' else 'منخفضة' }}
                                    </span>
                                </div>
                            </div>
                            <div class="notification-actions">
                                {% if not notification.is_read %}
                                <button class="btn btn-sm btn-outline-primary me-1" onclick="markAsRead({{ notification.id }})">
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification({{ notification.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% if notification.action_url %}
                                <a href="{{ notification.action_url }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% if not notifications %}
                <div class="text-center p-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد إشعارات</h5>
                    <p class="text-muted">ستظهر الإشعارات الجديدة هنا</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إعدادات الإشعارات -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card card-red-accent">
            <div class="card-header bg-secondary text-white red-border-bottom">
                <i class="fas fa-cog me-1"></i>
                إعدادات الإشعارات
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="email-notifications" checked>
                            <label class="form-check-label" for="email-notifications">
                                إشعارات البريد الإلكتروني
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="push-notifications" checked>
                            <label class="form-check-label" for="push-notifications">
                                الإشعارات الفورية
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="sms-notifications">
                            <label class="form-check-label" for="sms-notifications">
                                إشعارات SMS (للحالات العاجلة)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="sound-notifications" checked>
                            <label class="form-check-label" for="sound-notifications">
                                الأصوات
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="desktop-notifications" checked>
                            <label class="form-check-label" for="desktop-notifications">
                                إشعارات سطح المكتب
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                            <label class="form-check-label" for="auto-refresh">
                                التحديث التلقائي
                            </label>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="saveNotificationSettings()">
                        <i class="fas fa-save me-1"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث الإشعارات تلقائياً كل 30 ثانية
let autoRefreshInterval;

function startAutoRefresh() {
    if (document.getElementById('auto-refresh').checked) {
        autoRefreshInterval = setInterval(loadNotifications, 30000);
    }
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
}

// تحميل الإشعارات
function loadNotifications() {
    fetch('/api/notifications')
        .then(response => response.json())
        .then(data => {
            updateNotificationsList(data.notifications);
            updateNotificationsStats(data.stats);
        })
        .catch(error => console.error('Error loading notifications:', error));
}

// تحديث قائمة الإشعارات
function updateNotificationsList(notifications) {
    // تحديث قائمة الإشعارات
    // سيتم تنفيذ هذا لاحقاً
}

// تحديث إحصائيات الإشعارات
function updateNotificationsStats(stats) {
    document.getElementById('total-notifications').textContent = stats.total;
    document.getElementById('unread-notifications').textContent = stats.unread;
    document.getElementById('urgent-notifications').textContent = stats.urgent;
    document.getElementById('today-notifications').textContent = stats.today;
}

// تحديد إشعار كمقروء
function markAsRead(notificationId) {
    fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notification = document.querySelector(`[data-id="${notificationId}"]`);
            notification.classList.remove('unread');
            notification.classList.add('read');
            notification.querySelector('.btn-outline-primary').remove();
            loadNotifications(); // تحديث الإحصائيات
        }
    });
}

// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    fetch('/api/notifications/mark-all-read', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

// حذف إشعار
function deleteNotification(notificationId) {
    if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        fetch(`/api/notifications/${notificationId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.querySelector(`[data-id="${notificationId}"]`).remove();
                loadNotifications(); // تحديث الإحصائيات
            }
        });
    }
}

// حذف جميع الإشعارات المقروءة
function deleteAllRead() {
    if (confirm('هل أنت متأكد من حذف جميع الإشعارات المقروءة؟')) {
        fetch('/api/notifications/delete-read', {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
}

// فلترة الإشعارات
function filterNotifications() {
    const type = document.getElementById('notification-type').value;
    const status = document.getElementById('notification-status').value;
    const priority = document.getElementById('notification-priority').value;
    
    const notifications = document.querySelectorAll('.notification-item');
    
    notifications.forEach(notification => {
        let show = true;
        
        if (type && notification.dataset.type !== type) show = false;
        if (status === 'read' && notification.classList.contains('unread')) show = false;
        if (status === 'unread' && notification.classList.contains('read')) show = false;
        if (priority && notification.dataset.priority !== priority) show = false;
        
        notification.style.display = show ? 'block' : 'none';
    });
}

// حفظ إعدادات الإشعارات
function saveNotificationSettings() {
    const settings = {
        email: document.getElementById('email-notifications').checked,
        push: document.getElementById('push-notifications').checked,
        sms: document.getElementById('sms-notifications').checked,
        sound: document.getElementById('sound-notifications').checked,
        desktop: document.getElementById('desktop-notifications').checked,
        autoRefresh: document.getElementById('auto-refresh').checked
    };
    
    fetch('/api/notification-settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ الإعدادات بنجاح');
            if (settings.autoRefresh) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        }
    });
}

// بدء التحديث التلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});
</script>
{% endblock %}
