{% extends "base.html" %}

{% block title %}ALEMIS - الجدول الشهري{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
<style>
    .schedule-header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        position: relative;
        overflow: hidden;
    }

    .schedule-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 6s ease-in-out infinite;
    }

    .schedule-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -5%;
        width: 150px;
        height: 150px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite reverse;
    }

    .calendar {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(14, 165, 233, 0.1);
    }

    .calendar th {
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        color: white;
        padding: 1rem;
        text-align: center;
        font-weight: 600;
        border: none;
        font-size: 1rem;
    }

    .calendar td {
        border: 1px solid rgba(14, 165, 233, 0.1);
        padding: 0.75rem;
        text-align: center;
        vertical-align: top;
        height: 120px;
        position: relative;
        transition: all 0.3s ease;
    }

    .calendar td:hover {
        background: rgba(14, 165, 233, 0.05);
        transform: scale(1.02);
        box-shadow: 0 4px 15px rgba(14, 165, 233, 0.2);
    }

    .calendar .today {
        background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(14, 165, 233, 0.05));
        border: 2px solid var(--medical-primary);
    }

    .calendar .weekend {
        background: linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(107, 114, 128, 0.05));
    }

    .calendar .holiday {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    }

    .calendar .leave {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
    }

    .calendar .permission {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    }

    .calendar .coverage {
        background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(6, 182, 212, 0.05));
    }

    .calendar .day-number {
        font-weight: 700;
        font-size: 1.1rem;
        color: var(--medical-dark);
        margin-bottom: 0.5rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: rgba(14, 165, 233, 0.1);
    }

    .calendar .today .day-number {
        background: var(--medical-primary);
        color: white;
    }

    .calendar .event {
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
        padding: 0.25rem 0.5rem;
        border-radius: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.3s ease;
    }

    .calendar .event:hover {
        transform: scale(1.05);
    }

    .calendar .event.holiday {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }

    .calendar .event.leave {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .calendar .event.permission {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    .calendar .event.coverage {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
        color: white;
    }

    .legend {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: white;
        border-radius: 10px;
        border: 2px solid rgba(14, 165, 233, 0.1);
        transition: all 0.3s ease;
    }

    .legend-item:hover {
        border-color: var(--medical-primary);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(14, 165, 233, 0.1);
    }

    .legend-color {
        width: 24px;
        height: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .legend-text {
        font-weight: 500;
        color: var(--medical-dark);
    }

    .controls-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        border: 2px solid rgba(14, 165, 233, 0.1);
        margin-bottom: 1.5rem;
    }

    .form-control-schedule {
        border: 2px solid rgba(14, 165, 233, 0.2);
        border-radius: 10px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control-schedule:focus {
        border-color: var(--medical-primary);
        box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.25);
    }

    .btn-schedule {
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        border: none;
        border-radius: 10px;
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-schedule:hover {
        background: linear-gradient(135deg, #0284c7, #0369a1);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(14, 165, 233, 0.3);
        color: white;
    }

    .btn-holiday {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        border: none;
        border-radius: 10px;
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-holiday:hover {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Schedule Header -->
<div class="schedule-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="schedule-icon">
                    <i class="fas fa-calendar-week" style="font-size: 3rem; margin-bottom: 1rem; color: rgba(255,255,255,0.8);"></i>
                </div>
                <h1 style="font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); margin-bottom: 0.5rem;">الجدول الشهري</h1>
                <p style="font-size: 1.1rem; opacity: 0.9; font-weight: 300;">إدارة الجداول والمواعيد الشهرية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="schedule-stats">
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>تنظيم متقدم</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- أدوات التحكم -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-pulse">
                            <i class="fas fa-sliders-h medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">أدوات التحكم</h5>
                            <small class="opacity-75">تخصيص عرض الجدول الشهري</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="month" class="form-label" style="font-weight: 600; color: var(--medical-dark); display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-calendar-day text-primary"></i>
                                الشهر
                            </label>
                            <select class="form-select form-control-schedule" id="month">
                            <option value="1" {% if current_month == 1 %}selected{% endif %}>يناير</option>
                            <option value="2" {% if current_month == 2 %}selected{% endif %}>فبراير</option>
                            <option value="3" {% if current_month == 3 %}selected{% endif %}>مارس</option>
                            <option value="4" {% if current_month == 4 %}selected{% endif %}>أبريل</option>
                            <option value="5" {% if current_month == 5 %}selected{% endif %}>مايو</option>
                            <option value="6" {% if current_month == 6 %}selected{% endif %}>يونيو</option>
                            <option value="7" {% if current_month == 7 %}selected{% endif %}>يوليو</option>
                            <option value="8" {% if current_month == 8 %}selected{% endif %}>أغسطس</option>
                            <option value="9" {% if current_month == 9 %}selected{% endif %}>سبتمبر</option>
                            <option value="10" {% if current_month == 10 %}selected{% endif %}>أكتوبر</option>
                            <option value="11" {% if current_month == 11 %}selected{% endif %}>نوفمبر</option>
                            <option value="12" {% if current_month == 12 %}selected{% endif %}>ديسمبر</option>
                        </select>
                    </div>
                        <div class="col-md-4">
                            <label for="year" class="form-label" style="font-weight: 600; color: var(--medical-dark); display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-calendar text-success"></i>
                                السنة
                            </label>
                            <select class="form-select form-control-schedule" id="year">
                                <option value="2023" {% if current_year == 2023 %}selected{% endif %}>2023</option>
                                <option value="2024" {% if current_year == 2024 %}selected{% endif %}>2024</option>
                                <option value="2025" {% if current_year == 2025 %}selected{% endif %}>2025</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="department" class="form-label" style="font-weight: 600; color: var(--medical-dark); display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-building text-info"></i>
                                القسم
                            </label>
                            <select class="form-select form-control-schedule" id="department">
                                <option value="0">جميع الأقسام</option>
                                {% for dept in departments %}
                                <option value="{{ dept.id }}" {% if selected_department == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-12 text-center">
                            <button id="update-schedule" class="btn btn-schedule me-3">
                                <i class="fas fa-sync-alt"></i>
                                تحديث الجدول
                            </button>
                            {% if session.role in ['admin', 'hr', 'gm', 'manager'] %}
                            <button id="add-holiday" class="btn btn-holiday">
                                <i class="fas fa-plus"></i>
                                إضافة عطلة رسمية
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- مفتاح الألوان -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-pulse">
                            <i class="fas fa-palette medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">مفتاح الألوان</h5>
                            <small class="opacity-75">دليل ألوان الأحداث والمناسبات</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, rgba(107, 114, 128, 0.3), rgba(107, 114, 128, 0.1));"></div>
                            <span class="legend-text">عطلة أسبوعية</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, #ef4444, #dc2626);"></div>
                            <span class="legend-text">عطلة رسمية</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, #10b981, #059669);"></div>
                            <span class="legend-text">إجازة</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, #f59e0b, #d97706);"></div>
                            <span class="legend-text">إذن</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, #06b6d4, #0891b2);"></div>
                            <span class="legend-text">تغطية</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الجدول الشهري -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-pulse">
                            <i class="fas fa-calendar medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">جدول {{ month_name }} {{ current_year }}</h5>
                            <small class="opacity-75">عرض تفصيلي للأحداث والمواعيد</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    <div class="table-responsive">
                        <table class="calendar">
                        <thead>
                            <tr>
                                <th>الأحد</th>
                                <th>الإثنين</th>
                                <th>الثلاثاء</th>
                                <th>الأربعاء</th>
                                <th>الخميس</th>
                                <th>الجمعة</th>
                                <th>السبت</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for week in calendar_weeks %}
                            <tr>
                                {% for day in week %}
                                <td class="{% if day.today %}today{% endif %} {% if day.weekend %}weekend{% endif %} {% if day.holiday %}holiday{% endif %} {% if day.leave %}leave{% endif %} {% if day.permission %}permission{% endif %} {% if day.coverage %}coverage{% endif %}">
                                    {% if day.day > 0 %}
                                    <div class="day-number">{{ day.day }}</div>
                                    {% if day.events %}
                                    {% for event in day.events %}
                                    <div class="event {{ event.type }}">{{ event.title }}</div>
                                    {% endfor %}
                                    {% endif %}
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة عطلة رسمية -->
<div class="modal fade" id="holidayModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content medical-card medical-border-animated">
            <div class="modal-header" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-calendar-plus me-2"></i>
                    إضافة عطلة رسمية
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <form id="holiday-form">
                    <div class="mb-3">
                        <label for="holiday-date" class="form-label" style="font-weight: 600; color: var(--medical-dark); display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-calendar-day text-primary"></i>
                            التاريخ
                        </label>
                        <input type="date" class="form-control form-control-schedule" id="holiday-date" required>
                    </div>
                    <div class="mb-3">
                        <label for="holiday-name" class="form-label" style="font-weight: 600; color: var(--medical-dark); display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-tag text-success"></i>
                            اسم العطلة
                        </label>
                        <input type="text" class="form-control form-control-schedule" id="holiday-name" required placeholder="أدخل اسم العطلة...">
                    </div>
                    <div class="mb-3">
                        <label for="holiday-description" class="form-label" style="font-weight: 600; color: var(--medical-dark); display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-comment-alt text-info"></i>
                            الوصف
                        </label>
                        <textarea class="form-control form-control-schedule" id="holiday-description" rows="3" placeholder="وصف العطلة (اختياري)..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="padding: 1.5rem 2rem; border-top: 1px solid rgba(239, 68, 68, 0.1);">
                <button type="button" class="btn medical-btn-advanced" style="background: linear-gradient(135deg, #6b7280, #4b5563);" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="button" class="btn medical-btn-advanced ms-2" style="background: linear-gradient(135deg, #ef4444, #dc2626);" id="save-holiday">
                    <i class="fas fa-save me-1"></i>حفظ
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث الجدول عند تغيير الشهر أو السنة أو القسم
        document.getElementById('update-schedule').addEventListener('click', function() {
            const month = document.getElementById('month').value;
            const year = document.getElementById('year').value;
            const department = document.getElementById('department').value;

            window.location.href = `{{ url_for('monthly_schedule') }}?month=${month}&year=${year}&department=${department}`;
        });

        // فتح نافذة إضافة عطلة رسمية
        {% if session.role in ['admin', 'hr', 'gm', 'manager'] %}
        document.getElementById('add-holiday').addEventListener('click', function() {
            const holidayModal = new bootstrap.Modal(document.getElementById('holidayModal'));
            holidayModal.show();
        });

        // حفظ العطلة الرسمية
        document.getElementById('save-holiday').addEventListener('click', function() {
            const date = document.getElementById('holiday-date').value;
            const name = document.getElementById('holiday-name').value;
            const description = document.getElementById('holiday-description').value;

            if (!date || !name) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // إرسال البيانات إلى الخادم
            fetch('{{ url_for('add_holiday') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    date: date,
                    name: name,
                    description: description
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إعادة تحميل الصفحة
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء حفظ العطلة الرسمية');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الاتصال بالخادم');
            });
        });
        {% endif %}
    });
</script>
{% endblock %}
