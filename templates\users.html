{% extends "base.html" %}

{% block title %}ALEMIS - إدارة المستخدمين{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
<style>
    .users-header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        position: relative;
        overflow: hidden;
    }

    .users-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h3v4h2v-7.5c0-.83.67-1.5 1.5-1.5S12 9.67 12 10.5V18h2v-4h3v4h2v-7.5c0-.83.67-1.5 1.5-1.5S22 9.67 22 10.5V18h2v2H0v-2h4z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 6s ease-in-out infinite;
    }

    .users-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -5%;
        width: 150px;
        height: 150px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite reverse;
    }

    .role-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.85rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.3s ease;
    }

    .role-admin {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        color: white;
    }

    .role-hr {
        background: linear-gradient(135deg, #7c3aed, #6d28d9);
        color: white;
    }

    .role-manager {
        background: linear-gradient(135deg, #059669, #047857);
        color: white;
    }

    .role-gm {
        background: linear-gradient(135deg, #d97706, #b45309);
        color: white;
    }

    .role-employee {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        color: white;
    }

    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 15px;
        font-weight: 500;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .status-active {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .status-inactive {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .action-btn-edit {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
    }

    .action-btn-delete {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }

    .action-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-left: 0.75rem;
    }

    .user-info {
        display: flex;
        align-items: center;
    }

    .user-details {
        display: flex;
        flex-direction: column;
    }

    .user-name {
        font-weight: 600;
        color: var(--medical-dark);
    }

    .user-username {
        font-size: 0.85rem;
        color: var(--medical-secondary);
    }
</style>
{% endblock %}

{% block content %}
<!-- Users Header -->
<div class="users-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="users-icon">
                    <i class="fas fa-users-cog" style="font-size: 3rem; margin-bottom: 1rem; color: rgba(255,255,255,0.8);"></i>
                </div>
                <h1 style="font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); margin-bottom: 0.5rem;">إدارة المستخدمين</h1>
                <p style="font-size: 1.1rem; opacity: 0.9; font-weight: 300;">إدارة حسابات المستخدمين والصلاحيات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="users-stats">
                    <div class="stat-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>نظام إدارة آمن</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- Users Card -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="header-icon medical-pulse">
                                <i class="fas fa-users medical-icon-advanced"></i>
                            </div>
                            <div class="header-content">
                                <h5 class="mb-0">قائمة المستخدمين</h5>
                                <small class="opacity-75">إدارة حسابات المستخدمين والصلاحيات</small>
                            </div>
                        </div>
                        {% if user.role in ['admin', 'hr'] %}
                        <a href="{{ url_for('new_user') }}" class="btn medical-btn-advanced medical-shadow-glow-hover">
                            <i class="fas fa-user-plus me-1 medical-icon-advanced"></i>
                            إضافة مستخدم جديد
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    <div class="table-responsive medical-table-container medical-table-advanced">
                        <table class="table medical-table">
                            <thead class="medical-table-header">
                                <tr>
                                    <th><i class="fas fa-user me-2"></i>المستخدم</th>
                                    <th><i class="fas fa-envelope me-2"></i>البريد الإلكتروني</th>
                                    <th><i class="fas fa-user-tag me-2"></i>الدور</th>
                                    <th><i class="fas fa-building me-2"></i>القسم</th>
                                    <th><i class="fas fa-calendar-plus me-2"></i>تاريخ الانضمام</th>
                                    <th><i class="fas fa-toggle-on me-2"></i>الحالة</th>
                                    {% if user.role in ['admin', 'hr', 'gm'] %}
                                    <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for user_item in users %}
                                <tr class="medical-table-row medical-table-row-advanced medical-wave">
                                    <td>
                                        <div class="user-info">
                                            <div class="user-avatar">
                                                {{ user_item.first_name[0] }}{{ user_item.last_name[0] }}
                                            </div>
                                            <div class="user-details">
                                                <div class="user-name">{{ user_item.first_name }} {{ user_item.last_name }}</div>
                                                <div class="user-username">@{{ user_item.username }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            {{ user_item.email }}
                                        </div>
                                    </td>
                                    <td>
                                        {% if user_item.role == 'admin' %}
                                        <span class="role-badge role-admin">
                                            <i class="fas fa-crown me-1"></i>مدير النظام
                                        </span>
                                        {% elif user_item.role == 'hr' %}
                                        <span class="role-badge role-hr">
                                            <i class="fas fa-users me-1"></i>موارد بشرية
                                        </span>
                                        {% elif user_item.role == 'manager' %}
                                        <span class="role-badge role-manager">
                                            <i class="fas fa-user-tie me-1"></i>مدير قسم
                                        </span>
                                        {% elif user_item.role == 'gm' %}
                                        <span class="role-badge role-gm">
                                            <i class="fas fa-chess-king me-1"></i>مدير عام
                                        </span>
                                        {% else %}
                                        <span class="role-badge role-employee">
                                            <i class="fas fa-user me-1"></i>موظف
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-building text-info me-2"></i>
                                            {{ user_item.department_name }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <i class="fas fa-calendar-plus text-muted me-1"></i>
                                            {{ user_item.date_joined }}
                                        </div>
                                    </td>
                                    <td>
                                        {% if user_item.is_active %}
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle me-1"></i>نشط
                                        </span>
                                        {% else %}
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-times-circle me-1"></i>غير نشط
                                        </span>
                                        {% endif %}
                                    </td>
                                    {% if user.role in ['admin', 'hr', 'gm'] %}
                                    <td>
                                        <div class="action-buttons">
                                            {% if user.role in ['admin', 'hr'] %}
                                            <a href="{{ url_for('edit_user', user_id=user_item.id) }}" class="action-btn action-btn-edit" title="تعديل المستخدم">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}

                                            {% if user_item.id != user.id and (user.role == 'admin' or (user_item.role != 'admin' and user_item.role != 'gm')) %}
                                            <button type="button" class="action-btn action-btn-delete" title="حذف المستخدم"
                                                    data-bs-toggle="modal" data-bs-target="#deleteUserModal{{ user_item.id }}">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>

                                            <!-- Modal for delete confirmation -->
                                            <div class="modal fade" id="deleteUserModal{{ user_item.id }}" tabindex="-1" aria-labelledby="deleteUserModalLabel{{ user_item.id }}" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered">
                                                    <div class="modal-content medical-card medical-border-animated">
                                                        <div class="modal-header" style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white;">
                                                            <h5 class="modal-title d-flex align-items-center" id="deleteUserModalLabel{{ user_item.id }}">
                                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                                تأكيد حذف المستخدم
                                                            </h5>
                                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                                                        </div>
                                                        <div class="modal-body" style="padding: 2rem;">
                                                            <div class="text-center mb-3">
                                                                <div class="user-avatar" style="width: 60px; height: 60px; font-size: 1.5rem; margin: 0 auto 1rem;">
                                                                    {{ user_item.first_name[0] }}{{ user_item.last_name[0] }}
                                                                </div>
                                                            </div>
                                                            <p class="text-center mb-3">هل أنت متأكد من رغبتك في حذف المستخدم:</p>
                                                            <h6 class="text-center text-primary mb-3">{{ user_item.first_name }} {{ user_item.last_name }}</h6>
                                                            <div class="medical-alert" style="background: linear-gradient(135deg, #fef2f2, #fee2e2); border-left: 4px solid #ef4444;">
                                                                <div class="alert-icon">
                                                                    <i class="fas fa-exclamation-triangle" style="color: #ef4444;"></i>
                                                                </div>
                                                                <div class="alert-content">
                                                                    <h6 class="mb-1" style="color: #dc2626;">تحذير مهم</h6>
                                                                    <p class="mb-0" style="color: #7f1d1d;">سيتم حذف جميع البيانات المرتبطة بهذا المستخدم بشكل نهائي ولا يمكن التراجع عن هذا الإجراء.</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer" style="padding: 1.5rem 2rem;">
                                                            <button type="button" class="btn medical-btn-advanced" style="background: linear-gradient(135deg, #6b7280, #4b5563);" data-bs-dismiss="modal">
                                                                <i class="fas fa-times me-1"></i>إلغاء
                                                            </button>
                                                            <form action="{{ url_for('delete_user', user_id=user_item.id) }}" method="POST" style="display: inline;">
                                                                <button type="submit" class="btn medical-btn-advanced" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                                                                    <i class="fas fa-trash-alt me-1"></i>حذف المستخدم
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
