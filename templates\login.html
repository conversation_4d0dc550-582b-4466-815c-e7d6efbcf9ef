{% extends "base.html" %}

{% block title %}شركة العميس الطبية - تسجيل الدخول{% endblock %}

{% block styles %}
<style>
    /* ===== المتغيرات الأساسية ===== */
    :root {
        --primary-blue: #0ea5e9;
        --primary-blue-dark: #0284c7;
        --primary-blue-darker: #0369a1;
        --white: #ffffff;
        --light-gray: #f8fafc;
        --border-color: rgba(14, 165, 233, 0.3);
        --shadow-light: rgba(14, 165, 233, 0.1);
        --shadow-medium: rgba(14, 165, 233, 0.2);
        --shadow-dark: rgba(0, 0, 0, 0.3);
    }

    /* ===== الخلفية الأساسية ===== */
    body {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 50%, var(--primary-blue-darker) 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        overflow-x: hidden;
    }

    /* ===== نمط الخلفية الطبية ===== */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.08)' stroke-width='1'%3E%3Cpath d='M100 20 L180 100 L100 180 L20 100 Z'/%3E%3Cpath d='M100 40 L160 100 L100 160 L40 100 Z'/%3E%3Ccircle cx='100' cy='100' r='80'/%3E%3Ccircle cx='100' cy='100' r='60'/%3E%3Ccircle cx='100' cy='100' r='40'/%3E%3C/g%3E%3C/svg%3E") repeat;
        background-size: 150px 150px;
        opacity: 0.4;
        z-index: -1;
        animation: backgroundMove 120s linear infinite;
    }

    /* ===== الحاوي الرئيسي ===== */
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 1rem;
    }

    /* ===== بطاقة تسجيل الدخول ===== */
    .login-card {
        background: var(--white);
        border-radius: 20px;
        box-shadow:
            0 25px 50px var(--shadow-dark),
            0 0 0 1px var(--border-color);
        overflow: hidden;
        position: relative;
        max-width: 500px;
        width: 100%;
        margin: 0 auto;
        transition: all 0.4s ease;
        border: 2px solid var(--border-color);
    }

    .login-card:hover {
        transform: translateY(-8px);
        box-shadow:
            0 35px 70px var(--shadow-dark),
            0 0 0 2px var(--primary-blue);
    }

    /* ===== شريط علوي متحرك ===== */
    .login-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, var(--primary-blue), var(--primary-blue-dark), var(--primary-blue-darker));
        background-size: 200% 100%;
        animation: gradientMove 3s ease-in-out infinite;
        z-index: 1;
    }

    /* ===== رأس البطاقة ===== */
    .login-header {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
        color: var(--white);
        padding: 3rem 2rem 2rem;
        text-align: center;
        position: relative;
        z-index: 2;
    }

    /* ===== الشعار ===== */
    .login-logo {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .login-logo:hover {
        transform: scale(1.05);
        background: rgba(255, 255, 255, 0.2);
    }

    .login-logo svg {
        width: 45px;
        height: 45px;
        filter: drop-shadow(0 2px 8px rgba(0,0,0,0.2));
    }

    /* ===== العناوين ===== */
    .login-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        letter-spacing: 0.5px;
    }

    .login-subtitle {
        font-size: 0.95rem;
        opacity: 0.9;
        font-weight: 400;
        line-height: 1.4;
        margin-bottom: 0;
    }

    /* ===== جسم البطاقة ===== */
    .login-body {
        padding: 2.5rem;
        background: var(--white);
    }

    /* ===== مجموعة الحقول ===== */
    .form-group-login {
        margin-bottom: 1.5rem;
        position: relative;
    }

    /* ===== حقول الإدخال ===== */
    .form-control-login {
        width: 100%;
        height: 60px;
        padding: 0 3rem 0 3.5rem;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        font-size: 1rem;
        background: #f8fafc;
        transition: all 0.3s ease;
        outline: none;
        font-family: inherit;
    }

    .form-control-login:focus {
        border-color: var(--primary-blue);
        background: var(--white);
        box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        transform: translateY(-1px);
    }

    /* ===== أيقونات الحقول ===== */
    .form-icon {
        position: absolute;
        left: 1.2rem;
        top: 50%;
        transform: translateY(-50%);
        color: #64748b;
        font-size: 1.1rem;
        z-index: 2;
        transition: color 0.3s ease;
    }

    .form-group-login:focus-within .form-icon {
        color: var(--primary-blue);
    }

    /* ===== زر إظهار/إخفاء كلمة المرور ===== */
    .password-toggle {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #64748b;
        font-size: 1rem;
        cursor: pointer;
        z-index: 2;
        padding: 8px;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .password-toggle:hover {
        color: var(--primary-blue);
        background: rgba(14, 165, 233, 0.1);
    }

    /* ===== تسميات الحقول ===== */
    .form-label-login {
        position: absolute;
        left: 3.5rem;
        top: 50%;
        transform: translateY(-50%);
        color: #64748b;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        pointer-events: none;
        background: transparent;
        z-index: 1;
    }

    .form-control-login:focus + .form-label-login,
    .form-control-login:not(:placeholder-shown) + .form-label-login {
        top: -8px;
        left: 1rem;
        font-size: 0.8rem;
        color: var(--primary-blue);
        font-weight: 600;
        background: var(--white);
        padding: 0 0.5rem;
    }

    /* ===== زر تسجيل الدخول ===== */
    .login-btn {
        width: 100%;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
        border: none;
        border-radius: 12px;
        color: var(--white);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 1rem;
        position: relative;
        overflow: hidden;
    }

    .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
    }

    .login-btn:active {
        transform: translateY(0);
    }

    /* ===== خيارات إضافية ===== */
    .login-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 1.5rem 0 1rem;
        font-size: 0.9rem;
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-check-input {
        width: 18px;
        height: 18px;
        border: 2px solid #e2e8f0;
        border-radius: 4px;
        cursor: pointer;
    }

    .form-check-label {
        color: #64748b;
        cursor: pointer;
        user-select: none;
    }

    .forgot-password {
        color: var(--primary-blue);
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .forgot-password:hover {
        color: var(--primary-blue-dark);
        text-decoration: underline;
    }

    /* ===== ذيل البطاقة ===== */
    .login-footer {
        background: var(--light-gray);
        padding: 1.5rem 2.5rem;
        text-align: center;
        border-top: 1px solid #e2e8f0;
    }

    .login-footer-text {
        color: #64748b;
        font-size: 0.85rem;
        margin: 0;
        line-height: 1.4;
    }

    /* ===== الرسوم المتحركة ===== */
    @keyframes gradientMove {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    @keyframes backgroundMove {
        0% { transform: translateX(0) translateY(0); }
        25% { transform: translateX(-25px) translateY(-25px); }
        50% { transform: translateX(-50px) translateY(0); }
        75% { transform: translateX(-25px) translateY(25px); }
        100% { transform: translateX(0) translateY(0); }
    }

    @keyframes fadeInUp {
        0% {
            opacity: 0;
            transform: translateY(20px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* ===== تأثير الظهور ===== */
    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    /* ===== الأنماط المتجاوبة ===== */
    @media (max-width: 768px) {
        .login-container {
            padding: 1rem 0.5rem;
        }

        .login-card {
            max-width: 100%;
            margin: 0;
            border-radius: 15px;
        }

        .login-header {
            padding: 2rem 1.5rem;
        }

        .login-logo {
            width: 70px;
            height: 70px;
            margin-bottom: 1rem;
        }

        .login-logo svg {
            width: 40px;
            height: 40px;
        }

        .login-title {
            font-size: 1.5rem;
            margin-bottom: 0.3rem;
        }

        .login-subtitle {
            font-size: 0.85rem;
        }

        .login-body {
            padding: 2rem 1.5rem;
        }

        .form-control-login {
            height: 55px;
            padding: 0 2.5rem 0 3rem;
            font-size: 0.95rem;
        }

        .form-icon {
            left: 1rem;
            font-size: 1rem;
        }

        .form-label-login {
            left: 3rem;
            font-size: 0.9rem;
        }

        .form-control-login:focus + .form-label-login,
        .form-control-login:not(:placeholder-shown) + .form-label-login {
            left: 0.8rem;
            font-size: 0.75rem;
        }

        .login-btn {
            height: 45px;
            font-size: 0.95rem;
        }

        .login-options {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .login-footer {
            padding: 1.2rem 1.5rem;
        }

        .login-footer-text {
            font-size: 0.8rem;
        }
    }

    @media (max-width: 480px) {
        .login-container {
            padding: 0.5rem 0.25rem;
        }

        .login-card {
            border-radius: 12px;
        }

        .login-header {
            padding: 1.5rem 1rem;
        }

        .login-body {
            padding: 1.5rem 1rem;
        }

        .login-footer {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="login-card fade-in">
                    <!-- رأس البطاقة -->
                    <div class="login-header">
                        <div class="login-logo">
                            <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="50" cy="50" r="45" fill="rgba(255,255,255,0.9)" stroke="rgba(14, 165, 233, 0.5)" stroke-width="2"/>
                                <rect x="42" y="25" width="16" height="50" fill="#0ea5e9" rx="2"/>
                                <rect x="25" y="42" width="50" height="16" fill="#0ea5e9" rx="2"/>
                                <circle cx="50" cy="50" r="8" fill="white"/>
                            </svg>
                        </div>
                        <h1 class="login-title">مرحباً بك</h1>
                        <p class="login-subtitle">شركة العميس الطبية - نظام إدارة موظفين المختبر</p>
                    </div>

                    <!-- جسم البطاقة -->
                    <div class="login-body">
                        <form method="POST" action="{{ url_for('login') }}">
                            <!-- حقل اسم المستخدم -->
                            <div class="form-group-login">
                                <i class="fas fa-user form-icon"></i>
                                <input type="text" class="form-control-login" id="inputUsername"
                                       name="username" placeholder=" " required autocomplete="username">
                                <label for="inputUsername" class="form-label-login">اسم المستخدم</label>
                            </div>

                            <!-- حقل كلمة المرور -->
                            <div class="form-group-login">
                                <i class="fas fa-lock form-icon"></i>
                                <input type="password" class="form-control-login" id="inputPassword"
                                       name="password" placeholder=" " required autocomplete="current-password">
                                <label for="inputPassword" class="form-label-login">كلمة المرور</label>
                                <button type="button" class="password-toggle" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>

                            <!-- خيارات إضافية -->
                            <div class="login-options">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label" for="rememberMe">تذكرني</label>
                                </div>
                                <a href="#" class="forgot-password">
                                    <i class="fas fa-key"></i>
                                    نسيت كلمة المرور؟
                                </a>
                            </div>

                            <!-- زر تسجيل الدخول -->
                            <button type="submit" class="login-btn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </form>
                    </div>

                    <!-- ذيل البطاقة -->
                    <div class="login-footer">
                        <p class="login-footer-text">
                            <i class="fas fa-shield-alt me-1"></i>
                            نظام آمن ومحمي - متاح 24/7
                        </p>
                        <small class="login-footer-text">
                            جميع الحقوق محفوظة © 2024 - شركة العميس الطبية
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل إظهار/إخفاء كلمة المرور
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('inputPassword');

    togglePassword.addEventListener('click', function() {
        const icon = this.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // تحسين تجربة المستخدم - التركيز التلقائي
    document.getElementById('inputUsername').focus();

    // تأثير الضغط على زر تسجيل الدخول
    const loginBtn = document.querySelector('.login-btn');
    loginBtn.addEventListener('mousedown', function() {
        this.style.transform = 'translateY(-1px) scale(0.98)';
    });

    loginBtn.addEventListener('mouseup', function() {
        this.style.transform = 'translateY(-2px) scale(1)';
    });

    loginBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>
{% endblock %}
