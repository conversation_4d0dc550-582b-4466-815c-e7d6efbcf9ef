"""
اختبارات شاملة لنظام ALEMIS
Comprehensive Tests for ALEMIS System
"""
import pytest
import json
from datetime import datetime, date, timedelta
from flask import url_for
from app import create_app
from app.models import User, Department, LeaveType, LeaveRequest, UserRole
from app.database import db_manager
from app.security import password_manager


@pytest.fixture
def app():
    """إنشاء تطبيق للاختبار"""
    app = create_app('testing')
    
    with app.app_context():
        db_manager.create_all()
        yield app
        db_manager.drop_all()


@pytest.fixture
def client(app):
    """عميل الاختبار"""
    return app.test_client()


@pytest.fixture
def runner(app):
    """مشغل الأوامر"""
    return app.test_cli_runner()


@pytest.fixture
def sample_data(app):
    """بيانات عينة للاختبار"""
    with app.app_context():
        with db_manager.session_scope() as session:
            # إنشاء قسم
            department = Department(
                name="قسم الاختبار",
                description="قسم للاختبار",
                is_active=True
            )
            session.add(department)
            session.flush()
            
            # إنشاء نوع إجازة
            leave_type = LeaveType(
                name="إجازة اعتيادية",
                description="الإجازة السنوية",
                default_days=30,
                is_active=True
            )
            session.add(leave_type)
            session.flush()
            
            # إنشاء مستخدم إداري
            admin_user = User(
                username="test_admin",
                email="<EMAIL>",
                first_name="مدير",
                last_name="الاختبار",
                role=UserRole.ADMIN,
                department_id=department.id,
                is_active=True,
                is_verified=True
            )
            admin_user.set_password("test123")
            session.add(admin_user)
            
            # إنشاء مستخدم موظف
            employee_user = User(
                username="test_employee",
                email="<EMAIL>",
                first_name="موظف",
                last_name="الاختبار",
                role=UserRole.EMPLOYEE,
                department_id=department.id,
                is_active=True,
                is_verified=True
            )
            employee_user.set_password("test123")
            session.add(employee_user)
            
            session.commit()
            
            return {
                'department': department,
                'leave_type': leave_type,
                'admin_user': admin_user,
                'employee_user': employee_user
            }


class TestAuthentication:
    """اختبارات المصادقة"""
    
    def test_login_page_loads(self, client):
        """اختبار تحميل صفحة تسجيل الدخول"""
        response = client.get('/login')
        assert response.status_code == 200
        assert 'تسجيل الدخول' in response.get_data(as_text=True)
    
    def test_valid_login(self, client, sample_data):
        """اختبار تسجيل الدخول الصحيح"""
        response = client.post('/login', data={
            'username': 'test_admin',
            'password': 'test123'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert 'لوحة التحكم' in response.get_data(as_text=True)
    
    def test_invalid_login(self, client, sample_data):
        """اختبار تسجيل الدخول الخاطئ"""
        response = client.post('/login', data={
            'username': 'test_admin',
            'password': 'wrong_password'
        })
        
        assert response.status_code == 200
        assert 'فشل تسجيل الدخول' in response.get_data(as_text=True)
    
    def test_logout(self, client, sample_data):
        """اختبار تسجيل الخروج"""
        # تسجيل الدخول أولاً
        client.post('/login', data={
            'username': 'test_admin',
            'password': 'test123'
        })
        
        # تسجيل الخروج
        response = client.get('/logout', follow_redirects=True)
        assert response.status_code == 200
        assert 'تسجيل الدخول' in response.get_data(as_text=True)


class TestAPI:
    """اختبارات API"""
    
    def test_api_login(self, client, sample_data):
        """اختبار تسجيل الدخول عبر API"""
        response = client.post('/api/v1/auth/login', 
                              json={
                                  'username': 'test_admin',
                                  'password': 'test123'
                              })
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'access_token' in data
        assert 'refresh_token' in data
    
    def test_api_invalid_login(self, client, sample_data):
        """اختبار تسجيل الدخول الخاطئ عبر API"""
        response = client.post('/api/v1/auth/login',
                              json={
                                  'username': 'test_admin',
                                  'password': 'wrong_password'
                              })
        
        assert response.status_code == 401
    
    def test_api_protected_endpoint_without_token(self, client, sample_data):
        """اختبار الوصول لنقطة محمية بدون رمز"""
        response = client.get('/api/v1/users/')
        assert response.status_code == 401
    
    def test_api_protected_endpoint_with_token(self, client, sample_data):
        """اختبار الوصول لنقطة محمية مع رمز"""
        # الحصول على رمز الوصول
        login_response = client.post('/api/v1/auth/login',
                                   json={
                                       'username': 'test_admin',
                                       'password': 'test123'
                                   })
        
        token = json.loads(login_response.data)['access_token']
        
        # استخدام الرمز للوصول
        response = client.get('/api/v1/users/',
                            headers={'Authorization': f'Bearer {token}'})
        
        assert response.status_code == 200


class TestLeaveRequests:
    """اختبارات طلبات الإجازة"""
    
    def login_user(self, client, username='test_employee', password='test123'):
        """تسجيل دخول المستخدم"""
        return client.post('/login', data={
            'username': username,
            'password': password
        })
    
    def test_create_leave_request_via_api(self, client, sample_data):
        """اختبار إنشاء طلب إجازة عبر API"""
        # تسجيل الدخول والحصول على رمز
        login_response = client.post('/api/v1/auth/login',
                                   json={
                                       'username': 'test_employee',
                                       'password': 'test123'
                                   })
        
        token = json.loads(login_response.data)['access_token']
        
        # إنشاء طلب إجازة
        leave_data = {
            'leave_type_id': sample_data['leave_type'].id,
            'start_date': (date.today() + timedelta(days=7)).isoformat(),
            'end_date': (date.today() + timedelta(days=10)).isoformat(),
            'reason': 'إجازة للراحة'
        }
        
        response = client.post('/api/v1/leaves/requests',
                             json=leave_data,
                             headers={'Authorization': f'Bearer {token}'})
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['message'] == 'تم إنشاء طلب الإجازة بنجاح'
    
    def test_invalid_leave_request_dates(self, client, sample_data):
        """اختبار طلب إجازة بتواريخ خاطئة"""
        # تسجيل الدخول والحصول على رمز
        login_response = client.post('/api/v1/auth/login',
                                   json={
                                       'username': 'test_employee',
                                       'password': 'test123'
                                   })
        
        token = json.loads(login_response.data)['access_token']
        
        # إنشاء طلب إجازة بتواريخ خاطئة
        leave_data = {
            'leave_type_id': sample_data['leave_type'].id,
            'start_date': (date.today() + timedelta(days=10)).isoformat(),
            'end_date': (date.today() + timedelta(days=7)).isoformat(),  # تاريخ نهاية قبل البداية
            'reason': 'إجازة للراحة'
        }
        
        response = client.post('/api/v1/leaves/requests',
                             json=leave_data,
                             headers={'Authorization': f'Bearer {token}'})
        
        assert response.status_code == 400


class TestSecurity:
    """اختبارات الأمان"""
    
    def test_password_hashing(self):
        """اختبار تشفير كلمات المرور"""
        password = "test_password_123"
        hashed = password_manager.hash_password(password)
        
        assert hashed != password
        assert password_manager.verify_password(password, hashed)
        assert not password_manager.verify_password("wrong_password", hashed)
    
    def test_password_strength_validation(self):
        """اختبار التحقق من قوة كلمة المرور"""
        # كلمة مرور ضعيفة
        weak_password = "123"
        is_strong, message = password_manager.validate_password_strength(weak_password)
        assert not is_strong
        
        # كلمة مرور قوية
        strong_password = "StrongPass123!"
        is_strong, message = password_manager.validate_password_strength(strong_password)
        assert is_strong
    
    def test_rate_limiting(self, client, sample_data):
        """اختبار تحديد المعدل"""
        # محاولة تسجيل دخول متكررة
        for i in range(10):
            response = client.post('/login', data={
                'username': 'test_admin',
                'password': 'wrong_password'
            })
        
        # يجب أن يتم تحديد المعدل بعد عدة محاولات
        # هذا الاختبار يعتمد على إعدادات Rate Limiting


class TestReports:
    """اختبارات التقارير"""
    
    def test_health_check(self, client):
        """اختبار فحص الصحة"""
        response = client.get('/health')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'status' in data
        assert 'checks' in data


class TestDatabase:
    """اختبارات قاعدة البيانات"""
    
    def test_user_creation(self, app, sample_data):
        """اختبار إنشاء المستخدم"""
        with app.app_context():
            with db_manager.session_scope() as session:
                user_count = session.query(User).count()
                assert user_count >= 2  # admin + employee
    
    def test_user_relationships(self, app, sample_data):
        """اختبار علاقات المستخدم"""
        with app.app_context():
            with db_manager.session_scope() as session:
                user = session.query(User).filter(User.username == 'test_admin').first()
                assert user is not None
                assert user.department is not None
                assert user.department.name == "قسم الاختبار"


class TestNotifications:
    """اختبارات الإشعارات"""
    
    def test_notification_creation(self, app, sample_data):
        """اختبار إنشاء الإشعارات"""
        from app.notifications import notification_manager
        
        with app.app_context():
            # إرسال إشعار داخل التطبيق
            result = notification_manager.send_notification(
                recipients=sample_data['employee_user'],
                title="إشعار اختبار",
                message="هذا إشعار للاختبار",
                channels=['in_app']
            )
            
            assert result['in_app'] == True


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
