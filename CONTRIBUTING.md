# دليل المساهمة في ALEMIS

نرحب بمساهماتكم في تطوير نظام ALEMIS! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 📋 جدول المحتويات

- [قواعد السلوك](#قواعد-السلوك)
- [كيفية المساهمة](#كيفية-المساهمة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح الميزات](#اقتراح-الميزات)
- [إرشادات التطوير](#إرشادات-التطوير)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)

## 🤝 قواعد السلوك

نحن ملتزمون بتوفير بيئة ترحيبية وشاملة للجميع. يرجى قراءة [قواعد السلوك](CODE_OF_CONDUCT.md) قبل المساهمة.

## 🚀 كيفية المساهمة

### 1. إعداد بيئة التطوير

```bash
# نسخ المستودع
git clone https://github.com/yourusername/alemis.git
cd alemis

# إنشاء فرع جديد
git checkout -b feature/amazing-feature

# إعداد البيئة
make install
make dev
```

### 2. أنواع المساهمات المرحب بها

- 🐛 **إصلاح الأخطاء**: إصلاح المشاكل الموجودة
- ✨ **ميزات جديدة**: إضافة وظائف جديدة
- 📚 **التوثيق**: تحسين أو إضافة توثيق
- 🎨 **التصميم**: تحسين واجهة المستخدم
- ⚡ **الأداء**: تحسين أداء النظام
- 🔒 **الأمان**: تحسين الأمان
- 🧪 **الاختبارات**: إضافة أو تحسين الاختبارات

## 🐛 الإبلاغ عن الأخطاء

عند الإبلاغ عن خطأ، يرجى تضمين:

### معلومات البيئة
- نظام التشغيل
- إصدار Python
- إصدار المتصفح (إن أمكن)
- إصدار ALEMIS

### وصف المشكلة
- وصف واضح للمشكلة
- خطوات إعادة إنتاج المشكلة
- السلوك المتوقع مقابل السلوك الفعلي
- لقطات شاشة (إن أمكن)
- رسائل الخطأ

### مثال على تقرير خطأ

```markdown
## وصف المشكلة
عند محاولة تسجيل الدخول، يظهر خطأ 500

## خطوات إعادة الإنتاج
1. اذهب إلى صفحة تسجيل الدخول
2. أدخل اسم المستخدم وكلمة المرور
3. اضغط على "تسجيل الدخول"
4. يظهر خطأ 500

## السلوك المتوقع
يجب أن يتم تسجيل الدخول بنجاح

## البيئة
- OS: Ubuntu 20.04
- Python: 3.11
- Browser: Chrome 120
- ALEMIS: 2.0.0
```

## 💡 اقتراح الميزات

لاقتراح ميزة جديدة:

1. **تحقق من الميزات الموجودة**: تأكد من أن الميزة غير موجودة
2. **ابحث في Issues**: تحقق من عدم وجود اقتراح مشابه
3. **اكتب اقتراحاً مفصلاً**: اشرح الميزة والفائدة منها

### قالب اقتراح الميزة

```markdown
## ملخص الميزة
وصف مختصر للميزة المقترحة

## المشكلة
ما المشكلة التي تحلها هذه الميزة؟

## الحل المقترح
كيف تعمل الميزة؟

## البدائل المدروسة
هل فكرت في حلول أخرى؟

## معلومات إضافية
أي معلومات أخرى مفيدة
```

## 🛠️ إرشادات التطوير

### هيكل المشروع

```
alemis/
├── app/                    # كود التطبيق الرئيسي
│   ├── models.py          # نماذج قاعدة البيانات
│   ├── api.py             # API endpoints
│   ├── security.py        # نظام الأمان
│   ├── notifications.py   # نظام الإشعارات
│   └── ...
├── tests/                 # الاختبارات
├── static/                # الملفات الثابتة
├── templates/             # قوالب HTML
├── docker-compose.yml     # تكوين Docker
└── requirements.txt       # التبعيات
```

### إعداد قاعدة البيانات

```bash
# تهيئة قاعدة البيانات
python manage.py init_db

# إنشاء مستخدم تجريبي
python manage.py create_user
```

### تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
make test

# تشغيل اختبارات محددة
pytest tests/test_api.py -v

# تشغيل مع تغطية الكود
pytest --cov=app tests/
```

## 📏 معايير الكود

### Python

- اتبع [PEP 8](https://pep8.org/)
- استخدم [Black](https://black.readthedocs.io/) للتنسيق
- استخدم [isort](https://isort.readthedocs.io/) لترتيب الاستيرادات
- أضف type hints حيثما أمكن

```python
def create_user(username: str, email: str) -> User:
    """إنشاء مستخدم جديد"""
    user = User(username=username, email=email)
    return user
```

### JavaScript

- استخدم ES6+ features
- اتبع [Airbnb Style Guide](https://github.com/airbnb/javascript)
- استخدم `const` و `let` بدلاً من `var`

### CSS

- استخدم BEM methodology
- اكتب CSS متجاوب
- استخدم CSS variables للألوان

### التوثيق

- اكتب docstrings للدوال والكلاسات
- استخدم تعليقات واضحة
- حدث README.md عند الحاجة

## 🔍 عملية المراجعة

### قبل إرسال Pull Request

1. **تأكد من مرور الاختبارات**
   ```bash
   make test
   make lint
   ```

2. **تحديث التوثيق**
   - حدث README.md إذا لزم الأمر
   - أضف docstrings للكود الجديد

3. **اكتب رسالة commit واضحة**
   ```
   feat: إضافة نظام المصادقة الثنائية
   
   - إضافة QR code generation
   - إضافة TOTP verification
   - تحديث واجهة المستخدم
   ```

### قالب Pull Request

```markdown
## الوصف
وصف مختصر للتغييرات

## نوع التغيير
- [ ] إصلاح خطأ
- [ ] ميزة جديدة
- [ ] تحسين
- [ ] تحديث توثيق

## الاختبار
- [ ] تم اختبار التغييرات محلياً
- [ ] تمت إضافة اختبارات جديدة
- [ ] جميع الاختبارات تمر بنجاح

## قائمة المراجعة
- [ ] الكود يتبع معايير المشروع
- [ ] تم إجراء مراجعة ذاتية
- [ ] تم تحديث التوثيق
- [ ] لا توجد تحذيرات جديدة
```

## 🏷️ تسمية الفروع

استخدم أسماء وصفية للفروع:

- `feature/user-authentication` - للميزات الجديدة
- `bugfix/login-error` - لإصلاح الأخطاء
- `hotfix/security-patch` - للإصلاحات العاجلة
- `docs/api-documentation` - لتحديث التوثيق

## 📝 رسائل Commit

اتبع [Conventional Commits](https://www.conventionalcommits.org/):

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### أمثلة:

```
feat: إضافة نظام الإشعارات الفورية
fix: إصلاح مشكلة تسجيل الدخول
docs: تحديث دليل التثبيت
style: تحسين تنسيق الكود
refactor: إعادة هيكلة نظام المصادقة
test: إضافة اختبارات للـ API
chore: تحديث التبعيات
```

## 🆘 الحصول على المساعدة

إذا كنت بحاجة إلى مساعدة:

1. **اقرأ التوثيق**: تحقق من README.md و docs/
2. **ابحث في Issues**: قد تجد إجابة لسؤالك
3. **اسأل في Discussions**: للأسئلة العامة
4. **أنشئ Issue**: للمشاكل المحددة

## 🙏 شكراً لك!

شكراً لك على اهتمامك بالمساهمة في ALEMIS. مساهماتك تساعد في جعل النظام أفضل للجميع!
