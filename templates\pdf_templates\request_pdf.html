<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ request_title }}</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #60a5fa;
            padding-bottom: 10px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        h1 {
            color: #60a5fa;
            font-size: 24px;
            margin-bottom: 5px;
        }
        .subtitle {
            color: #6c757d;
            font-size: 16px;
            margin-top: 0;
        }
        .request-info {
            margin-bottom: 30px;
        }
        .request-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .request-info th, .request-info td {
            border: 1px solid #dee2e6;
            padding: 10px;
            text-align: right;
        }
        .request-info th {
            background-color: #f8f9fa;
            width: 30%;
        }
        .details {
            margin-bottom: 30px;
        }
        .approval-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .approval-box {
            border: 1px solid #dee2e6;
            padding: 15px;
            width: 45%;
            text-align: center;
        }
        .approval-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stamp {
            margin: 15px auto;
            width: 100px;
            height: 100px;
            border: 2px solid #60a5fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #60a5fa;
            font-weight: bold;
            transform: rotate(-15deg);
        }
        .signature-line {
            margin-top: 20px;
            border-top: 1px solid #dee2e6;
            padding-top: 5px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }
        .badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .badge-success {
            background-color: #198754;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .badge-info {
            background-color: #0dcaf0;
            color: #212529;
        }
        .badge-primary {
            background-color: #60a5fa;
        }
        .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .print-controls button {
            margin: 0 5px;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-print {
            background-color: #60a5fa;
            color: white;
        }
        .btn-save {
            background-color: #198754;
            color: white;
        }
        .btn-close {
            background-color: #6c757d;
            color: white;
        }
        @media print {
            .print-controls {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- أزرار التحكم في الطباعة -->
    <div class="print-controls">
        <button class="btn-print" onclick="window.print()">
            🖨️ طباعة
        </button>
        <button class="btn-save" onclick="savePDF()">
            💾 حفظ PDF
        </button>
        <button class="btn-close" onclick="window.close()">
            ❌ إغلاق
        </button>
    </div>

    <div class="container">
        <div class="header">
            <img src="{{ logo_path }}" alt="ALEMIS Logo" class="logo">
            <h1>{{ request_title }}</h1>
            <p class="subtitle">نظام إدارة إجازات الموظفين في المختبرات</p>
        </div>

        <div class="request-info">
            <h2>معلومات الطلب</h2>
            <table>
                <tr>
                    <th>رقم الطلب</th>
                    <td>{{ request.id }}</td>
                </tr>
                <tr>
                    <th>نوع الطلب</th>
                    <td>{{ request_type_name }}</td>
                </tr>
                <tr>
                    <th>تاريخ الطلب</th>
                    <td>{{ request.created_at }}</td>
                </tr>
                <tr>
                    <th>حالة الطلب</th>
                    <td>
                        {% if request.status == 'approved' %}
                        <span class="badge badge-success">تمت الموافقة</span>
                        {% elif request.status == 'pending' %}
                        <span class="badge badge-warning">قيد الانتظار</span>
                        {% elif request.status == 'rejected' %}
                        <span class="badge badge-danger">مرفوض</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>مقدم الطلب</th>
                    <td>{{ employee_name }}</td>
                </tr>
                <tr>
                    <th>القسم</th>
                    <td>{{ department_name }}</td>
                </tr>
            </table>
        </div>

        <div class="details">
            <h2>تفاصيل الطلب</h2>

            {% if request_type == 'leave' %}
            <table>
                <tr>
                    <th>نوع الإجازة</th>
                    <td>{{ request.leave_type_name }}</td>
                </tr>
                <tr>
                    <th>تاريخ البداية</th>
                    <td>{{ request.start_date }}</td>
                </tr>
                <tr>
                    <th>تاريخ النهاية</th>
                    <td>{{ request.end_date }}</td>
                </tr>
                <tr>
                    <th>عدد الأيام</th>
                    <td>{{ leave_days }}</td>
                </tr>
                <tr>
                    <th>سبب الإجازة</th>
                    <td>{{ request.reason }}</td>
                </tr>
            </table>

            {% elif request_type == 'coverage' %}
            <table>
                <tr>
                    <th>تاريخ التغطية</th>
                    <td>{{ request.coverage_date }}</td>
                </tr>
                <tr>
                    <th>نوع التغطية</th>
                    <td>
                        {% if request.coverage_type == 'morning' %}
                        دوام صباحي (8ص-4م)
                        {% elif request.coverage_type == 'evening' %}
                        دوام مسائي (4م-12ل)
                        {% elif request.coverage_type == 'night' %}
                        دوام ليلي (12ل-8ص)
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>سبب التغطية</th>
                    <td>{{ request.coverage_reason }}</td>
                </tr>
                <tr>
                    <th>وصف إضافي</th>
                    <td>{{ request.description }}</td>
                </tr>
            </table>

            {% elif request_type == 'shift_swap' %}
            <table>
                <tr>
                    <th>نوع التبديل</th>
                    <td>
                        {% if request.swap_type == 'with_employee' %}
                        تبديل مع موظف آخر
                        {% else %}
                        تبديل بدون موظف
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>مدة التبديل</th>
                    <td>
                        {% if request.swap_duration == 'one_day' %}
                        يوم واحد
                        {% else %}
                        عدة أيام
                        {% endif %}
                    </td>
                </tr>
                {% if request.swap_duration == 'one_day' %}
                <tr>
                    <th>تاريخ التبديل</th>
                    <td>{{ request.swap_date }}</td>
                </tr>
                {% else %}
                <tr>
                    <th>تاريخ البداية</th>
                    <td>{{ request.start_date }}</td>
                </tr>
                <tr>
                    <th>تاريخ النهاية</th>
                    <td>{{ request.end_date }}</td>
                </tr>
                {% endif %}
                {% if request.swap_with_user_id %}
                <tr>
                    <th>التبديل مع</th>
                    <td>{{ swap_with_name }}</td>
                </tr>
                {% endif %}
                <tr>
                    <th>سبب التبديل</th>
                    <td>{{ request.reason }}</td>
                </tr>
            </table>

            {% elif request_type == 'profile' %}
            <table>
                <tr>
                    <th>نوع التعديل</th>
                    <td>
                        {% if request.request_type == 'username' %}
                        تغيير اسم المستخدم
                        {% elif request.request_type == 'email' %}
                        تغيير البريد الإلكتروني
                        {% elif request.request_type == 'password' %}
                        تغيير كلمة المرور
                        {% elif request.request_type == 'personal_info' %}
                        تغيير المعلومات الشخصية
                        {% endif %}
                    </td>
                </tr>
                {% if request.request_type != 'password' %}
                <tr>
                    <th>القيمة الحالية</th>
                    <td>{{ request.current_value }}</td>
                </tr>
                <tr>
                    <th>القيمة الجديدة</th>
                    <td>{{ request.new_value }}</td>
                </tr>
                {% endif %}
                <tr>
                    <th>سبب التعديل</th>
                    <td>{{ request.reason }}</td>
                </tr>
            </table>
            {% endif %}
        </div>

        <div class="approval-section">
            <div class="approval-box">
                <div class="approval-title">موافقة المدير</div>
                {% if request.manager_approval == 1 %}
                <div class="stamp">تمت الموافقة</div>
                <div>{{ manager_name }}</div>
                <div class="signature-line">التوقيع</div>
                {% else %}
                <p>لم تتم الموافقة بعد</p>
                {% endif %}
            </div>

            <div class="approval-box">
                <div class="approval-title">موافقة الموارد البشرية</div>
                {% if request.hr_approval == 1 %}
                <div class="stamp">تمت الموافقة</div>
                <div>{{ hr_name }}</div>
                <div class="signature-line">التوقيع</div>
                {% else %}
                <p>لم تتم الموافقة بعد</p>
                {% endif %}
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا المستند بواسطة نظام ALEMIS لإدارة إجازات الموظفين في المختبرات</p>
            <p>تاريخ الطباعة: {{ print_date }}</p>
        </div>
    </div>

    <script>
        function savePDF() {
            // استخدام وظيفة الطباعة في المتصفح لحفظ كـ PDF
            window.print();
        }

        // إضافة اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // Ctrl+P للطباعة
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Escape للإغلاق
            if (e.key === 'Escape') {
                window.close();
            }
        });

        // تحسين عرض الصفحة للطباعة
        window.addEventListener('beforeprint', function() {
            document.title = '{{ request_type_name }}_{{ request.id }}';
        });
    </script>
</body>
</html>
