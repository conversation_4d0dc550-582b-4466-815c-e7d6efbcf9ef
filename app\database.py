"""
نظام إدارة قاعدة البيانات المحسن
Enhanced Database Management System
"""
import os
import logging
from typing import Optional, List, Dict, Any, Union
from contextlib import contextmanager

try:
    from sqlalchemy import create_engine, event, pool
    from sqlalchemy.orm import sessionmaker, Session, scoped_session
    from sqlalchemy.exc import SQLAlchemyError, IntegrityError
    from sqlalchemy.pool import QueuePool
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    # إنشاء بدائل بسيطة
    class Session:
        pass

    class SQLAlchemyError(Exception):
        pass

    class IntegrityError(SQLAlchemyError):
        pass

try:
    from flask import current_app, g
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

    class g:
        pass

try:
    from .models import Base, User, Department, LeaveType, LeaveRequest, LeaveBalance, AuditLog
    MODELS_AVAILABLE = True
except ImportError:
    MODELS_AVAILABLE = False
    # إنشاء بدائل بسيطة
    class Base:
        metadata = None

    User = Department = LeaveType = LeaveRequest = LeaveBalance = AuditLog = None


logger = logging.getLogger(__name__)


class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""

    def __init__(self, app=None):
        self.engine = None
        self.session_factory = None
        self.Session = None
        self.available = SQLALCHEMY_AVAILABLE

        if app is not None and SQLALCHEMY_AVAILABLE:
            self.init_app(app)

    def init_app(self, app):
        """تهيئة قاعدة البيانات"""
        if not SQLALCHEMY_AVAILABLE:
            logger.warning("SQLAlchemy غير متاح - سيتم تعطيل قاعدة البيانات")
            return

        database_url = app.config.get('DATABASE_URL', 'sqlite:///alemis.db')

        try:
            # إعداد محرك قاعدة البيانات
            engine_kwargs = {
                'echo': app.config.get('DEBUG', False),
                'pool_pre_ping': True,
                'pool_recycle': app.config.get('DATABASE_POOL_RECYCLE', 3600),
            }

            if database_url.startswith('sqlite'):
                engine_kwargs.update({
                    'connect_args': {'check_same_thread': False},
                    'poolclass': pool.StaticPool
                })
            else:
                engine_kwargs.update({
                    'pool_size': app.config.get('DATABASE_POOL_SIZE', 10),
                    'max_overflow': app.config.get('DATABASE_MAX_OVERFLOW', 20),
                    'pool_timeout': app.config.get('DATABASE_POOL_TIMEOUT', 30),
                    'poolclass': QueuePool
                })

            self.engine = create_engine(database_url, **engine_kwargs)

            # إعداد جلسات قاعدة البيانات
            self.session_factory = sessionmaker(bind=self.engine)
            self.Session = scoped_session(self.session_factory)

            # تسجيل معالجات الأحداث
            self._register_event_listeners()

            # تسجيل معالجات Flask
            if FLASK_AVAILABLE:
                app.before_request(self._before_request)
                app.teardown_appcontext(self._teardown_db)

            # حفظ المرجع في التطبيق
            app.db = self

        except Exception as e:
            logger.error(f"فشل في تهيئة قاعدة البيانات: {e}")
            self.available = False
    
    def _register_event_listeners(self):
        """تسجيل معالجات أحداث قاعدة البيانات"""
        
        @event.listens_for(self.engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """تفعيل Foreign Keys في SQLite"""
            if 'sqlite' in str(self.engine.url):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=10000")
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.close()
    
    def _before_request(self):
        """معالج ما قبل الطلب"""
        g.db_session = self.Session()
    
    def _teardown_db(self, exception):
        """تنظيف جلسة قاعدة البيانات"""
        session = g.pop('db_session', None)
        if session is not None:
            try:
                if exception is None:
                    session.commit()
                else:
                    session.rollback()
            except Exception as e:
                logger.error(f"خطأ في إغلاق جلسة قاعدة البيانات: {e}")
                session.rollback()
            finally:
                session.close()
    
    def create_all(self):
        """إنشاء جميع الجداول"""
        if not self.available or not MODELS_AVAILABLE:
            logger.warning("قاعدة البيانات أو النماذج غير متاحة")
            return

        try:
            Base.metadata.create_all(self.engine)
            logger.info("تم إنشاء جميع جداول قاعدة البيانات بنجاح")
        except Exception as e:
            logger.error(f"خطأ في إنشاء جداول قاعدة البيانات: {e}")
            raise

    def drop_all(self):
        """حذف جميع الجداول"""
        if not self.available or not MODELS_AVAILABLE:
            logger.warning("قاعدة البيانات أو النماذج غير متاحة")
            return

        try:
            Base.metadata.drop_all(self.engine)
            logger.info("تم حذف جميع جداول قاعدة البيانات")
        except Exception as e:
            logger.error(f"خطأ في حذف جداول قاعدة البيانات: {e}")
            raise
    
    @contextmanager
    def session_scope(self):
        """Context manager لجلسة قاعدة البيانات"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"خطأ في جلسة قاعدة البيانات: {e}")
            raise
        finally:
            session.close()
    
    def get_session(self) -> Session:
        """الحصول على جلسة قاعدة البيانات الحالية"""
        return g.get('db_session') or self.Session()


class BaseRepository:
    """مستودع البيانات الأساسي"""
    
    def __init__(self, model_class, db_manager: DatabaseManager):
        self.model_class = model_class
        self.db_manager = db_manager
    
    def get_session(self) -> Session:
        """الحصول على جلسة قاعدة البيانات"""
        return self.db_manager.get_session()
    
    def create(self, **kwargs) -> Any:
        """إنشاء سجل جديد"""
        session = self.get_session()
        try:
            instance = self.model_class(**kwargs)
            session.add(instance)
            session.flush()  # للحصول على ID
            return instance
        except IntegrityError as e:
            session.rollback()
            logger.error(f"خطأ في إنشاء {self.model_class.__name__}: {e}")
            raise ValueError("البيانات المدخلة غير صحيحة أو مكررة")
        except Exception as e:
            session.rollback()
            logger.error(f"خطأ غير متوقع في إنشاء {self.model_class.__name__}: {e}")
            raise
    
    def get_by_id(self, id: int) -> Optional[Any]:
        """الحصول على سجل بالمعرف"""
        session = self.get_session()
        return session.query(self.model_class).filter(self.model_class.id == id).first()
    
    def get_all(self, limit: int = None, offset: int = None) -> List[Any]:
        """الحصول على جميع السجلات"""
        session = self.get_session()
        query = session.query(self.model_class)
        
        if offset:
            query = query.offset(offset)
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    def update(self, id: int, **kwargs) -> Optional[Any]:
        """تحديث سجل"""
        session = self.get_session()
        try:
            instance = session.query(self.model_class).filter(self.model_class.id == id).first()
            if not instance:
                return None
            
            for key, value in kwargs.items():
                if hasattr(instance, key):
                    setattr(instance, key, value)
            
            session.flush()
            return instance
        except Exception as e:
            session.rollback()
            logger.error(f"خطأ في تحديث {self.model_class.__name__}: {e}")
            raise
    
    def delete(self, id: int) -> bool:
        """حذف سجل"""
        session = self.get_session()
        try:
            instance = session.query(self.model_class).filter(self.model_class.id == id).first()
            if not instance:
                return False
            
            session.delete(instance)
            session.flush()
            return True
        except Exception as e:
            session.rollback()
            logger.error(f"خطأ في حذف {self.model_class.__name__}: {e}")
            raise
    
    def count(self) -> int:
        """عدد السجلات"""
        session = self.get_session()
        return session.query(self.model_class).count()
    
    def exists(self, **kwargs) -> bool:
        """التحقق من وجود سجل"""
        session = self.get_session()
        query = session.query(self.model_class)
        
        for key, value in kwargs.items():
            if hasattr(self.model_class, key):
                query = query.filter(getattr(self.model_class, key) == value)
        
        return query.first() is not None


class UserRepository(BaseRepository):
    """مستودع بيانات المستخدمين"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(User, db_manager)
    
    def get_by_username(self, username: str) -> Optional[User]:
        """الحصول على مستخدم بالاسم"""
        session = self.get_session()
        return session.query(User).filter(User.username == username).first()
    
    def get_by_email(self, email: str) -> Optional[User]:
        """الحصول على مستخدم بالبريد الإلكتروني"""
        session = self.get_session()
        return session.query(User).filter(User.email == email).first()
    
    def get_active_users(self) -> List[User]:
        """الحصول على المستخدمين النشطين"""
        session = self.get_session()
        return session.query(User).filter(User.is_active == True).all()
    
    def get_by_role(self, role: str) -> List[User]:
        """الحصول على المستخدمين بدور معين"""
        session = self.get_session()
        return session.query(User).filter(User.role == role).all()


class LeaveRequestRepository(BaseRepository):
    """مستودع بيانات طلبات الإجازة"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(LeaveRequest, db_manager)
    
    def get_by_user(self, user_id: int) -> List[LeaveRequest]:
        """الحصول على طلبات المستخدم"""
        session = self.get_session()
        return session.query(LeaveRequest).filter(LeaveRequest.user_id == user_id).all()
    
    def get_by_status(self, status: str) -> List[LeaveRequest]:
        """الحصول على الطلبات بحالة معينة"""
        session = self.get_session()
        return session.query(LeaveRequest).filter(LeaveRequest.status == status).all()
    
    def get_pending_requests(self) -> List[LeaveRequest]:
        """الحصول على الطلبات المعلقة"""
        session = self.get_session()
        return session.query(LeaveRequest).filter(
            LeaveRequest.status.in_(['pending', 'submitted', 'manager_approved', 'hr_approved'])
        ).all()


# إنشاء مثيل مدير قاعدة البيانات
db_manager = DatabaseManager()

# إنشاء مستودعات البيانات
user_repository = UserRepository(db_manager)
leave_request_repository = LeaveRequestRepository(db_manager)
