{% extends "base.html" %}

{% block title %}ALEMIS - طلب إذن جديد{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">طلب إذن جديد</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-clock me-1"></i>
                نموذج طلب إذن
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('new_permission') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="date" class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="date" name="date" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="start_time" class="form-label">وقت البداية</label>
                            <input type="time" class="form-control" id="start_time" name="start_time" required>
                        </div>
                        <div class="col-md-6">
                            <label for="end_time" class="form-label">وقت النهاية</label>
                            <input type="time" class="form-control" id="end_time" name="end_time" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="reason" class="form-label">سبب الإذن</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-1"></i>
                                ملاحظة: يجب الموافقة على طلب الإذن من قبل مدير القسم.
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>
                                تقديم الطلب
                            </button>
                            <a href="{{ url_for('permissions') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calculate duration between start and end times
        var startTimeInput = document.getElementById('start_time');
        var endTimeInput = document.getElementById('end_time');
        
        function updateDuration() {
            var startTime = startTimeInput.value;
            var endTime = endTimeInput.value;
            
            if (startTime && endTime) {
                // Parse times
                var startParts = startTime.split(':');
                var endParts = endTime.split(':');
                
                var startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
                var endMinutes = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);
                
                if (endMinutes > startMinutes) {
                    var durationMinutes = endMinutes - startMinutes;
                    var hours = Math.floor(durationMinutes / 60);
                    var minutes = durationMinutes % 60;
                    
                    // Show duration
                    var durationInfo = document.getElementById('duration-info');
                    if (!durationInfo) {
                        durationInfo = document.createElement('div');
                        durationInfo.id = 'duration-info';
                        durationInfo.className = 'alert alert-primary mt-3';
                        endTimeInput.parentNode.appendChild(durationInfo);
                    }
                    durationInfo.innerHTML = '<i class="fas fa-hourglass-half me-1"></i> مدة الإذن: <strong>' + hours + '</strong> ساعة و <strong>' + minutes + '</strong> دقيقة';
                }
            }
        }
        
        startTimeInput.addEventListener('change', updateDuration);
        endTimeInput.addEventListener('change', updateDuration);
    });
</script>
{% endblock %}
