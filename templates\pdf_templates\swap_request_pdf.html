<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب تبديل دوام - {{ swap_request.id }}</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #60a5fa;
            padding-bottom: 20px;
        }
        .logo {
            max-width: 120px;
            margin-bottom: 15px;
        }
        h1 {
            color: #60a5fa;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .subtitle {
            color: #6c757d;
            font-size: 18px;
            margin-top: 0;
            font-weight: normal;
        }
        .request-info {
            margin-bottom: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .request-info h2 {
            color: #495057;
            font-size: 20px;
            margin-bottom: 15px;
            border-bottom: 2px solid #60a5fa;
            padding-bottom: 5px;
        }
        .request-info table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .request-info th,
        .request-info td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }
        .request-info th {
            background-color: #e9ecef;
            font-weight: bold;
            color: #495057;
            width: 30%;
        }
        .request-info td {
            background-color: white;
        }
        .badge {
            display: inline-block;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: bold;
            border-radius: 4px;
            color: white;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .approval-section {
            margin-bottom: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .approval-section h2 {
            color: #495057;
            font-size: 20px;
            margin-bottom: 15px;
            border-bottom: 2px solid #28a745;
            padding-bottom: 5px;
        }
        .approval-item {
            margin-bottom: 15px;
            padding: 15px;
            background-color: white;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }
        .approval-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .approval-status {
            margin-bottom: 8px;
        }
        .approval-comment {
            color: #6c757d;
            font-style: italic;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        .stamp-area {
            margin-top: 30px;
            text-align: center;
            padding: 20px;
            border: 2px dashed #60a5fa;
            background-color: #f8f9fa;
        }
        .stamp-text {
            color: #60a5fa;
            font-weight: bold;
            font-size: 16px;
        }
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .container {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>شركة العميس الطبية</h1>
            <p class="subtitle">نظام إدارة موظفين المختبر</p>
            <h2 style="color: #28a745; margin-top: 20px;">طلب تبديل دوام معتمد</h2>
        </div>

        <!-- Request Information -->
        <div class="request-info">
            <h2>معلومات الطلب</h2>
            <table>
                <tr>
                    <th>رقم الطلب</th>
                    <td>{{ swap_request.id }}</td>
                </tr>
                <tr>
                    <th>الموظف الطالب</th>
                    <td>{{ requester_name }}</td>
                </tr>
                <tr>
                    <th>رقم الموظف الطالب</th>
                    <td>{{ swap_request.requester_username }}</td>
                </tr>
                <tr>
                    <th>قسم الموظف الطالب</th>
                    <td>{{ swap_request.requester_department }}</td>
                </tr>
                <tr>
                    <th>الموظف المطلوب التبديل معه</th>
                    <td>{{ target_name }}</td>
                </tr>
                {% if swap_request.target_username %}
                <tr>
                    <th>رقم الموظف المطلوب التبديل معه</th>
                    <td>{{ swap_request.target_username }}</td>
                </tr>
                {% endif %}
                {% if swap_request.target_department %}
                <tr>
                    <th>قسم الموظف المطلوب التبديل معه</th>
                    <td>{{ swap_request.target_department }}</td>
                </tr>
                {% endif %}
                <tr>
                    <th>نوع التبديل</th>
                    <td>{{ swap_request.swap_type or 'غير محدد' }}</td>
                </tr>
                <tr>
                    <th>تاريخ التبديل</th>
                    <td>{{ swap_request.swap_date }}</td>
                </tr>
                {% if swap_request.shift_type %}
                <tr>
                    <th>نوع الشفت</th>
                    <td>{{ swap_request.shift_type }}</td>
                </tr>
                {% endif %}
                {% if swap_request.requester_shift_type %}
                <tr>
                    <th>شفت الموظف الطالب</th>
                    <td>{{ swap_request.requester_shift_type }}</td>
                </tr>
                {% endif %}
                <tr>
                    <th>سبب التبديل</th>
                    <td>{{ swap_request.reason or 'غير محدد' }}</td>
                </tr>
                <tr>
                    <th>تاريخ تقديم الطلب</th>
                    <td>{{ swap_request.created_at }}</td>
                </tr>
                <tr>
                    <th>حالة الطلب</th>
                    <td>
                        <span class="badge badge-success">تمت الموافقة</span>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Approval Details -->
        <div class="approval-section">
            <h2>تفاصيل الموافقات</h2>
            
            {% if approval_details.manager_approved %}
            <div class="approval-item">
                <div class="approval-title">موافقة مدير المختبر</div>
                <div class="approval-status">
                    <span class="badge badge-success">تمت الموافقة</span>
                </div>
                {% if approval_details.manager_comment %}
                <div class="approval-comment">تعليق: {{ approval_details.manager_comment }}</div>
                {% endif %}
            </div>
            {% endif %}

            {% if approval_details.hr_approved %}
            <div class="approval-item">
                <div class="approval-title">موافقة الموارد البشرية</div>
                <div class="approval-status">
                    <span class="badge badge-success">تمت الموافقة</span>
                </div>
                {% if approval_details.hr_comment %}
                <div class="approval-comment">تعليق: {{ approval_details.hr_comment }}</div>
                {% endif %}
            </div>
            {% endif %}

            {% if approval_details.hospital_manager_approved %}
            <div class="approval-item">
                <div class="approval-title">موافقة المدير العام</div>
                <div class="approval-status">
                    <span class="badge badge-success">تمت الموافقة</span>
                </div>
                {% if approval_details.hospital_manager_comment %}
                <div class="approval-comment">تعليق: {{ approval_details.hospital_manager_comment }}</div>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Official Stamp Area -->
        <div class="stamp-area">
            <div class="stamp-text">ختم الموافقة الرسمي</div>
            <p style="margin-top: 10px; color: #6c757d;">تم اعتماد هذا الطلب رسمياً من قبل الإدارة المختصة</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا المستند بتاريخ: {{ current_date }}</p>
            <p>شركة العميس الطبية - نظام إدارة موظفين المختبر</p>
            <p style="font-size: 12px; margin-top: 10px;">هذا المستند معتمد إلكترونياً ولا يحتاج إلى توقيع ورقي</p>
        </div>
    </div>

    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
