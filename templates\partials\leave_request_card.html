<div class="request-card">
    <div class="request-header">
        <i class="fas fa-calendar-alt me-2"></i>
        طلب إجازة - {{ request.leave_type_name }}
    </div>
    <div class="request-body">
        <div class="request-info">
            <div>
                <strong>رقم الطلب:</strong> #{{ request.id }}
            </div>
            <div>
                <span class="badge status-badge bg-{{ status_badge }}">{{ status_text }}</span>
            </div>
        </div>
        
        <div class="request-details">
            <div class="row">
                <div class="col-6">
                    <small class="text-muted">تاريخ البداية</small>
                    <div><strong>{{ request.start_date }}</strong></div>
                </div>
                <div class="col-6">
                    <small class="text-muted">تاريخ النهاية</small>
                    <div><strong>{{ request.end_date }}</strong></div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-6">
                    <small class="text-muted">عدد الأيام</small>
                    <div><strong>{{ request.days_requested }} يوم</strong></div>
                </div>
                <div class="col-6">
                    <small class="text-muted">تاريخ التقديم</small>
                    <div><strong>{{ request.created_at[:10] }}</strong></div>
                </div>
            </div>
            
            {% if request.reason %}
            <div class="mt-3">
                <small class="text-muted">السبب</small>
                <div>{{ request.reason }}</div>
            </div>
            {% endif %}
            
            {% if request.manager_notes %}
            <div class="mt-3">
                <small class="text-muted">ملاحظات المدير</small>
                <div class="alert alert-info mb-0">{{ request.manager_notes }}</div>
            </div>
            {% endif %}
        </div>
        
        <div class="mt-3 d-flex justify-content-between align-items-center">
            <small class="text-muted">
                <i class="fas fa-clock me-1"></i>
                {{ request.created_at }}
            </small>
            {% if status == 'approved' %}
            <a href="{{ url_for('export_request_pdf', request_type='leave', request_id=request.id) }}" 
               class="btn btn-sm btn-outline-primary">
                <i class="fas fa-download me-1"></i>تصدير PDF
            </a>
            {% endif %}
        </div>
    </div>
</div>
