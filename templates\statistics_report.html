{% extends "base.html" %}

{% block title %}ALEMIS - تقرير الإحصائيات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">تقرير الإحصائيات</h2>
        
        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-1"></i>
                تصفية النتائج
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('statistics_report') }}" class="report-filter">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="year" class="form-label">السنة</label>
                            <select class="form-select" id="year" name="year">
                                <option value="2023" {% if filters.year == '2023' %}selected{% endif %}>2023</option>
                                <option value="2024" {% if filters.year == '2024' %}selected{% endif %}>2024</option>
                                <option value="2025" {% if filters.year == '2025' %}selected{% endif %}>2025</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="all" {% if filters.department_id == 'all' %}selected{% endif %}>جميع الأقسام</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}" {% if filters.department_id|string == department.id|string %}selected{% endif %}>{{ department.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-redo me-1"></i>
                                إعادة تعيين
                            </button>
                        </div>
                        <div class="col-md-6 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-success w-100" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-1"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Summary Cards -->
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي الإجازات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_leaves }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي أيام الإجازات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_leave_days }} يوم</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    إجمالي طلبات التغطية
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_coverage_requests }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    إجمالي طلبات الأذونات
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_permissions }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Charts -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-bar me-1"></i>
                        الإجازات حسب الشهر
                    </div>
                    <div class="card-body">
                        <canvas id="leavesByMonthChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-pie me-1"></i>
                        الإجازات حسب النوع
                    </div>
                    <div class="card-body">
                        <canvas id="leavesByTypeChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-bar me-1"></i>
                        الإجازات حسب القسم
                    </div>
                    <div class="card-body">
                        <canvas id="leavesByDepartmentChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-chart-line me-1"></i>
                        معدل الموافقة على الإجازات
                    </div>
                    <div class="card-body">
                        <canvas id="approvalRateChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tables -->
        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-table me-1"></i>
                        إحصائيات الإجازات حسب النوع
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>نوع الإجازة</th>
                                        <th>عدد الطلبات</th>
                                        <th>إجمالي الأيام</th>
                                        <th>متوسط المدة</th>
                                        <th>نسبة الموافقة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in leave_type_stats %}
                                    <tr>
                                        <td>{{ stat.name }}</td>
                                        <td>{{ stat.count }}</td>
                                        <td>{{ stat.total_days }} يوم</td>
                                        <td>{{ stat.avg_days|round(1) }} يوم</td>
                                        <td>{{ (stat.approval_rate * 100)|round(1) }}%</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-table me-1"></i>
                        إحصائيات الإجازات حسب القسم
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>القسم</th>
                                        <th>عدد الموظفين</th>
                                        <th>عدد الإجازات</th>
                                        <th>إجمالي أيام الإجازات</th>
                                        <th>متوسط الإجازات لكل موظف</th>
                                        <th>متوسط أيام الإجازات لكل موظف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in department_stats %}
                                    <tr>
                                        <td>{{ stat.name }}</td>
                                        <td>{{ stat.employee_count }}</td>
                                        <td>{{ stat.leave_count }}</td>
                                        <td>{{ stat.total_days }} يوم</td>
                                        <td>{{ (stat.leave_count / stat.employee_count)|round(1) }}</td>
                                        <td>{{ (stat.total_days / stat.employee_count)|round(1) }} يوم</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    function resetFilters() {
        document.getElementById('year').value = '{{ current_year }}';
        document.getElementById('department_id').value = 'all';
    }
    
    function exportToPDF() {
        alert('سيتم تصدير التقرير إلى ملف PDF قريباً.');
        // This would typically use a library like jsPDF or call a server endpoint
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        // Leaves by Month Chart
        const leavesByMonthCtx = document.getElementById('leavesByMonthChart').getContext('2d');
        const leavesByMonthChart = new Chart(leavesByMonthCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'عدد الإجازات',
                    data: {{ leaves_by_month|tojson }},
                    backgroundColor: 'rgba(78, 115, 223, 0.5)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // Leaves by Type Chart
        const leavesByTypeCtx = document.getElementById('leavesByTypeChart').getContext('2d');
        const leavesByTypeChart = new Chart(leavesByTypeCtx, {
            type: 'pie',
            data: {
                labels: {{ leave_type_labels|tojson }},
                datasets: [{
                    data: {{ leave_type_data|tojson }},
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b',
                        '#5a5c69',
                        '#858796'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#dda20a',
                        '#be2617',
                        '#3a3b45',
                        '#60616f'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            },
        });
        
        // Leaves by Department Chart
        const leavesByDepartmentCtx = document.getElementById('leavesByDepartmentChart').getContext('2d');
        const leavesByDepartmentChart = new Chart(leavesByDepartmentCtx, {
            type: 'bar',
            data: {
                labels: {{ department_labels|tojson }},
                datasets: [{
                    label: 'عدد الإجازات',
                    data: {{ department_data|tojson }},
                    backgroundColor: 'rgba(28, 200, 138, 0.5)',
                    borderColor: 'rgba(28, 200, 138, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // Approval Rate Chart
        const approvalRateCtx = document.getElementById('approvalRateChart').getContext('2d');
        const approvalRateChart = new Chart(approvalRateCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'معدل الموافقة',
                    data: {{ approval_rate_data|tojson }},
                    backgroundColor: 'rgba(54, 185, 204, 0.05)',
                    borderColor: 'rgba(54, 185, 204, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(54, 185, 204, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(54, 185, 204, 1)',
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y + '%';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
