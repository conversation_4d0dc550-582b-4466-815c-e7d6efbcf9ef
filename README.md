# ALEMIS - نظام إدارة إجازات الموظفين في المختبرات

ALEMIS هو نظام متكامل لإدارة إجازات الموظفين في المختبرات، مبني باستخدام إطار عمل Flask في لغة Python.

## المميزات الرئيسية

### 1. إدارة المستخدمين والصلاحيات
- نظام أدوار متعدد (مدير نظام، مدير، موارد بشرية، مدير عام، موظف)
- تسجيل دخول آمن مع دعم المصادقة الثنائية
- إدارة الملفات الشخصية للموظفين
- تنظيم الموظفين في أقسام مختلفة

### 2. إدارة الإجازات
- دعم أنواع متعددة من الإجازات:
  - إجازة اعتيادية
  - إجازة مرضية
  - إجازة طارئة
  - إجازة بدون راتب
  - إجازة أمومة
  - إجازة حج
  - إجازة تغطية
- تتبع أرصدة الإجازات لكل موظف
- نظام موافقات متعدد المستويات (مدير، موارد بشرية)
- إمكانية إرفاق مستندات مع طلبات الإجازة

### 3. إدارة التغطية
- نظام لطلب تغطية الموظفين أثناء الإجازات
- تتبع طلبات التغطية والموافقات عليها

### 4. إدارة الأذونات
- نظام لطلب أذونات خاصة
- إمكانية إرفاق مستندات مع طلبات الأذونات

### 5. إدارة الجداول الشهرية
- جدولة العمل الشهري للموظفين
- تسجيل العطل الرسمية والإجازات

### 6. تدقيق وتقارير
- سجل تدقيق للعمليات المهمة في النظام
- إمكانية إنشاء تقارير مختلفة
- تصدير التقارير بصيغة PDF

### 7. إشعارات وتواصل
- نظام إشعارات للموظفين والمدراء
- إرسال بريد إلكتروني للإشعارات المهمة

## متطلبات النظام

- Python 3.6+
- Flask
- SQLAlchemy
- Flask-Login
- Flask-WTF
- Flask-Mail
- Flask-Babel
- Flask-Moment
- PyOTP
- وغيرها من المكتبات (انظر ملف requirements.txt)

## التثبيت

1. قم بنسخ المستودع:
```
git clone https://github.com/yourusername/alemis.git
cd alemis
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:
```
python -m venv venv
source venv/bin/activate  # على Linux/Mac
venv\Scripts\activate  # على Windows
```

3. قم بتثبيت المكتبات المطلوبة:
```
pip install -r requirements.txt
```

4. قم بتهيئة قاعدة البيانات:
```
python init_db.py
```

5. قم بتشغيل التطبيق:
```
python app.py
```

6. افتح المتصفح على العنوان:
```
http://localhost:5000
```

## حسابات المستخدمين الافتراضية

### الحسابات الإشرافية:

1. **مدير النظام**
   - اسم المستخدم: admin
   - كلمة المرور: admin123

2. **المدير العام**
   - اسم المستخدم: gm
   - كلمة المرور: gm123

3. **مدير الموارد البشرية**
   - اسم المستخدم: hr
   - كلمة المرور: hr123

4. **مدير المختبر الكيميائي**
   - اسم المستخدم: chem_manager
   - كلمة المرور: manager123

5. **مدير المختبر البيولوجي**
   - اسم المستخدم: bio_manager
   - كلمة المرور: manager123

6. **مدير مختبر الأبحاث**
   - اسم المستخدم: research_manager
   - كلمة المرور: manager123

### حسابات الموظفين:

- **موظفي المختبر الكيميائي**
  - اسم المستخدم: chem_emp1, chem_emp2, ... chem_emp5
  - كلمة المرور: emp123

- **موظفي المختبر البيولوجي**
  - اسم المستخدم: bio_emp1, bio_emp2, ... bio_emp5
  - كلمة المرور: emp123

- **موظفي مختبر الأبحاث**
  - اسم المستخدم: research_emp1, research_emp2, ... research_emp5
  - كلمة المرور: emp123

## هيكل المشروع

```
alemis/
├── app.py                  # تطبيق Flask الرئيسي
├── init_db.py              # سكريبت تهيئة قاعدة البيانات
├── requirements.txt        # متطلبات المكتبات
├── static/                 # الملفات الثابتة
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   └── img/                # الصور
└── templates/              # قوالب HTML
    ├── base.html           # القالب الأساسي
    ├── index.html          # الصفحة الرئيسية
    ├── login.html          # صفحة تسجيل الدخول
    └── dashboard.html      # لوحة التحكم
```

## المساهمة

نرحب بمساهماتكم في تطوير هذا المشروع. يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بعمل Commit للتغييرات (`git commit -m 'Add some amazing feature'`)
4. قم بدفع الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الاتصال

إذا كان لديك أي أسئلة أو استفسارات، يرجى التواصل معنا على:

- البريد الإلكتروني: <EMAIL>
- الموقع: www.alemis.com
