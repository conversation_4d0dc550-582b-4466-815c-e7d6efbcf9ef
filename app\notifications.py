"""
نظام الإشعارات المتقدم
Advanced Notification System
"""
import logging
import smtplib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from jinja2 import Template
from flask import current_app, url_for, render_template_string
from .models import User, Notification
from .database import db_manager
import json
import requests


logger = logging.getLogger(__name__)


class NotificationChannel:
    """قناة الإشعارات الأساسية"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
    
    def send(self, recipient: Union[User, str], title: str, message: str, **kwargs) -> bool:
        """إرسال الإشعار"""
        raise NotImplementedError("يجب تنفيذ دالة الإرسال في الفئة المشتقة")
    
    def is_available(self) -> bool:
        """التحقق من توفر القناة"""
        return self.enabled


class EmailChannel(NotificationChannel):
    """قناة البريد الإلكتروني"""
    
    def __init__(self):
        super().__init__("email")
        self.smtp_server = None
        self.smtp_port = None
        self.username = None
        self.password = None
        self.use_tls = True
        self.default_sender = None
    
    def configure(self, smtp_server: str, smtp_port: int, username: str, 
                 password: str, use_tls: bool = True, default_sender: str = None):
        """تكوين إعدادات البريد الإلكتروني"""
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        self.default_sender = default_sender or username
    
    def send(self, recipient: Union[User, str], title: str, message: str, 
             html_message: str = None, attachments: List[str] = None, **kwargs) -> bool:
        """إرسال بريد إلكتروني"""
        try:
            # تحديد عنوان المستلم
            if isinstance(recipient, User):
                recipient_email = recipient.email
                recipient_name = recipient.full_name
            else:
                recipient_email = recipient
                recipient_name = recipient
            
            # إنشاء الرسالة
            msg = MIMEMultipart('alternative')
            msg['Subject'] = title
            msg['From'] = self.default_sender
            msg['To'] = recipient_email
            
            # إضافة النص العادي
            text_part = MIMEText(message, 'plain', 'utf-8')
            msg.attach(text_part)
            
            # إضافة HTML إذا كان متوفراً
            if html_message:
                html_part = MIMEText(html_message, 'html', 'utf-8')
                msg.attach(html_part)
            
            # إضافة المرفقات
            if attachments:
                for file_path in attachments:
                    try:
                        with open(file_path, 'rb') as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                            encoders.encode_base64(part)
                            part.add_header(
                                'Content-Disposition',
                                f'attachment; filename= {file_path.split("/")[-1]}'
                            )
                            msg.attach(part)
                    except Exception as e:
                        logger.warning(f"فشل في إرفاق الملف {file_path}: {e}")
            
            # إرسال البريد
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"تم إرسال بريد إلكتروني إلى {recipient_email}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في إرسال البريد الإلكتروني إلى {recipient_email}: {e}")
            return False
    
    def is_available(self) -> bool:
        """التحقق من توفر البريد الإلكتروني"""
        return (self.enabled and self.smtp_server and self.smtp_port and 
                self.username and self.password)


class SMSChannel(NotificationChannel):
    """قناة الرسائل النصية"""
    
    def __init__(self):
        super().__init__("sms")
        self.api_url = None
        self.api_key = None
        self.sender_name = None
    
    def configure(self, api_url: str, api_key: str, sender_name: str = "ALEMIS"):
        """تكوين إعدادات الرسائل النصية"""
        self.api_url = api_url
        self.api_key = api_key
        self.sender_name = sender_name
    
    def send(self, recipient: Union[User, str], title: str, message: str, **kwargs) -> bool:
        """إرسال رسالة نصية"""
        try:
            # تحديد رقم الهاتف
            if isinstance(recipient, User):
                phone_number = recipient.phone
                if not phone_number:
                    logger.warning(f"لا يوجد رقم هاتف للمستخدم {recipient.username}")
                    return False
            else:
                phone_number = recipient
            
            # تنسيق الرسالة
            full_message = f"{title}\n{message}"
            
            # إرسال الرسالة عبر API
            payload = {
                'to': phone_number,
                'message': full_message,
                'sender': self.sender_name
            }
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(self.api_url, json=payload, headers=headers)
            
            if response.status_code == 200:
                logger.info(f"تم إرسال رسالة نصية إلى {phone_number}")
                return True
            else:
                logger.error(f"فشل في إرسال الرسالة النصية: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في إرسال الرسالة النصية: {e}")
            return False
    
    def is_available(self) -> bool:
        """التحقق من توفر الرسائل النصية"""
        return self.enabled and self.api_url and self.api_key


class InAppChannel(NotificationChannel):
    """قناة الإشعارات داخل التطبيق"""
    
    def __init__(self):
        super().__init__("in_app")
    
    def send(self, recipient: Union[User, str], title: str, message: str, 
             notification_type: str = "info", action_url: str = None, **kwargs) -> bool:
        """إرسال إشعار داخل التطبيق"""
        try:
            if isinstance(recipient, str):
                logger.error("الإشعارات داخل التطبيق تتطلب كائن مستخدم")
                return False
            
            # إنشاء الإشعار في قاعدة البيانات
            with db_manager.session_scope() as session:
                notification = Notification(
                    user_id=recipient.id,
                    title=title,
                    message=message,
                    type=notification_type,
                    action_url=action_url,
                    is_read=False,
                    created_at=datetime.utcnow()
                )
                session.add(notification)
                session.flush()
            
            logger.info(f"تم إنشاء إشعار داخل التطبيق للمستخدم {recipient.username}")
            return True
            
        except Exception as e:
            logger.error(f"فشل في إنشاء الإشعار داخل التطبيق: {e}")
            return False


class PushNotificationChannel(NotificationChannel):
    """قناة الإشعارات المدفوعة"""
    
    def __init__(self):
        super().__init__("push")
        self.firebase_key = None
    
    def configure(self, firebase_key: str):
        """تكوين إعدادات الإشعارات المدفوعة"""
        self.firebase_key = firebase_key
    
    def send(self, recipient: Union[User, str], title: str, message: str, **kwargs) -> bool:
        """إرسال إشعار مدفوع"""
        try:
            # سيتم تنفيذها لاحقاً مع Firebase
            logger.info(f"إشعار مدفوع: {title} - {message}")
            return True
        except Exception as e:
            logger.error(f"فشل في إرسال الإشعار المدفوع: {e}")
            return False


class NotificationManager:
    """مدير الإشعارات الرئيسي"""
    
    def __init__(self):
        self.channels: Dict[str, NotificationChannel] = {}
        self.templates: Dict[str, str] = {}
        self.default_channels = ['in_app']
        
        # تسجيل القنوات الافتراضية
        self.register_channel(EmailChannel())
        self.register_channel(SMSChannel())
        self.register_channel(InAppChannel())
        self.register_channel(PushNotificationChannel())
    
    def register_channel(self, channel: NotificationChannel):
        """تسجيل قناة إشعارات"""
        self.channels[channel.name] = channel
    
    def register_template(self, name: str, template: str):
        """تسجيل قالب إشعار"""
        self.templates[name] = template
    
    def send_notification(self, recipients: Union[User, List[User], str, List[str]], 
                         title: str, message: str, channels: List[str] = None,
                         template_name: str = None, template_data: Dict[str, Any] = None,
                         **kwargs) -> Dict[str, bool]:
        """إرسال إشعار عبر قنوات متعددة"""
        
        # تحديد القنوات المستخدمة
        if channels is None:
            channels = self.default_channels
        
        # تحديد المستلمين
        if not isinstance(recipients, list):
            recipients = [recipients]
        
        # تطبيق القالب إذا كان متوفراً
        if template_name and template_name in self.templates:
            template = Template(self.templates[template_name])
            message = template.render(template_data or {})
        
        results = {}
        
        # إرسال الإشعار عبر كل قناة
        for channel_name in channels:
            if channel_name not in self.channels:
                logger.warning(f"قناة الإشعار غير موجودة: {channel_name}")
                continue
            
            channel = self.channels[channel_name]
            if not channel.is_available():
                logger.warning(f"قناة الإشعار غير متاحة: {channel_name}")
                continue
            
            channel_results = []
            for recipient in recipients:
                try:
                    result = channel.send(recipient, title, message, **kwargs)
                    channel_results.append(result)
                except Exception as e:
                    logger.error(f"خطأ في إرسال الإشعار عبر {channel_name}: {e}")
                    channel_results.append(False)
            
            results[channel_name] = all(channel_results)
        
        return results
    
    def send_leave_request_notification(self, leave_request, action: str):
        """إرسال إشعار طلب إجازة"""
        user = leave_request.user
        
        if action == 'submitted':
            title = "طلب إجازة جديد"
            message = f"تم تقديم طلب إجازة من {user.full_name}"
            # إرسال للمدراء
            managers = self._get_managers_for_user(user)
            self.send_notification(managers, title, message)
        
        elif action == 'approved':
            title = "تمت الموافقة على طلب الإجازة"
            message = f"تمت الموافقة على طلب إجازتك من {leave_request.start_date} إلى {leave_request.end_date}"
            self.send_notification(user, title, message)
        
        elif action == 'rejected':
            title = "تم رفض طلب الإجازة"
            message = f"تم رفض طلب إجازتك من {leave_request.start_date} إلى {leave_request.end_date}"
            self.send_notification(user, title, message)
    
    def _get_managers_for_user(self, user: User) -> List[User]:
        """الحصول على المدراء المسؤولين عن المستخدم"""
        # سيتم تنفيذها لاحقاً
        return []


# إنشاء مثيل مدير الإشعارات
notification_manager = NotificationManager()

# تسجيل القوالب الافتراضية
notification_manager.register_template('leave_request_submitted', """
طلب إجازة جديد

المستخدم: {{ user.full_name }}
نوع الإجازة: {{ leave_type.name }}
من: {{ start_date }}
إلى: {{ end_date }}
السبب: {{ reason }}

يرجى مراجعة الطلب والموافقة عليه.
""")
