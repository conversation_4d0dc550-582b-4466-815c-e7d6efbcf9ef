<div class="request-card">
    <div class="request-header" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
        <i class="fas fa-exchange-alt me-2"></i>
        طلب تبديل دوام
    </div>
    <div class="request-body">
        <div class="request-info">
            <div>
                <strong>رقم الطلب:</strong> #{{ request.id }}
            </div>
            <div>
                <span class="badge status-badge bg-{{ status_badge }}">{{ status_text }}</span>
            </div>
        </div>
        
        <div class="request-details">
            <div class="row">
                <div class="col-6">
                    <small class="text-muted">الموظف الطالب</small>
                    <div><strong>{{ request.requester_first_name }} {{ request.requester_last_name }}</strong></div>
                </div>
                <div class="col-6">
                    <small class="text-muted">الموظف المستهدف</small>
                    <div><strong>
                        {% if request.target_first_name %}
                            {{ request.target_first_name }} {{ request.target_last_name }}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </strong></div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-6">
                    <small class="text-muted">نوع التبديل</small>
                    <div><strong>{{ request.swap_type }}</strong></div>
                </div>
                <div class="col-6">
                    <small class="text-muted">تاريخ التقديم</small>
                    <div><strong>{{ request.created_at[:10] }}</strong></div>
                </div>
            </div>
            
            {% if request.swap_date %}
            <div class="row mt-3">
                <div class="col-6">
                    <small class="text-muted">تاريخ التبديل</small>
                    <div><strong>{{ request.swap_date }}</strong></div>
                </div>
                {% if request.start_date and request.end_date %}
                <div class="col-6">
                    <small class="text-muted">فترة التبديل</small>
                    <div><strong>{{ request.start_date }} - {{ request.end_date }}</strong></div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            {% if request.shift_type %}
            <div class="row mt-3">
                <div class="col-6">
                    <small class="text-muted">الشفت المطلوب</small>
                    <div><strong>
                        {% if request.shift_type == 'morning' %}
                            صباحي (8ص-4م)
                        {% elif request.shift_type == 'evening' %}
                            مسائي (4م-12ل)
                        {% elif request.shift_type == 'night' %}
                            ليلي (12ل-8ص)
                        {% elif request.shift_type == 'split' %}
                            فترتين (9ص-12ظ، 4م-9م)
                        {% else %}
                            {{ request.shift_type }}
                        {% endif %}
                    </strong></div>
                </div>
                {% if request.requester_shift_type %}
                <div class="col-6">
                    <small class="text-muted">شفت الموظف الطالب</small>
                    <div><strong>
                        {% if request.requester_shift_type == 'morning' %}
                            صباحي (8ص-4م)
                        {% elif request.requester_shift_type == 'evening' %}
                            مسائي (4م-12ل)
                        {% elif request.requester_shift_type == 'night' %}
                            ليلي (12ل-8ص)
                        {% elif request.requester_shift_type == 'split' %}
                            فترتين (9ص-12ظ، 4م-9م)
                        {% else %}
                            {{ request.requester_shift_type }}
                        {% endif %}
                    </strong></div>
                </div>
                {% endif %}
            </div>
            {% endif %}
            
            {% if request.reason %}
            <div class="mt-3">
                <small class="text-muted">السبب</small>
                <div>{{ request.reason }}</div>
            </div>
            {% endif %}
            
            {% if request.manager_notes %}
            <div class="mt-3">
                <small class="text-muted">ملاحظات المدير</small>
                <div class="alert alert-info mb-0">{{ request.manager_notes }}</div>
            </div>
            {% endif %}
        </div>
        
        <div class="mt-3 d-flex justify-content-between align-items-center">
            <small class="text-muted">
                <i class="fas fa-clock me-1"></i>
                {{ request.created_at }}
            </small>
            {% if status == 'approved' %}
            <a href="{{ url_for('export_request_pdf', request_type='swap', request_id=request.id) }}" 
               class="btn btn-sm btn-outline-warning">
                <i class="fas fa-download me-1"></i>تصدير PDF
            </a>
            {% endif %}
        </div>
    </div>
</div>
