"""
نظام التقارير المتقدم
Advanced Reporting System
"""
import io
import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from sqlalchemy import func, and_, or_, extract
from .models import User, LeaveRequest, LeaveBalance, Department, LeaveType
from .database import db_manager


logger = logging.getLogger(__name__)


class ReportFormat(Enum):
    """تنسيقات التقارير"""
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"
    HTML = "html"


class ReportType(Enum):
    """أنواع التقارير"""
    LEAVE_SUMMARY = "leave_summary"
    EMPLOYEE_REPORT = "employee_report"
    DEPARTMENT_REPORT = "department_report"
    MONTHLY_REPORT = "monthly_report"
    ANNUAL_REPORT = "annual_report"
    ATTENDANCE_REPORT = "attendance_report"
    ANALYTICS_REPORT = "analytics_report"


@dataclass
class ReportFilter:
    """مرشح التقارير"""
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    department_id: Optional[int] = None
    user_id: Optional[int] = None
    leave_type_id: Optional[int] = None
    status: Optional[str] = None
    year: Optional[int] = None
    month: Optional[int] = None


class BaseReportGenerator:
    """مولد التقارير الأساسي"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.session = None
    
    def generate(self, report_type: ReportType, filters: ReportFilter, 
                format: ReportFormat = ReportFormat.PDF) -> bytes:
        """إنتاج التقرير"""
        with self.db_manager.session_scope() as session:
            self.session = session
            
            # جمع البيانات
            data = self._collect_data(report_type, filters)
            
            # إنتاج التقرير بالتنسيق المطلوب
            if format == ReportFormat.PDF:
                return self._generate_pdf(report_type, data, filters)
            elif format == ReportFormat.EXCEL:
                return self._generate_excel(report_type, data, filters)
            elif format == ReportFormat.CSV:
                return self._generate_csv(report_type, data, filters)
            elif format == ReportFormat.JSON:
                return self._generate_json(report_type, data, filters)
            elif format == ReportFormat.HTML:
                return self._generate_html(report_type, data, filters)
            else:
                raise ValueError(f"تنسيق التقرير غير مدعوم: {format}")
    
    def _collect_data(self, report_type: ReportType, filters: ReportFilter) -> Dict[str, Any]:
        """جمع بيانات التقرير"""
        if report_type == ReportType.LEAVE_SUMMARY:
            return self._collect_leave_summary_data(filters)
        elif report_type == ReportType.EMPLOYEE_REPORT:
            return self._collect_employee_report_data(filters)
        elif report_type == ReportType.DEPARTMENT_REPORT:
            return self._collect_department_report_data(filters)
        elif report_type == ReportType.MONTHLY_REPORT:
            return self._collect_monthly_report_data(filters)
        elif report_type == ReportType.ANNUAL_REPORT:
            return self._collect_annual_report_data(filters)
        else:
            raise ValueError(f"نوع التقرير غير مدعوم: {report_type}")
    
    def _collect_leave_summary_data(self, filters: ReportFilter) -> Dict[str, Any]:
        """جمع بيانات ملخص الإجازات"""
        query = self.session.query(LeaveRequest).join(User).join(LeaveType)
        
        # تطبيق المرشحات
        if filters.start_date:
            query = query.filter(LeaveRequest.start_date >= filters.start_date)
        if filters.end_date:
            query = query.filter(LeaveRequest.end_date <= filters.end_date)
        if filters.department_id:
            query = query.filter(User.department_id == filters.department_id)
        if filters.user_id:
            query = query.filter(LeaveRequest.user_id == filters.user_id)
        if filters.leave_type_id:
            query = query.filter(LeaveRequest.leave_type_id == filters.leave_type_id)
        if filters.status:
            query = query.filter(LeaveRequest.status == filters.status)
        
        leave_requests = query.all()
        
        # إحصائيات عامة
        total_requests = len(leave_requests)
        approved_requests = len([lr for lr in leave_requests if lr.status == 'approved'])
        rejected_requests = len([lr for lr in leave_requests if lr.status == 'rejected'])
        pending_requests = len([lr for lr in leave_requests if lr.status == 'pending'])
        
        # إحصائيات حسب نوع الإجازة
        leave_type_stats = {}
        for lr in leave_requests:
            leave_type = lr.leave_type.name
            if leave_type not in leave_type_stats:
                leave_type_stats[leave_type] = {'count': 0, 'days': 0}
            leave_type_stats[leave_type]['count'] += 1
            leave_type_stats[leave_type]['days'] += lr.days_requested or 0
        
        # إحصائيات حسب القسم
        department_stats = {}
        for lr in leave_requests:
            dept_name = lr.user.department.name if lr.user.department else 'غير محدد'
            if dept_name not in department_stats:
                department_stats[dept_name] = {'count': 0, 'days': 0}
            department_stats[dept_name]['count'] += 1
            department_stats[dept_name]['days'] += lr.days_requested or 0
        
        return {
            'leave_requests': leave_requests,
            'summary': {
                'total_requests': total_requests,
                'approved_requests': approved_requests,
                'rejected_requests': rejected_requests,
                'pending_requests': pending_requests,
                'approval_rate': (approved_requests / total_requests * 100) if total_requests > 0 else 0
            },
            'leave_type_stats': leave_type_stats,
            'department_stats': department_stats,
            'filters': filters
        }
    
    def _collect_employee_report_data(self, filters: ReportFilter) -> Dict[str, Any]:
        """جمع بيانات تقرير الموظف"""
        if not filters.user_id:
            raise ValueError("معرف المستخدم مطلوب لتقرير الموظف")
        
        user = self.session.query(User).filter(User.id == filters.user_id).first()
        if not user:
            raise ValueError("المستخدم غير موجود")
        
        # طلبات الإجازة
        leave_requests_query = self.session.query(LeaveRequest).filter(
            LeaveRequest.user_id == filters.user_id
        )
        
        if filters.start_date:
            leave_requests_query = leave_requests_query.filter(
                LeaveRequest.start_date >= filters.start_date
            )
        if filters.end_date:
            leave_requests_query = leave_requests_query.filter(
                LeaveRequest.end_date <= filters.end_date
            )
        
        leave_requests = leave_requests_query.all()
        
        # أرصدة الإجازات
        current_year = filters.year or datetime.now().year
        leave_balances = self.session.query(LeaveBalance).filter(
            and_(
                LeaveBalance.user_id == filters.user_id,
                LeaveBalance.year == current_year
            )
        ).all()
        
        return {
            'user': user,
            'leave_requests': leave_requests,
            'leave_balances': leave_balances,
            'filters': filters
        }
    
    def _generate_pdf(self, report_type: ReportType, data: Dict[str, Any], 
                     filters: ReportFilter) -> bytes:
        """إنتاج تقرير PDF"""
        buffer = io.BytesIO()
        
        # إعداد الوثيقة
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                               topMargin=72, bottomMargin=18)
        
        # إعداد الأنماط
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # وسط
            textColor=colors.darkblue
        )
        
        # محتوى التقرير
        story = []
        
        # العنوان
        if report_type == ReportType.LEAVE_SUMMARY:
            title = "تقرير ملخص الإجازات"
        elif report_type == ReportType.EMPLOYEE_REPORT:
            title = f"تقرير الموظف - {data['user'].full_name}"
        else:
            title = "تقرير النظام"
        
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 12))
        
        # معلومات التقرير
        report_info = f"تاريخ الإنتاج: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        if filters.start_date:
            report_info += f"<br/>من: {filters.start_date}"
        if filters.end_date:
            report_info += f"<br/>إلى: {filters.end_date}"
        
        story.append(Paragraph(report_info, styles['Normal']))
        story.append(Spacer(1, 12))
        
        # محتوى التقرير حسب النوع
        if report_type == ReportType.LEAVE_SUMMARY:
            story.extend(self._generate_leave_summary_pdf_content(data, styles))
        elif report_type == ReportType.EMPLOYEE_REPORT:
            story.extend(self._generate_employee_pdf_content(data, styles))
        
        # بناء الوثيقة
        doc.build(story)
        
        buffer.seek(0)
        return buffer.getvalue()
    
    def _generate_leave_summary_pdf_content(self, data: Dict[str, Any], styles) -> List:
        """إنتاج محتوى تقرير ملخص الإجازات PDF"""
        story = []
        
        # الإحصائيات العامة
        summary = data['summary']
        summary_data = [
            ['الإحصائية', 'القيمة'],
            ['إجمالي الطلبات', str(summary['total_requests'])],
            ['الطلبات المعتمدة', str(summary['approved_requests'])],
            ['الطلبات المرفوضة', str(summary['rejected_requests'])],
            ['الطلبات المعلقة', str(summary['pending_requests'])],
            ['معدل الموافقة', f"{summary['approval_rate']:.1f}%"]
        ]
        
        summary_table = Table(summary_data)
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(Paragraph("الإحصائيات العامة", styles['Heading2']))
        story.append(summary_table)
        story.append(Spacer(1, 12))
        
        return story
    
    def _generate_excel(self, report_type: ReportType, data: Dict[str, Any], 
                       filters: ReportFilter) -> bytes:
        """إنتاج تقرير Excel"""
        buffer = io.BytesIO()
        
        with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
            if report_type == ReportType.LEAVE_SUMMARY:
                # ورقة الملخص
                summary_df = pd.DataFrame([data['summary']])
                summary_df.to_excel(writer, sheet_name='الملخص', index=False)
                
                # ورقة الطلبات
                if data['leave_requests']:
                    requests_data = []
                    for lr in data['leave_requests']:
                        requests_data.append({
                            'المستخدم': lr.user.full_name,
                            'نوع الإجازة': lr.leave_type.name,
                            'تاريخ البداية': lr.start_date,
                            'تاريخ النهاية': lr.end_date,
                            'عدد الأيام': lr.days_requested,
                            'الحالة': lr.status,
                            'تاريخ الإنشاء': lr.created_at
                        })
                    
                    requests_df = pd.DataFrame(requests_data)
                    requests_df.to_excel(writer, sheet_name='الطلبات', index=False)
        
        buffer.seek(0)
        return buffer.getvalue()
    
    def _generate_csv(self, report_type: ReportType, data: Dict[str, Any], 
                     filters: ReportFilter) -> bytes:
        """إنتاج تقرير CSV"""
        buffer = io.StringIO()
        
        if report_type == ReportType.LEAVE_SUMMARY and data['leave_requests']:
            # إنشاء DataFrame
            requests_data = []
            for lr in data['leave_requests']:
                requests_data.append({
                    'المستخدم': lr.user.full_name,
                    'نوع الإجازة': lr.leave_type.name,
                    'تاريخ البداية': lr.start_date,
                    'تاريخ النهاية': lr.end_date,
                    'عدد الأيام': lr.days_requested,
                    'الحالة': lr.status,
                    'تاريخ الإنشاء': lr.created_at
                })
            
            df = pd.DataFrame(requests_data)
            df.to_csv(buffer, index=False, encoding='utf-8-sig')
        
        return buffer.getvalue().encode('utf-8-sig')
    
    def _generate_json(self, report_type: ReportType, data: Dict[str, Any], 
                      filters: ReportFilter) -> bytes:
        """إنتاج تقرير JSON"""
        import json
        
        # تحويل البيانات إلى تنسيق قابل للتسلسل
        serializable_data = self._make_serializable(data)
        
        json_str = json.dumps(serializable_data, ensure_ascii=False, indent=2)
        return json_str.encode('utf-8')
    
    def _make_serializable(self, obj):
        """تحويل الكائنات إلى تنسيق قابل للتسلسل"""
        if hasattr(obj, 'to_dict'):
            return obj.to_dict()
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {key: self._make_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        else:
            return obj


class AnalyticsReportGenerator(BaseReportGenerator):
    """مولد تقارير التحليلات"""
    
    def generate_analytics_dashboard_data(self, filters: ReportFilter) -> Dict[str, Any]:
        """إنتاج بيانات لوحة التحليلات"""
        with self.db_manager.session_scope() as session:
            self.session = session
            
            # إحصائيات عامة
            total_employees = session.query(User).filter(User.role == 'employee').count()
            total_requests = session.query(LeaveRequest).count()
            
            # طلبات الشهر الحالي
            current_month = datetime.now().replace(day=1)
            monthly_requests = session.query(LeaveRequest).filter(
                LeaveRequest.created_at >= current_month
            ).count()
            
            # معدل الموافقة
            approved_requests = session.query(LeaveRequest).filter(
                LeaveRequest.status == 'approved'
            ).count()
            approval_rate = (approved_requests / total_requests * 100) if total_requests > 0 else 0
            
            # الاتجاهات الشهرية
            monthly_trends = self._get_monthly_trends()
            
            # توزيع أنواع الإجازات
            leave_type_distribution = self._get_leave_type_distribution()
            
            # توزيع الأقسام
            department_distribution = self._get_department_distribution()
            
            return {
                'summary': {
                    'total_employees': total_employees,
                    'total_requests': total_requests,
                    'monthly_requests': monthly_requests,
                    'approval_rate': approval_rate
                },
                'monthly_trends': monthly_trends,
                'leave_type_distribution': leave_type_distribution,
                'department_distribution': department_distribution
            }
    
    def _get_monthly_trends(self) -> List[Dict[str, Any]]:
        """الحصول على الاتجاهات الشهرية"""
        # سيتم تنفيذها لاحقاً
        return []
    
    def _get_leave_type_distribution(self) -> List[Dict[str, Any]]:
        """الحصول على توزيع أنواع الإجازات"""
        # سيتم تنفيذها لاحقاً
        return []
    
    def _get_department_distribution(self) -> List[Dict[str, Any]]:
        """الحصول على توزيع الأقسام"""
        # سيتم تنفيذها لاحقاً
        return []


# إنشاء مولدات التقارير
report_generator = BaseReportGenerator(db_manager)
analytics_generator = AnalyticsReportGenerator(db_manager)
