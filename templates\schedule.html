{% extends "base.html" %}

{% block title %}ALEMIS - الجدول الشهري{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">الجدول الشهري</h2>
        
        <!-- Schedule Controls -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar-week me-1"></i>
                {{ month_name }} {{ year }}
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('schedule') }}" class="row">
                    <div class="col-md-3 mb-3">
                        <label for="department_id" class="form-label">القسم</label>
                        <select class="form-select" id="department_id" name="department_id" onchange="this.form.submit()">
                            {% for department in departments %}
                            <option value="{{ department.id }}" {% if department_id == department.id %}selected{% endif %}>{{ department.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="month" class="form-label">الشهر</label>
                        <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                            <option value="1" {% if month == 1 %}selected{% endif %}>يناير</option>
                            <option value="2" {% if month == 2 %}selected{% endif %}>فبراير</option>
                            <option value="3" {% if month == 3 %}selected{% endif %}>مارس</option>
                            <option value="4" {% if month == 4 %}selected{% endif %}>أبريل</option>
                            <option value="5" {% if month == 5 %}selected{% endif %}>مايو</option>
                            <option value="6" {% if month == 6 %}selected{% endif %}>يونيو</option>
                            <option value="7" {% if month == 7 %}selected{% endif %}>يوليو</option>
                            <option value="8" {% if month == 8 %}selected{% endif %}>أغسطس</option>
                            <option value="9" {% if month == 9 %}selected{% endif %}>سبتمبر</option>
                            <option value="10" {% if month == 10 %}selected{% endif %}>أكتوبر</option>
                            <option value="11" {% if month == 11 %}selected{% endif %}>نوفمبر</option>
                            <option value="12" {% if month == 12 %}selected{% endif %}>ديسمبر</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="year" class="form-label">السنة</label>
                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                            {% for y in range(year-2, year+3) %}
                            <option value="{{ y }}" {% if year == y %}selected{% endif %}>{{ y }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <button type="button" class="btn btn-success w-100" onclick="printSchedule()">
                            <i class="fas fa-print me-1"></i>
                            طباعة الجدول
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Schedule Calendar -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-table me-1"></i>
                جدول الإجازات
            </div>
            <div class="card-body">
                <div class="calendar-container" id="scheduleCalendar">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th style="width: 200px;">الموظف</th>
                                    {% for day in range(1, num_days + 1) %}
                                    <th style="width: 40px;" class="text-center">{{ day }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr>
                                    <td>{{ employee.first_name }} {{ employee.last_name }}</td>
                                    {% for day in range(num_days) %}
                                    <td class="text-center calendar-day {% if calendar_data[employee.id]['days'][day] %}has-leave{% endif %}">
                                        {% if calendar_data[employee.id]['days'][day] %}
                                        <div class="leave-indicator" title="{{ calendar_data[employee.id]['days'][day]['type'] }}">
                                            <span class="badge 
                                                {% if calendar_data[employee.id]['days'][day]['type'] == 'إجازة اعتيادية' %}bg-primary
                                                {% elif calendar_data[employee.id]['days'][day]['type'] == 'إجازة مرضية' %}bg-danger
                                                {% elif calendar_data[employee.id]['days'][day]['type'] == 'إجازة طارئة' %}bg-warning
                                                {% elif calendar_data[employee.id]['days'][day]['type'] == 'إجازة بدون راتب' %}bg-secondary
                                                {% elif calendar_data[employee.id]['days'][day]['type'] == 'إجازة أمومة' %}bg-info
                                                {% elif calendar_data[employee.id]['days'][day]['type'] == 'إجازة حج' %}bg-success
                                                {% elif calendar_data[employee.id]['days'][day]['type'] == 'إجازة تغطية' %}bg-dark
                                                {% else %}bg-light{% endif %}">
                                                <i class="fas fa-check"></i>
                                            </span>
                                        </div>
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Legend -->
                <div class="mt-3">
                    <h5>مفتاح الألوان</h5>
                    <div class="d-flex flex-wrap">
                        <div class="me-3 mb-2">
                            <span class="badge bg-primary">&nbsp;</span>
                            <span class="ms-1">إجازة اعتيادية</span>
                        </div>
                        <div class="me-3 mb-2">
                            <span class="badge bg-danger">&nbsp;</span>
                            <span class="ms-1">إجازة مرضية</span>
                        </div>
                        <div class="me-3 mb-2">
                            <span class="badge bg-warning">&nbsp;</span>
                            <span class="ms-1">إجازة طارئة</span>
                        </div>
                        <div class="me-3 mb-2">
                            <span class="badge bg-secondary">&nbsp;</span>
                            <span class="ms-1">إجازة بدون راتب</span>
                        </div>
                        <div class="me-3 mb-2">
                            <span class="badge bg-info">&nbsp;</span>
                            <span class="ms-1">إجازة أمومة</span>
                        </div>
                        <div class="me-3 mb-2">
                            <span class="badge bg-success">&nbsp;</span>
                            <span class="ms-1">إجازة حج</span>
                        </div>
                        <div class="me-3 mb-2">
                            <span class="badge bg-dark">&nbsp;</span>
                            <span class="ms-1">إجازة تغطية</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .calendar-day {
        padding: 0.25rem !important;
        height: 40px;
        width: 40px;
        position: relative;
    }
    
    .leave-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    
    .has-leave {
        background-color: rgba(0, 0, 0, 0.05);
    }
    
    @media print {
        .navbar, .card-header, form, .btn, footer {
            display: none !important;
        }
        
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        
        .card-body {
            padding: 0 !important;
        }
        
        body {
            padding: 0 !important;
            margin: 0 !important;
        }
        
        h2 {
            text-align: center;
            margin-bottom: 20px;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    function printSchedule() {
        window.print();
    }
</script>
{% endblock %}
