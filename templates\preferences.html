{% extends "base.html" %}

{% block title %}ALEMIS - الإعدادات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">إعدادات المستخدم</h2>

        <div class="row">
            <div class="col-md-3">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-list me-1"></i>
                        قائمة الإعدادات
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush" id="settings-tabs" role="tablist">
                            <a class="list-group-item list-group-item-action active" id="general-tab" data-bs-toggle="list" href="#general" role="tab" aria-controls="general">
                                <i class="fas fa-cog me-2"></i> الإعدادات العامة
                            </a>
                            <a class="list-group-item list-group-item-action" id="notifications-tab" data-bs-toggle="list" href="#notifications" role="tab" aria-controls="notifications">
                                <i class="fas fa-bell me-2"></i> الإشعارات
                            </a>
                            <a class="list-group-item list-group-item-action" id="appearance-tab" data-bs-toggle="list" href="#appearance" role="tab" aria-controls="appearance">
                                <i class="fas fa-palette me-2"></i> المظهر
                            </a>
                            <a class="list-group-item list-group-item-action" id="dashboard-tab" data-bs-toggle="list" href="#dashboard" role="tab" aria-controls="dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                            </a>
                            <a class="list-group-item list-group-item-action" id="privacy-tab" data-bs-toggle="list" href="#privacy" role="tab" aria-controls="privacy">
                                <i class="fas fa-user-shield me-2"></i> الخصوصية والأمان
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="tab-content">
                    <!-- الإعدادات العامة -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-cog me-1"></i>
                                الإعدادات العامة
                            </div>
                            <div class="card-body">
                                <form id="general-settings-form">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">اللغة</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="ar" selected>العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai">دبي (GMT+4)</option>
                                            <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                            <option value="Asia/Bahrain">البحرين (GMT+3)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="date-format" class="form-label">تنسيق التاريخ</label>
                                        <select class="form-select" id="date-format" name="date_format">
                                            <option value="dd/mm/yyyy" selected>يوم/شهر/سنة (31/12/2023)</option>
                                            <option value="mm/dd/yyyy">شهر/يوم/سنة (12/31/2023)</option>
                                            <option value="yyyy-mm-dd">سنة-شهر-يوم (2023-12-31)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="time-format" class="form-label">تنسيق الوقت</label>
                                        <select class="form-select" id="time-format" name="time_format">
                                            <option value="12" selected>12 ساعة (مساءً/صباحاً)</option>
                                            <option value="24">24 ساعة</option>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الإشعارات -->
                    <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-bell me-1"></i>
                                إعدادات الإشعارات
                            </div>
                            <div class="card-body">
                                <form id="notifications-settings-form">
                                    <h5 class="mb-3">إشعارات البريد الإلكتروني</h5>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="email-leave-requests" name="email_leave_requests" checked>
                                        <label class="form-check-label" for="email-leave-requests">طلبات الإجازات</label>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="email-coverage-requests" name="email_coverage_requests" checked>
                                        <label class="form-check-label" for="email-coverage-requests">طلبات التغطية</label>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="email-shift-swaps" name="email_shift_swaps" checked>
                                        <label class="form-check-label" for="email-shift-swaps">طلبات تبديل الدوام</label>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="email-schedule-updates" name="email_schedule_updates" checked>
                                        <label class="form-check-label" for="email-schedule-updates">تحديثات جدول الدوام</label>
                                    </div>

                                    <h5 class="mb-3 mt-4">إشعارات النظام</h5>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="system-leave-requests" name="system_leave_requests" checked>
                                        <label class="form-check-label" for="system-leave-requests">طلبات الإجازات</label>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="system-coverage-requests" name="system_coverage_requests" checked>
                                        <label class="form-check-label" for="system-coverage-requests">طلبات التغطية</label>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="system-shift-swaps" name="system_shift_swaps" checked>
                                        <label class="form-check-label" for="system-shift-swaps">طلبات تبديل الدوام</label>
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="system-schedule-updates" name="system_schedule_updates" checked>
                                        <label class="form-check-label" for="system-schedule-updates">تحديثات جدول الدوام</label>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات المظهر -->
                    <div class="tab-pane fade" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-palette me-1"></i>
                                إعدادات المظهر
                            </div>
                            <div class="card-body">
                                <form id="appearance-settings-form">
                                    <div class="mb-4">
                                        <label class="form-label">وضع العرض</label>
                                        <div class="d-flex">
                                            <div class="form-check me-4">
                                                <input class="form-check-input" type="radio" name="theme_mode" id="theme-light" value="light" checked>
                                                <label class="form-check-label" for="theme-light">
                                                    <i class="fas fa-sun me-1 text-warning"></i> فاتح
                                                </label>
                                            </div>
                                            <div class="form-check me-4">
                                                <input class="form-check-input" type="radio" name="theme_mode" id="theme-dark" value="dark">
                                                <label class="form-check-label" for="theme-dark">
                                                    <i class="fas fa-moon me-1 text-primary"></i> داكن
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="theme_mode" id="theme-auto" value="auto">
                                                <label class="form-check-label" for="theme-auto">
                                                    <i class="fas fa-adjust me-1"></i> تلقائي
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">اللون الرئيسي</label>
                                        <div class="d-flex flex-wrap">
                                            <div class="form-check me-4 mb-2">
                                                <input class="form-check-input" type="radio" name="primary_color" id="color-blue" value="blue" checked>
                                                <label class="form-check-label" for="color-blue">
                                                    <span class="color-swatch" style="background-color: #1a4cb8;"></span> أزرق
                                                </label>
                                            </div>
                                            <div class="form-check me-4 mb-2">
                                                <input class="form-check-input" type="radio" name="primary_color" id="color-green" value="green">
                                                <label class="form-check-label" for="color-green">
                                                    <span class="color-swatch" style="background-color: #198754;"></span> أخضر
                                                </label>
                                            </div>
                                            <div class="form-check me-4 mb-2">
                                                <input class="form-check-input" type="radio" name="primary_color" id="color-purple" value="purple">
                                                <label class="form-check-label" for="color-purple">
                                                    <span class="color-swatch" style="background-color: #6f42c1;"></span> بنفسجي
                                                </label>
                                            </div>
                                            <div class="form-check me-4 mb-2">
                                                <input class="form-check-input" type="radio" name="primary_color" id="color-red" value="red">
                                                <label class="form-check-label" for="color-red">
                                                    <span class="color-swatch" style="background-color: #dc3545;"></span> أحمر
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="font-size" class="form-label">حجم الخط</label>
                                        <select class="form-select" id="font-size" name="font_size">
                                            <option value="small">صغير</option>
                                            <option value="medium" selected>متوسط</option>
                                            <option value="large">كبير</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات لوحة التحكم -->
                    <div class="tab-pane fade" id="dashboard" role="tabpanel" aria-labelledby="dashboard-tab">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                إعدادات لوحة التحكم
                            </div>
                            <div class="card-body">
                                <form id="dashboard-settings-form">
                                    <div class="mb-4">
                                        <label class="form-label">العناصر المعروضة</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3 form-check">
                                                    <input type="checkbox" class="form-check-input" id="show-leave-balance" name="show_leave_balance" checked>
                                                    <label class="form-check-label" for="show-leave-balance">رصيد الإجازات</label>
                                                </div>
                                                <div class="mb-3 form-check">
                                                    <input type="checkbox" class="form-check-input" id="show-pending-requests" name="show_pending_requests" checked>
                                                    <label class="form-check-label" for="show-pending-requests">الطلبات المعلقة</label>
                                                </div>
                                                <div class="mb-3 form-check">
                                                    <input type="checkbox" class="form-check-input" id="show-approved-requests" name="show_approved_requests" checked>
                                                    <label class="form-check-label" for="show-approved-requests">الطلبات الموافق عليها</label>
                                                </div>
                                                <div class="mb-3 form-check">
                                                    <input type="checkbox" class="form-check-input" id="show-rejected-requests" name="show_rejected_requests" checked>
                                                    <label class="form-check-label" for="show-rejected-requests">الطلبات المرفوضة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3 form-check">
                                                    <input type="checkbox" class="form-check-input" id="show-shift-schedule" name="show_shift_schedule" checked>
                                                    <label class="form-check-label" for="show-shift-schedule">جدول الدوام</label>
                                                </div>
                                                <div class="mb-3 form-check">
                                                    <input type="checkbox" class="form-check-input" id="show-leave-chart" name="show_leave_chart" checked>
                                                    <label class="form-check-label" for="show-leave-chart">رسم بياني للإجازات</label>
                                                </div>
                                                <div class="mb-3 form-check">
                                                    <input type="checkbox" class="form-check-input" id="show-requests-chart" name="show_requests_chart" checked>
                                                    <label class="form-check-label" for="show-requests-chart">رسم بياني للطلبات</label>
                                                </div>
                                                <div class="mb-3 form-check">
                                                    <input type="checkbox" class="form-check-input" id="show-recent-activities" name="show_recent_activities">
                                                    <label class="form-check-label" for="show-recent-activities">النشاطات الأخيرة</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="dashboard-layout" class="form-label">تخطيط لوحة التحكم</label>
                                        <select class="form-select" id="dashboard-layout" name="dashboard_layout">
                                            <option value="default" selected>افتراضي</option>
                                            <option value="compact">مضغوط</option>
                                            <option value="expanded">موسع</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إعدادات الخصوصية والأمان -->
                    <div class="tab-pane fade" id="privacy" role="tabpanel" aria-labelledby="privacy-tab">
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-user-shield me-1"></i>
                                الخصوصية والأمان
                            </div>
                            <div class="card-body">
                                <form id="privacy-settings-form">
                                    <div class="mb-4">
                                        <h5>تغيير كلمة المرور</h5>
                                        <div class="mb-3">
                                            <label for="current-password" class="form-label">كلمة المرور الحالية</label>
                                            <input type="password" class="form-control" id="current-password" name="current_password">
                                        </div>
                                        <div class="mb-3">
                                            <label for="new-password" class="form-label">كلمة المرور الجديدة</label>
                                            <input type="password" class="form-control" id="new-password" name="new_password">
                                        </div>
                                        <div class="mb-3">
                                            <label for="confirm-password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                                            <input type="password" class="form-control" id="confirm-password" name="confirm_password">
                                        </div>
                                        <button type="button" class="btn btn-primary" id="change-password-btn">
                                            <i class="fas fa-key me-1"></i>
                                            تغيير كلمة المرور
                                        </button>
                                    </div>

                                    <hr>

                                    <div class="mb-4">
                                        <h5>إعدادات الخصوصية</h5>
                                        <div class="mb-3 form-check">
                                            <input type="checkbox" class="form-check-input" id="show-profile-info" name="show_profile_info" checked>
                                            <label class="form-check-label" for="show-profile-info">عرض معلومات الملف الشخصي للموظفين الآخرين</label>
                                        </div>
                                        <div class="mb-3 form-check">
                                            <input type="checkbox" class="form-check-input" id="show-leave-status" name="show_leave_status" checked>
                                            <label class="form-check-label" for="show-leave-status">عرض حالة الإجازات للموظفين الآخرين</label>
                                        </div>
                                        <div class="mb-3 form-check">
                                            <input type="checkbox" class="form-check-input" id="show-online-status" name="show_online_status" checked>
                                            <label class="form-check-label" for="show-online-status">عرض حالة الاتصال</label>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="mb-4">
                                        <h5>جلسات تسجيل الدخول</h5>
                                        <p>آخر تسجيل دخول: <strong>{{ login_date|default('اليوم، 10:30 صباحاً') }}</strong></p>
                                        <button type="button" class="btn btn-warning" id="logout-all-sessions-btn">
                                            <i class="fas fa-sign-out-alt me-1"></i>
                                            تسجيل الخروج من جميع الأجهزة
                                        </button>
                                    </div>

                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .list-group-item.active {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .color-swatch {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 5px;
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حفظ الإعدادات العامة
        document.getElementById('general-settings-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم حفظ الإعدادات العامة بنجاح');
        });

        // حفظ إعدادات الإشعارات
        document.getElementById('notifications-settings-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم حفظ إعدادات الإشعارات بنجاح');
        });

        // حفظ إعدادات المظهر
        document.getElementById('appearance-settings-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // تطبيق وضع العرض
            const themeMode = document.querySelector('input[name="theme_mode"]:checked').value;
            const toggleSwitch = document.getElementById('checkbox');

            if (themeMode === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
                if (toggleSwitch) toggleSwitch.checked = true;
            } else if (themeMode === 'light') {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
                if (toggleSwitch) toggleSwitch.checked = false;
            } else {
                // وضع تلقائي حسب تفضيلات المتصفح
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                    if (toggleSwitch) toggleSwitch.checked = true;
                } else {
                    document.documentElement.setAttribute('data-theme', 'light');
                    localStorage.setItem('theme', 'light');
                    if (toggleSwitch) toggleSwitch.checked = false;
                }
            }

            alert('تم حفظ إعدادات المظهر بنجاح');
        });

        // حفظ إعدادات لوحة التحكم
        document.getElementById('dashboard-settings-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم حفظ إعدادات لوحة التحكم بنجاح');
        });

        // حفظ إعدادات الخصوصية والأمان
        document.getElementById('privacy-settings-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم حفظ إعدادات الخصوصية والأمان بنجاح');
        });

        // تغيير كلمة المرور
        document.getElementById('change-password-btn').addEventListener('click', function() {
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            if (!currentPassword || !newPassword || !confirmPassword) {
                alert('يرجى ملء جميع حقول كلمة المرور');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
                return;
            }

            alert('تم تغيير كلمة المرور بنجاح');

            // إعادة تعيين الحقول
            document.getElementById('current-password').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('confirm-password').value = '';
        });

        // تسجيل الخروج من جميع الأجهزة
        document.getElementById('logout-all-sessions-btn').addEventListener('click', function() {
            if (confirm('هل أنت متأكد من تسجيل الخروج من جميع الأجهزة؟')) {
                alert('تم تسجيل الخروج من جميع الأجهزة بنجاح');
            }
        });

        // تعيين وضع العرض الحالي
        const currentTheme = localStorage.getItem('theme');
        if (currentTheme === 'dark') {
            document.getElementById('theme-dark').checked = true;
        } else if (currentTheme === 'light') {
            document.getElementById('theme-light').checked = true;
        } else {
            document.getElementById('theme-auto').checked = true;
        }
    });
</script>
{% endblock %}
