{% extends "base.html" %}

{% block title %}ALEMIS - تقرير الموظفين{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">تقرير الموظفين</h2>
        
        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-1"></i>
                تصفية النتائج
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('employee_report') }}" class="report-filter">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="all" {% if filters.department_id == 'all' %}selected{% endif %}>جميع الأقسام</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}" {% if filters.department_id|string == department.id|string %}selected{% endif %}>{{ department.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="role" class="form-label">الدور</label>
                            <select class="form-select" id="role" name="role">
                                <option value="all" {% if filters.role == 'all' %}selected{% endif %}>جميع الأدوار</option>
                                <option value="admin" {% if filters.role == 'admin' %}selected{% endif %}>مدير النظام</option>
                                <option value="hr" {% if filters.role == 'hr' %}selected{% endif %}>موارد بشرية</option>
                                <option value="manager" {% if filters.role == 'manager' %}selected{% endif %}>مدير قسم</option>
                                <option value="gm" {% if filters.role == 'gm' %}selected{% endif %}>مدير عام</option>
                                <option value="employee" {% if filters.role == 'employee' %}selected{% endif %}>موظف</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" {% if filters.status == 'all' %}selected{% endif %}>الكل</option>
                                <option value="active" {% if filters.status == 'active' %}selected{% endif %}>نشط</option>
                                <option value="inactive" {% if filters.status == 'inactive' %}selected{% endif %}>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-redo me-1"></i>
                                إعادة تعيين
                            </button>
                        </div>
                        <div class="col-md-6 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-success w-100" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-1"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Summary -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-chart-pie me-1"></i>
                ملخص
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي الموظفين</h5>
                                <h2 class="mb-0">{{ total_employees }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">موظفين نشطين</h5>
                                <h2 class="mb-0">{{ active_employees }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h5 class="card-title">موظفين غير نشطين</h5>
                                <h2 class="mb-0">{{ inactive_employees }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">في إجازة حالياً</h5>
                                <h2 class="mb-0">{{ on_leave_employees }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h5>توزيع الموظفين حسب القسم</h5>
                        <canvas id="departmentChart" height="200"></canvas>
                    </div>
                    <div class="col-md-6">
                        <h5>توزيع الموظفين حسب الدور</h5>
                        <canvas id="roleChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Results -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-table me-1"></i>
                قائمة الموظفين
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="employeeReportTable">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>القسم</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ الانضمام</th>
                                <th>رصيد الإجازات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees %}
                            <tr>
                                <td>{{ employee.first_name }} {{ employee.last_name }}</td>
                                <td>{{ employee.username }}</td>
                                <td>{{ employee.email }}</td>
                                <td>{{ employee.department_name }}</td>
                                <td>
                                    {% if employee.role == 'admin' %}
                                    مدير النظام
                                    {% elif employee.role == 'hr' %}
                                    موارد بشرية
                                    {% elif employee.role == 'manager' %}
                                    مدير قسم
                                    {% elif employee.role == 'gm' %}
                                    مدير عام
                                    {% else %}
                                    موظف
                                    {% endif %}
                                </td>
                                <td>
                                    {% if employee.is_active == 1 %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ employee.date_joined }}</td>
                                <td>
                                    {% for balance in leave_balances %}
                                        {% if balance.user_id == employee.id and balance.leave_type_name == 'إجازة اعتيادية' %}
                                            {{ balance.remaining_days }} يوم
                                        {% endif %}
                                    {% endfor %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <p>إجمالي النتائج: <strong>{{ employees|length }}</strong></p>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-1"></i>
                    لا توجد نتائج تطابق معايير البحث.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    function resetFilters() {
        document.getElementById('department_id').value = 'all';
        document.getElementById('role').value = 'all';
        document.getElementById('status').value = 'all';
    }
    
    function exportToPDF() {
        alert('سيتم تصدير التقرير إلى ملف PDF قريباً.');
        // This would typically use a library like jsPDF or call a server endpoint
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        // Department Chart
        const departmentCtx = document.getElementById('departmentChart').getContext('2d');
        const departmentChart = new Chart(departmentCtx, {
            type: 'pie',
            data: {
                labels: {{ department_labels|tojson }},
                datasets: [{
                    data: {{ department_data|tojson }},
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#dda20a',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            },
        });
        
        // Role Chart
        const roleCtx = document.getElementById('roleChart').getContext('2d');
        const roleChart = new Chart(roleCtx, {
            type: 'pie',
            data: {
                labels: {{ role_labels|tojson }},
                datasets: [{
                    data: {{ role_data|tojson }},
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e',
                        '#e74a3b'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#dda20a',
                        '#be2617'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            },
        });
    });
</script>
{% endblock %}
