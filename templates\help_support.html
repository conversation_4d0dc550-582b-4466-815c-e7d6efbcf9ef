{% extends "base.html" %}

{% block title %}ALEMIS - المساعدة والدعم{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">المساعدة والدعم</h2>
        
        <!-- قسم البحث -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-search me-1"></i>
                البحث في مركز المساعدة
            </div>
            <div class="card-body">
                <div class="input-group mb-3">
                    <input type="text" id="help-search" class="form-control" placeholder="ابحث عن سؤال أو موضوع...">
                    <button class="btn btn-primary" type="button" id="search-button">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
                <div id="search-results" class="mt-3" style="display: none;">
                    <h5>نتائج البحث:</h5>
                    <ul class="list-group" id="results-list">
                        <!-- ستتم إضافة النتائج هنا بواسطة JavaScript -->
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- الأسئلة الشائعة -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-question-circle me-1"></i>
                الأسئلة الشائعة
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <!-- سؤال 1 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading1">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse1" aria-expanded="false" aria-controls="faqCollapse1">
                                كيف يمكنني تقديم طلب إجازة؟
                            </button>
                        </h2>
                        <div id="faqCollapse1" class="accordion-collapse collapse" aria-labelledby="faqHeading1" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>لتقديم طلب إجازة، اتبع الخطوات التالية:</p>
                                <ol>
                                    <li>انتقل إلى قائمة "الإجازات" في الشريط العلوي</li>
                                    <li>اختر "طلب إجازة جديدة"</li>
                                    <li>حدد نوع الإجازة وتاريخ البداية والنهاية</li>
                                    <li>أدخل سبب الإجازة (اختياري)</li>
                                    <li>انقر على زر "تقديم الطلب"</li>
                                </ol>
                                <p>سيتم إرسال طلبك إلى المدير المباشر للموافقة عليه.</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- سؤال 2 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse2" aria-expanded="false" aria-controls="faqCollapse2">
                                كيف يمكنني تبديل الدوام مع زميل؟
                            </button>
                        </h2>
                        <div id="faqCollapse2" class="accordion-collapse collapse" aria-labelledby="faqHeading2" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>يمكنك تبديل الدوام مع زميل بطريقتين:</p>
                                <h6>1. التبديل المباشر (قبل اعتماد الجدول):</h6>
                                <ul>
                                    <li>انتقل إلى قائمة "جدول الدوام" ثم اختر "تبديل دوام مباشر"</li>
                                    <li>حدد نوع التبديل (يوم كامل أو شفت محدد)</li>
                                    <li>اختر الموظف الذي تريد التبديل معه</li>
                                    <li>حدد اليوم/الأيام والشفت/الشفتات</li>
                                    <li>أدخل سبب التبديل</li>
                                    <li>انقر على "إرسال طلب التبديل"</li>
                                </ul>
                                <h6>2. طلب تبديل رسمي (بعد اعتماد الجدول):</h6>
                                <ul>
                                    <li>انتقل إلى قائمة "جدول الدوام" ثم اختر "طلب تبديل دوام"</li>
                                    <li>أكمل النموذج بالمعلومات المطلوبة</li>
                                    <li>سيتم إرسال الطلب للموافقة عليه من قبل المدير</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- سؤال 3 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse3" aria-expanded="false" aria-controls="faqCollapse3">
                                كيف يمكنني معرفة رصيد إجازاتي المتبقي؟
                            </button>
                        </h2>
                        <div id="faqCollapse3" class="accordion-collapse collapse" aria-labelledby="faqHeading3" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>يمكنك معرفة رصيد إجازاتك المتبقي بعدة طرق:</p>
                                <ol>
                                    <li>في لوحة التحكم الرئيسية، ستجد بطاقة "رصيد الإجازات" تعرض رصيدك من الإجازة الاعتيادية</li>
                                    <li>في الرسم البياني "توزيع أرصدة الإجازات" ستجد تفاصيل جميع أنواع الإجازات المتاحة لك</li>
                                    <li>يمكنك أيضًا الاطلاع على تفاصيل أرصدة الإجازات من خلال صفحة "الملف الشخصي"</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <!-- سؤال 4 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse4" aria-expanded="false" aria-controls="faqCollapse4">
                                كيف يمكنني تغيير كلمة المرور الخاصة بي؟
                            </button>
                        </h2>
                        <div id="faqCollapse4" class="accordion-collapse collapse" aria-labelledby="faqHeading4" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>لتغيير كلمة المرور الخاصة بك، اتبع الخطوات التالية:</p>
                                <ol>
                                    <li>انقر على اسم المستخدم الخاص بك في الزاوية العلوية اليمنى</li>
                                    <li>اختر "الملف الشخصي" من القائمة المنسدلة</li>
                                    <li>انتقل إلى قسم "تغيير كلمة المرور"</li>
                                    <li>أدخل كلمة المرور الحالية وكلمة المرور الجديدة وتأكيدها</li>
                                    <li>انقر على زر "تحديث كلمة المرور"</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <!-- سؤال 5 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faqHeading5">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse5" aria-expanded="false" aria-controls="faqCollapse5">
                                كيف يمكنني تقديم طلب تغطية؟
                            </button>
                        </h2>
                        <div id="faqCollapse5" class="accordion-collapse collapse" aria-labelledby="faqHeading5" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>لتقديم طلب تغطية، اتبع الخطوات التالية:</p>
                                <ol>
                                    <li>انتقل إلى قائمة "طلبات أخرى" في الشريط العلوي</li>
                                    <li>اختر "طلب تغطية"</li>
                                    <li>حدد تاريخ التغطية والموظف الذي سيقوم بالتغطية</li>
                                    <li>أدخل سبب طلب التغطية</li>
                                    <li>انقر على زر "تقديم الطلب"</li>
                                </ol>
                                <p>سيتم إرسال الطلب إلى الموظف المحدد للموافقة عليه، ثم إلى المدير للاعتماد النهائي.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روبوت الدردشة -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-robot me-1"></i>
                مساعد ALEMIS الذكي
            </div>
            <div class="card-body">
                <div class="chat-container" id="chat-container">
                    <div class="chat-messages" id="chat-messages">
                        <div class="message bot">
                            <div class="message-content">
                                <p>مرحباً! أنا مساعد ALEMIS الذكي. كيف يمكنني مساعدتك اليوم؟</p>
                            </div>
                        </div>
                        <!-- ستتم إضافة الرسائل هنا بواسطة JavaScript -->
                    </div>
                    <div class="chat-input">
                        <input type="text" id="user-input" class="form-control" placeholder="اكتب سؤالك هنا...">
                        <button class="btn btn-primary" id="send-button">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اتصل بنا -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-headset me-1"></i>
                اتصل بفريق الدعم
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <form id="support-form">
                            <div class="mb-3">
                                <label for="support-subject" class="form-label">الموضوع</label>
                                <input type="text" class="form-control" id="support-subject" required>
                            </div>
                            <div class="mb-3">
                                <label for="support-category" class="form-label">الفئة</label>
                                <select class="form-select" id="support-category" required>
                                    <option value="">-- اختر الفئة --</option>
                                    <option value="technical">مشكلة تقنية</option>
                                    <option value="account">حساب المستخدم</option>
                                    <option value="leaves">الإجازات</option>
                                    <option value="shifts">جداول الدوام</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="support-message" class="form-label">الرسالة</label>
                                <textarea class="form-control" id="support-message" rows="5" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="support-attachment" class="form-label">إرفاق ملف (اختياري)</label>
                                <input type="file" class="form-control" id="support-attachment">
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>
                                إرسال
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <div class="contact-info">
                            <h5>معلومات الاتصال</h5>
                            <p><i class="fas fa-envelope me-2"></i> البريد الإلكتروني: <EMAIL></p>
                            <p><i class="fas fa-phone me-2"></i> الهاتف: +966 12 345 6789</p>
                            <p><i class="fas fa-clock me-2"></i> ساعات العمل: الأحد - الخميس، 8:00 ص - 4:00 م</p>
                            
                            <div class="alert alert-info mt-4">
                                <h6><i class="fas fa-info-circle me-2"></i> ملاحظة</h6>
                                <p>يتم الرد على جميع الاستفسارات خلال 24 ساعة عمل. للحالات العاجلة، يرجى الاتصال بالرقم المذكور أعلاه.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    /* تنسيقات روبوت الدردشة */
    .chat-container {
        display: flex;
        flex-direction: column;
        height: 400px;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        overflow: hidden;
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background-color: #f8fafc;
    }
    
    .chat-input {
        display: flex;
        padding: 0.75rem;
        background-color: #fff;
        border-top: 1px solid #e2e8f0;
    }
    
    .chat-input input {
        flex: 1;
        margin-left: 0.5rem;
    }
    
    .message {
        margin-bottom: 1rem;
        display: flex;
    }
    
    .message.user {
        justify-content: flex-end;
    }
    
    .message-content {
        max-width: 80%;
        padding: 0.75rem 1rem;
        border-radius: 1rem;
    }
    
    .message.bot .message-content {
        background-color: #e2e8f0;
        border-bottom-right-radius: 0.25rem;
    }
    
    .message.user .message-content {
        background-color: var(--primary-color);
        color: white;
        border-bottom-left-radius: 0.25rem;
    }
    
    .message-content p {
        margin-bottom: 0;
    }
    
    /* تنسيقات الأسئلة الشائعة */
    .accordion-button:not(.collapsed) {
        background-color: rgba(26, 76, 184, 0.1);
        color: var(--primary-color);
    }
    
    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(26, 76, 184, 0.25);
    }
    
    /* تنسيقات معلومات الاتصال */
    .contact-info {
        padding: 1rem;
        background-color: #f8fafc;
        border-radius: 0.5rem;
        height: 100%;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // روبوت الدردشة
        const chatMessages = document.getElementById('chat-messages');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        
        // قاعدة بيانات الأسئلة والأجوبة البسيطة
        const qaPairs = {
            'كيف أقدم طلب إجازة': 'لتقديم طلب إجازة، انتقل إلى قائمة "الإجازات" واختر "طلب إجازة جديدة". ثم املأ النموذج وانقر على "تقديم الطلب".',
            'ما هو رصيد إجازاتي': 'يمكنك معرفة رصيد إجازاتك من لوحة التحكم الرئيسية أو من خلال صفحة "الملف الشخصي".',
            'كيف أغير كلمة المرور': 'لتغيير كلمة المرور، انقر على اسم المستخدم في الزاوية العلوية اليمنى، ثم اختر "الملف الشخصي" وانتقل إلى قسم "تغيير كلمة المرور".',
            'كيف أبدل الدوام': 'يمكنك تبديل الدوام من خلال "تبديل دوام مباشر" قبل اعتماد الجدول، أو من خلال "طلب تبديل دوام" بعد الاعتماد.',
            'من هو المدير': 'يمكنك معرفة اسم مديرك من صفحة "الملف الشخصي" في قسم معلومات القسم.',
            'متى يتم اعتماد الجدول': 'يتم اعتماد جدول الدوام الشهري في نهاية كل شهر للشهر التالي.',
            'كيف أطلب تغطية': 'لطلب تغطية، انتقل إلى قائمة "طلبات أخرى" واختر "طلب تغطية"، ثم املأ النموذج وانقر على "تقديم الطلب".'
        };
        
        // إضافة رسالة جديدة إلى المحادثة
        function addMessage(text, isUser) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            const paragraph = document.createElement('p');
            paragraph.textContent = text;
            
            contentDiv.appendChild(paragraph);
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            
            // تمرير إلى أسفل لعرض الرسالة الجديدة
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // معالجة إرسال رسالة المستخدم
        function handleUserMessage() {
            const text = userInput.value.trim();
            if (text === '') return;
            
            // إضافة رسالة المستخدم
            addMessage(text, true);
            userInput.value = '';
            
            // البحث عن إجابة في قاعدة البيانات
            setTimeout(() => {
                let botResponse = 'عذراً، لا يمكنني الإجابة على هذا السؤال حالياً. يرجى التواصل مع فريق الدعم للمساعدة.';
                
                // البحث عن تطابق جزئي في الأسئلة
                for (const question in qaPairs) {
                    if (text.includes(question) || question.includes(text)) {
                        botResponse = qaPairs[question];
                        break;
                    }
                }
                
                // إضافة رد الروبوت
                addMessage(botResponse, false);
            }, 500);
        }
        
        // إضافة مستمعي الأحداث
        sendButton.addEventListener('click', handleUserMessage);
        userInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleUserMessage();
            }
        });
        
        // البحث في الأسئلة الشائعة
        const helpSearch = document.getElementById('help-search');
        const searchButton = document.getElementById('search-button');
        const searchResults = document.getElementById('search-results');
        const resultsList = document.getElementById('results-list');
        
        function performSearch() {
            const query = helpSearch.value.trim().toLowerCase();
            if (query === '') return;
            
            // جمع جميع الأسئلة من الأكورديون
            const questions = document.querySelectorAll('.accordion-button');
            const results = [];
            
            questions.forEach(question => {
                const text = question.textContent.trim();
                if (text.toLowerCase().includes(query)) {
                    results.push({
                        text: text,
                        target: question.getAttribute('data-bs-target')
                    });
                }
            });
            
            // عرض النتائج
            resultsList.innerHTML = '';
            if (results.length > 0) {
                results.forEach(result => {
                    const li = document.createElement('li');
                    li.className = 'list-group-item';
                    li.textContent = result.text;
                    li.style.cursor = 'pointer';
                    
                    li.addEventListener('click', function() {
                        const targetQuestion = document.querySelector(`[data-bs-target="${result.target}"]`);
                        if (targetQuestion) {
                            targetQuestion.click();
                            targetQuestion.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    });
                    
                    resultsList.appendChild(li);
                });
                searchResults.style.display = 'block';
            } else {
                const li = document.createElement('li');
                li.className = 'list-group-item text-center';
                li.textContent = 'لا توجد نتائج مطابقة للبحث';
                resultsList.appendChild(li);
                searchResults.style.display = 'block';
            }
        }
        
        searchButton.addEventListener('click', performSearch);
        helpSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // نموذج الدعم
        const supportForm = document.getElementById('support-form');
        
        supportForm.addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم إرسال طلب الدعم بنجاح. سيتم التواصل معك قريباً.');
            supportForm.reset();
        });
    });
</script>
{% endblock %}
