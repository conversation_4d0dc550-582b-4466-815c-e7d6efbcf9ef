-- Drop tables if they exist
DROP TABLE IF EXISTS audit_logs;
DROP TABLE IF EXISTS permission_requests;
DROP TABLE IF EXISTS coverage_requests;
DROP TABLE IF EXISTS leave_attachments;
DROP TABLE IF EXISTS leave_balances;
DROP TABLE IF EXISTS leave_requests;
DROP TABLE IF EXISTS leave_types;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS departments;
DROP TABLE IF EXISTS other_requests;
DROP TABLE IF EXISTS shift_swap_requests;
DROP TABLE IF EXISTS shift_schedules;
DROP TABLE IF EXISTS shift_assignments;
DROP TABLE IF EXISTS monthly_operations;
DROP TABLE IF EXISTS coverage_days;

-- Create departments table
CREATE TABLE departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT
);

-- Create users table
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    role TEXT NOT NULL,  -- admin, hr, manager, gm, employee
    department_id INTEGER,
    is_active INTEGER DEFAULT 1,
    date_joined TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    otp_secret TEXT,
    FOREIGN KEY (department_id) REFERENCES departments (id)
);

-- Create leave_types table
CREATE TABLE leave_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    default_days INTEGER DEFAULT 0
);

-- Create leave_requests table
CREATE TABLE leave_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    leave_type_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending',  -- pending, approved, rejected
    manager_approval INTEGER DEFAULT 0,
    hr_approval INTEGER DEFAULT 0,
    document_path TEXT,  -- مسار الملف المرفق
    coverage_day_ids TEXT,  -- معرفات أيام التغطية المستخدمة
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (leave_type_id) REFERENCES leave_types (id)
);

-- Create leave_attachments table
CREATE TABLE leave_attachments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    leave_request_id INTEGER NOT NULL,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (leave_request_id) REFERENCES leave_requests (id)
);

-- Create leave_balances table
CREATE TABLE leave_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    leave_type_id INTEGER NOT NULL,
    year INTEGER NOT NULL,
    total_days INTEGER DEFAULT 0,
    used_days INTEGER DEFAULT 0,
    remaining_days INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (leave_type_id) REFERENCES leave_types (id)
);

-- Create coverage_requests table
CREATE TABLE coverage_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    leave_request_id INTEGER NOT NULL,
    covering_user_id INTEGER NOT NULL,
    status TEXT DEFAULT 'pending',  -- pending, approved, rejected
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (leave_request_id) REFERENCES leave_requests (id),
    FOREIGN KEY (covering_user_id) REFERENCES users (id)
);

-- Create permission_requests table
CREATE TABLE permission_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    reason TEXT,
    status TEXT DEFAULT 'pending',  -- pending, approved, rejected
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Create audit_logs table
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    entity_type TEXT,
    entity_id INTEGER,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Create profile_change_requests table
CREATE TABLE profile_change_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    request_type TEXT NOT NULL, -- username, password, email, personal_info
    current_value TEXT,
    new_value TEXT NOT NULL,
    status TEXT DEFAULT 'pending', -- pending, approved, rejected
    manager_approval INTEGER DEFAULT 0,
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Insert default departments
INSERT INTO departments (name, description) VALUES
    ('قسم المختبر', 'قسم المختبر الرئيسي');

-- Create other_requests table
CREATE TABLE other_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    status TEXT DEFAULT 'pending',  -- pending, approved, rejected
    manager_approval INTEGER DEFAULT 0,
    hr_approval INTEGER DEFAULT 0,
    gm_approval INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Create shift_swap_requests table
CREATE TABLE shift_swap_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    swap_type TEXT NOT NULL,  -- with_employee, without_employee, use_coverage_day
    swap_reason_type TEXT NOT NULL, -- regular_shift, coverage_day
    swap_duration TEXT NOT NULL, -- one_day, multiple_days
    swap_with_user_id INTEGER,
    swap_date DATE,
    start_date DATE,
    end_date DATE,
    reason TEXT,
    status TEXT DEFAULT 'pending',  -- pending, approved, rejected
    manager_approval INTEGER DEFAULT 0,
    hr_approval INTEGER DEFAULT 0,
    shift_type TEXT, -- morning, evening, night (الشفت المطلوب)
    requester_shift_type TEXT, -- morning, evening, night (شفت الموظف الطالب)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (swap_with_user_id) REFERENCES users (id)
);

-- Create shift_schedules table
CREATE TABLE shift_schedules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    month INTEGER NOT NULL,
    year INTEGER NOT NULL,
    status TEXT DEFAULT 'draft',  -- draft, manager_approved, hr_approved, gm_approved
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- Create shift_assignments table
CREATE TABLE shift_assignments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    schedule_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    day INTEGER NOT NULL,
    shift_type TEXT NOT NULL,  -- morning, evening, night, split
    status TEXT DEFAULT 'assigned',  -- assigned, swapped
    FOREIGN KEY (schedule_id) REFERENCES shift_schedules (id),
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Create monthly_operations table
CREATE TABLE monthly_operations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    month INTEGER NOT NULL,
    year INTEGER NOT NULL,
    operation_date DATE NOT NULL,
    description TEXT NOT NULL,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- Create coverage_days table
CREATE TABLE coverage_days (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    coverage_date DATE NOT NULL,
    shift_type TEXT NOT NULL,  -- morning, evening, night
    status TEXT DEFAULT 'pending',  -- pending, approved, used
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Modify coverage_requests table to include more details
DROP TABLE IF EXISTS coverage_requests;
CREATE TABLE coverage_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    coverage_date DATE NOT NULL,
    coverage_type TEXT NOT NULL,
    coverage_reason TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'pending',  -- pending, approved, rejected
    manager_approval INTEGER DEFAULT 0,
    hr_approval INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Insert default leave types
INSERT INTO leave_types (name, description, default_days) VALUES
    ('إجازة اعتيادية', 'الإجازة السنوية المدفوعة', 30),
    ('إجازة مرضية', 'إجازة للظروف الصحية', 15),
    ('إجازة طارئة', 'إجازة للظروف الطارئة', 5),
    ('إجازة بدون راتب', 'إجازة غير مدفوعة', 0),
    ('إجازة أمومة', 'إجازة للأمهات الجدد', 70),
    ('إجازة حج', 'إجازة لأداء فريضة الحج', 15),
    ('إجازة تغطية', 'إجازة لتغطية موظف آخر', 0);

-- Insert default users (password: admin123)
INSERT INTO users (username, email, password_hash, first_name, last_name, role, department_id) VALUES
    ('admin', '<EMAIL>', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', 'مدير', 'النظام', 'admin', 1),
    ('manager', '<EMAIL>', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', 'مدير', 'المختبر', 'manager', 1),
    ('hr', '<EMAIL>', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', 'مسؤول', 'الموارد البشرية', 'hr', 1),
    ('gm', '<EMAIL>', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', 'المدير', 'العام', 'gm', 1),
    ('employee1', '<EMAIL>', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', 'موظف', 'الأول', 'employee', 1),
    ('employee2', '<EMAIL>', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', 'موظف', 'الثاني', 'employee', 1);
