{% extends "base.html" %}

{% block title %}ALEMIS - تعديل مستخدم{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">تعديل مستخدم</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-user-edit me-1"></i>
                نموذج تعديل مستخدم: {{ user_to_edit.first_name }} {{ user_to_edit.last_name }}
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('edit_user', user_id=user_to_edit.id) }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" value="{{ user_to_edit.username }}" readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ user_to_edit.email }}" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="first_name" class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user_to_edit.first_name }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="last_name" class="form-label">الاسم الأخير</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user_to_edit.last_name }}" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">كلمة المرور (اتركها فارغة للاحتفاظ بكلمة المرور الحالية)</label>
                            <input type="password" class="form-control" id="password" name="password">
                        </div>
                        <div class="col-md-6">
                            <label for="role" class="form-label">الدور</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="employee" {% if user_to_edit.role == 'employee' %}selected{% endif %}>موظف</option>
                                <option value="manager" {% if user_to_edit.role == 'manager' %}selected{% endif %}>مدير قسم</option>
                                <option value="hr" {% if user_to_edit.role == 'hr' %}selected{% endif %}>موارد بشرية</option>
                                <option value="gm" {% if user_to_edit.role == 'gm' %}selected{% endif %}>مدير عام</option>
                                {% if user.role == 'admin' %}
                                <option value="admin" {% if user_to_edit.role == 'admin' %}selected{% endif %}>مدير النظام</option>
                                {% endif %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id" required>
                                {% for department in departments %}
                                <option value="{{ department.id }}" {% if user_to_edit.department_id == department.id %}selected{% endif %}>{{ department.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if user_to_edit.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ التغييرات
                            </button>
                            <a href="{{ url_for('users') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
