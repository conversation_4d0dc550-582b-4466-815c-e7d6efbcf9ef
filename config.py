"""
تكوين التطبيق المحسن
Enhanced Application Configuration
"""
import os
import secrets
from datetime import timedelta
from typing import Dict, Any
from pydantic_settings import BaseSettings
from pydantic import Field, validator


class Config(BaseSettings):
    """إعدادات التطبيق الأساسية"""
    
    # Application Settings
    SECRET_KEY: str = Field(default_factory=lambda: secrets.token_hex(32))
    DEBUG: bool = Field(default=False)
    TESTING: bool = Field(default=False)
    
    # Database Configuration
    DATABASE_URL: str = Field(default="sqlite:///alemis.db")
    DATABASE_POOL_SIZE: int = Field(default=10)
    DATABASE_POOL_TIMEOUT: int = Field(default=20)
    DATABASE_POOL_RECYCLE: int = Field(default=3600)
    
    # Security Settings
    SECURITY_PASSWORD_SALT: str = Field(default_factory=lambda: secrets.token_hex(16))
    SECURITY_REGISTERABLE: bool = Field(default=False)
    SECURITY_RECOVERABLE: bool = Field(default=True)
    SECURITY_TRACKABLE: bool = Field(default=True)
    SECURITY_CHANGEABLE: bool = Field(default=True)
    SECURITY_CONFIRMABLE: bool = Field(default=False)
    
    # JWT Settings
    JWT_SECRET_KEY: str = Field(default_factory=lambda: secrets.token_hex(32))
    JWT_ACCESS_TOKEN_EXPIRES: timedelta = Field(default=timedelta(hours=1))
    JWT_REFRESH_TOKEN_EXPIRES: timedelta = Field(default=timedelta(days=30))
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME: timedelta = Field(default=timedelta(hours=2))
    SESSION_COOKIE_SECURE: bool = Field(default=True)
    SESSION_COOKIE_HTTPONLY: bool = Field(default=True)
    SESSION_COOKIE_SAMESITE: str = Field(default='Lax')
    
    # File Upload Settings
    UPLOAD_FOLDER: str = Field(default='uploads')
    MAX_CONTENT_LENGTH: int = Field(default=16 * 1024 * 1024)  # 16MB
    ALLOWED_EXTENSIONS: set = Field(default={'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'txt'})
    
    # Email Configuration
    MAIL_SERVER: str = Field(default='localhost')
    MAIL_PORT: int = Field(default=587)
    MAIL_USE_TLS: bool = Field(default=True)
    MAIL_USE_SSL: bool = Field(default=False)
    MAIL_USERNAME: str = Field(default='')
    MAIL_PASSWORD: str = Field(default='')
    MAIL_DEFAULT_SENDER: str = Field(default='<EMAIL>')
    
    # Redis Configuration
    REDIS_URL: str = Field(default='redis://localhost:6379/0')
    CACHE_TYPE: str = Field(default='redis')
    CACHE_REDIS_URL: str = Field(default='redis://localhost:6379/1')
    CACHE_DEFAULT_TIMEOUT: int = Field(default=300)
    
    # Rate Limiting
    RATELIMIT_STORAGE_URL: str = Field(default='redis://localhost:6379/2')
    RATELIMIT_DEFAULT: str = Field(default='100 per hour')
    
    # Celery Configuration
    CELERY_BROKER_URL: str = Field(default='redis://localhost:6379/3')
    CELERY_RESULT_BACKEND: str = Field(default='redis://localhost:6379/4')
    
    # Logging Configuration
    LOG_LEVEL: str = Field(default='INFO')
    LOG_FILE: str = Field(default='logs/alemis.log')
    LOG_MAX_BYTES: int = Field(default=10485760)  # 10MB
    LOG_BACKUP_COUNT: int = Field(default=5)
    
    # Monitoring & Analytics
    SENTRY_DSN: str = Field(default='')
    ANALYTICS_ENABLED: bool = Field(default=True)
    
    # API Configuration
    API_TITLE: str = Field(default='ALEMIS API')
    API_VERSION: str = Field(default='v1')
    API_DESCRIPTION: str = Field(default='نظام إدارة إجازات الموظفين في المختبرات')
    
    # Internationalization
    LANGUAGES: Dict[str, str] = Field(default={
        'ar': 'العربية',
        'en': 'English'
    })
    BABEL_DEFAULT_LOCALE: str = Field(default='ar')
    BABEL_DEFAULT_TIMEZONE: str = Field(default='Asia/Riyadh')
    
    # Security Headers
    FORCE_HTTPS: bool = Field(default=True)
    CONTENT_SECURITY_POLICY: Dict[str, Any] = Field(default={
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
        'style-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
        'img-src': "'self' data: https:",
        'font-src': "'self' https://cdnjs.cloudflare.com",
        'connect-src': "'self'",
        'frame-ancestors': "'none'"
    })
    
    # Application Features
    ENABLE_2FA: bool = Field(default=True)
    ENABLE_EMAIL_NOTIFICATIONS: bool = Field(default=True)
    ENABLE_SMS_NOTIFICATIONS: bool = Field(default=False)
    ENABLE_PUSH_NOTIFICATIONS: bool = Field(default=True)
    ENABLE_AUDIT_LOG: bool = Field(default=True)
    ENABLE_ANALYTICS: bool = Field(default=True)
    
    # Business Rules
    DEFAULT_ANNUAL_LEAVE_DAYS: int = Field(default=30)
    DEFAULT_SICK_LEAVE_DAYS: int = Field(default=15)
    MAX_CONSECUTIVE_LEAVE_DAYS: int = Field(default=14)
    MIN_ADVANCE_NOTICE_DAYS: int = Field(default=3)
    
    class Config:
        env_file = '.env'
        env_file_encoding = 'utf-8'
        case_sensitive = True


class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG: bool = Field(default=True)
    TESTING: bool = Field(default=False)
    SESSION_COOKIE_SECURE: bool = Field(default=False)
    FORCE_HTTPS: bool = Field(default=False)


class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    DEBUG: bool = Field(default=True)
    TESTING: bool = Field(default=True)
    DATABASE_URL: str = Field(default="sqlite:///:memory:")
    WTF_CSRF_ENABLED: bool = Field(default=False)
    SESSION_COOKIE_SECURE: bool = Field(default=False)
    FORCE_HTTPS: bool = Field(default=False)


class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG: bool = Field(default=False)
    TESTING: bool = Field(default=False)
    SESSION_COOKIE_SECURE: bool = Field(default=True)
    FORCE_HTTPS: bool = Field(default=True)


# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}


def get_config(config_name: str = None) -> Config:
    """الحصول على إعدادات التطبيق"""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')
    
    config_class = config_map.get(config_name, DevelopmentConfig)
    return config_class()
