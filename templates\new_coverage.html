{% extends "base.html" %}

{% block title %}ALEMIS - طلب تغطية جديد{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <h2 class="mb-4">طلب تغطية جديد</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-exchange-alt me-1"></i>
                نموذج طلب تغطية
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('new_coverage') }}">
                    <div class="mb-3">
                        <label for="coverage_date" class="form-label">تاريخ التغطية</label>
                        <input type="date" class="form-control" id="coverage_date" name="coverage_date" required>
                    </div>

                    <div class="mb-3">
                        <label for="coverage_type" class="form-label">فترة الدوام</label>
                        <select class="form-select" id="coverage_type" name="coverage_type" required>
                            <option value="">-- اختر فترة الدوام --</option>
                            <option value="morning">دوام صباحي (8ص-4م)</option>
                            <option value="evening">دوام مسائي (4م-12ل)</option>
                            <option value="night">دوام ليلي (12ل-8ص)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="coverage_reason" class="form-label">نوع التغطية</label>
                        <input type="text" class="form-control" id="coverage_reason" name="coverage_reason" placeholder="أدخل نوع التغطية" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف التغطية</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i>
                            إرسال الطلب
                        </button>
                        <a href="{{ url_for('coverage_requests') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة إلى قائمة الطلبات
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // التحقق من التاريخ
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const coverageDate = new Date(document.getElementById('coverage_date').value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (coverageDate < today) {
                e.preventDefault();
                alert('لا يمكن اختيار تاريخ في الماضي');
                return;
            }
        });
    });
</script>
{% endblock %}
