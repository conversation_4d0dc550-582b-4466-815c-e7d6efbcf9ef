{% extends "base.html" %}

{% block title %}ALEMIS - تقرير الإجازات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">تقرير الإجازات</h2>
        
        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-1"></i>
                تصفية النتائج
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('leave_report') }}" class="report-filter">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="all" {% if filters.department_id == 'all' %}selected{% endif %}>جميع الأقسام</option>
                                {% for department in departments %}
                                <option value="{{ department.id }}" {% if filters.department_id|string == department.id|string %}selected{% endif %}>{{ department.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="leave_type_id" class="form-label">نوع الإجازة</label>
                            <select class="form-select" id="leave_type_id" name="leave_type_id">
                                <option value="all" {% if filters.leave_type_id == 'all' %}selected{% endif %}>جميع الأنواع</option>
                                {% for leave_type in leave_types %}
                                <option value="{{ leave_type.id }}" {% if filters.leave_type_id|string == leave_type.id|string %}selected{% endif %}>{{ leave_type.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" {% if filters.status == 'all' %}selected{% endif %}>جميع الحالات</option>
                                <option value="pending" {% if filters.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                <option value="approved" {% if filters.status == 'approved' %}selected{% endif %}>تمت الموافقة</option>
                                <option value="rejected" {% if filters.status == 'rejected' %}selected{% endif %}>مرفوض</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ filters.start_date }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ filters.end_date }}">
                        </div>
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-redo me-1"></i>
                                إعادة تعيين
                            </button>
                        </div>
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="button" class="btn btn-success w-100" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-1"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Results -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-table me-1"></i>
                نتائج البحث
            </div>
            <div class="card-body">
                {% if leave_requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="leaveReportTable">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>القسم</th>
                                <th>نوع الإجازة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>المدة</th>
                                <th>السبب</th>
                                <th>الحالة</th>
                                <th>موافقة المدير</th>
                                <th>موافقة الموارد البشرية</th>
                                <th>تاريخ الطلب</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave in leave_requests %}
                            <tr>
                                <td>{{ leave.first_name }} {{ leave.last_name }}</td>
                                <td>{{ leave.department_name }}</td>
                                <td>{{ leave.leave_type_name }}</td>
                                <td>{{ leave.start_date }}</td>
                                <td>{{ leave.end_date }}</td>
                                <td>
                                    {% set start_date = leave.start_date.split('-') %}
                                    {% set end_date = leave.end_date.split('-') %}
                                    {% set start = [start_date[0]|int, start_date[1]|int, start_date[2]|int] %}
                                    {% set end = [end_date[0]|int, end_date[1]|int, end_date[2]|int] %}
                                    {% set days = (end[0] - start[0]) * 365 + (end[1] - start[1]) * 30 + (end[2] - start[2]) + 1 %}
                                    {{ days }} يوم
                                </td>
                                <td>{{ leave.reason }}</td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif leave.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif leave.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if leave.manager_approval == 1 %}
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-clock"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if leave.hr_approval == 1 %}
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-clock"></i></span>
                                    {% endif %}
                                </td>
                                <td>{{ leave.created_at }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <p>إجمالي النتائج: <strong>{{ leave_requests|length }}</strong></p>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-1"></i>
                    لا توجد نتائج تطابق معايير البحث.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function resetFilters() {
        document.getElementById('department_id').value = 'all';
        document.getElementById('leave_type_id').value = 'all';
        document.getElementById('status').value = 'all';
        document.getElementById('start_date').value = '';
        document.getElementById('end_date').value = '';
    }
    
    function exportToPDF() {
        alert('سيتم تصدير التقرير إلى ملف PDF قريباً.');
        // This would typically use a library like jsPDF or call a server endpoint
    }
</script>
{% endblock %}
