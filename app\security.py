"""
نظام الأمان المحسن
Enhanced Security System
"""
import hashlib
import secrets
import pyotp
import jwt
import bcrypt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple
from functools import wraps
from flask import request, jsonify, session, current_app, g
from cryptography.fernet import Fernet
import re


class SecurityManager:
    """مدير الأمان الرئيسي"""
    
    def __init__(self, app=None):
        self.app = app
        self._cipher_suite = None
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة نظام الأمان"""
        self.app = app
        
        # إنشاء مفتاح التشفير
        encryption_key = app.config.get('ENCRYPTION_KEY')
        if not encryption_key:
            encryption_key = Fernet.generate_key()
            app.config['ENCRYPTION_KEY'] = encryption_key
        
        self._cipher_suite = Fernet(encryption_key)
        
        # تسجيل المعالجات
        app.before_request(self._before_request)
        app.after_request(self._after_request)
    
    def _before_request(self):
        """معالج ما قبل الطلب"""
        # تسجيل محاولات الوصول
        self._log_access_attempt()
        
        # فحص Rate Limiting
        if not self._check_rate_limit():
            return jsonify({'error': 'تم تجاوز الحد المسموح من الطلبات'}), 429
    
    def _after_request(self, response):
        """معالج ما بعد الطلب"""
        # إضافة رؤوس الأمان
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        return response
    
    def _log_access_attempt(self):
        """تسجيل محاولات الوصول"""
        # سيتم تنفيذها لاحقاً مع نظام التسجيل
        pass
    
    def _check_rate_limit(self) -> bool:
        """فحص حدود المعدل"""
        # سيتم تنفيذها لاحقاً مع Redis
        return True


class PasswordManager:
    """مدير كلمات المرور"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """تشفير كلمة المرور"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """التحقق من كلمة المرور"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    @staticmethod
    def generate_secure_password(length: int = 12) -> str:
        """إنتاج كلمة مرور آمنة"""
        import string
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    @staticmethod
    def validate_password_strength(password: str) -> Tuple[bool, str]:
        """فحص قوة كلمة المرور"""
        if len(password) < 8:
            return False, "كلمة المرور يجب أن تكون 8 أحرف على الأقل"
        
        if not re.search(r"[A-Z]", password):
            return False, "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل"
        
        if not re.search(r"[a-z]", password):
            return False, "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل"
        
        if not re.search(r"\d", password):
            return False, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"
        
        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
            return False, "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل"
        
        return True, "كلمة المرور قوية"


class TwoFactorAuth:
    """نظام المصادقة الثنائية"""
    
    @staticmethod
    def generate_secret() -> str:
        """إنتاج مفتاح سري للمصادقة الثنائية"""
        return pyotp.random_base32()
    
    @staticmethod
    def generate_qr_url(secret: str, user_email: str, issuer: str = "ALEMIS") -> str:
        """إنتاج رابط QR Code"""
        totp = pyotp.TOTP(secret)
        return totp.provisioning_uri(
            name=user_email,
            issuer_name=issuer
        )
    
    @staticmethod
    def verify_token(secret: str, token: str) -> bool:
        """التحقق من رمز المصادقة الثنائية"""
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)
    
    @staticmethod
    def generate_backup_codes(count: int = 10) -> list:
        """إنتاج رموز احتياطية"""
        return [secrets.token_hex(4).upper() for _ in range(count)]


class JWTManager:
    """مدير JWT Tokens"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    def generate_access_token(self, user_id: int, role: str, expires_in: int = 3600) -> str:
        """إنتاج رمز الوصول"""
        payload = {
            'user_id': user_id,
            'role': role,
            'exp': datetime.utcnow() + timedelta(seconds=expires_in),
            'iat': datetime.utcnow(),
            'type': 'access'
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def generate_refresh_token(self, user_id: int, expires_in: int = 2592000) -> str:
        """إنتاج رمز التحديث"""
        payload = {
            'user_id': user_id,
            'exp': datetime.utcnow() + timedelta(seconds=expires_in),
            'iat': datetime.utcnow(),
            'type': 'refresh'
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """التحقق من الرمز"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None


class DataEncryption:
    """تشفير البيانات الحساسة"""
    
    def __init__(self, cipher_suite):
        self.cipher_suite = cipher_suite
    
    def encrypt(self, data: str) -> str:
        """تشفير البيانات"""
        if not data:
            return data
        return self.cipher_suite.encrypt(data.encode()).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        if not encrypted_data:
            return encrypted_data
        return self.cipher_suite.decrypt(encrypted_data.encode()).decode()


class InputValidator:
    """مدقق المدخلات"""
    
    @staticmethod
    def sanitize_input(data: str) -> str:
        """تنظيف المدخلات"""
        if not data:
            return data
        
        # إزالة الأحرف الخطيرة
        dangerous_chars = ['<', '>', '"', "'", '&', 'script', 'javascript:', 'vbscript:']
        for char in dangerous_chars:
            data = data.replace(char, '')
        
        return data.strip()
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """التحقق من صحة البريد الإلكتروني"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """التحقق من صحة رقم الهاتف"""
        pattern = r'^(\+966|0)?[5][0-9]{8}$'
        return re.match(pattern, phone) is not None


def require_auth(f):
    """مُزخرف للتحقق من المصادقة"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'مطلوب تسجيل الدخول'}), 401
        return f(*args, **kwargs)
    return decorated_function


def require_role(roles):
    """مُزخرف للتحقق من الدور"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'role' not in session or session['role'] not in roles:
                return jsonify({'error': 'ليس لديك صلاحية للوصول'}), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def require_2fa(f):
    """مُزخرف للتحقق من المصادقة الثنائية"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('2fa_verified', False):
            return jsonify({'error': 'مطلوب التحقق من المصادقة الثنائية'}), 401
        return f(*args, **kwargs)
    return decorated_function


# إنشاء مثيل مدير الأمان
security_manager = SecurityManager()
password_manager = PasswordManager()
two_factor_auth = TwoFactorAuth()
input_validator = InputValidator()
