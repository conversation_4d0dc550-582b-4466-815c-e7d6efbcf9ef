#!/usr/bin/env python
"""
تشغيل سريع لنظام ALEMIS
Quick Start Script for ALEMIS System
"""
import os
import sys
import subprocess
import platform
from pathlib import Path


def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║     🚀 ALEMIS - تشغيل سريع                                   ║
    ║                                                              ║
    ║     Enhanced Laboratory Employee Leave Management System     ║
    ║     Quick Start Setup                                        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_python_version():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    
    if sys.version_info < (3, 9):
        print("❌ يتطلب Python 3.9 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - مدعوم")
    return True


def check_system_requirements():
    """فحص متطلبات النظام"""
    print("\n🔍 فحص متطلبات النظام...")
    
    system = platform.system()
    print(f"📱 نظام التشغيل: {system}")
    
    # فحص Git
    try:
        subprocess.run(['git', '--version'], capture_output=True, check=True)
        print("✅ Git متاح")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ Git غير متاح - مطلوب للتطوير")
    
    # فحص Docker
    try:
        subprocess.run(['docker', '--version'], capture_output=True, check=True)
        print("✅ Docker متاح")
        docker_available = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ Docker غير متاح - سيتم التثبيت اليدوي")
        docker_available = False
    
    # فحص Docker Compose
    if docker_available:
        try:
            subprocess.run(['docker-compose', '--version'], capture_output=True, check=True)
            print("✅ Docker Compose متاح")
        except (subprocess.CalledProcessError, FileNotFoundError):
            try:
                subprocess.run(['docker', 'compose', 'version'], capture_output=True, check=True)
                print("✅ Docker Compose (v2) متاح")
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("⚠️ Docker Compose غير متاح")
                docker_available = False
    
    return docker_available


def create_env_file():
    """إنشاء ملف البيئة"""
    print("\n📝 إنشاء ملف البيئة...")
    
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ ملف .env موجود بالفعل")
        return
    
    if env_example.exists():
        # نسخ من المثال
        with open(env_example, 'r', encoding='utf-8') as src:
            content = src.read()
        
        with open(env_file, 'w', encoding='utf-8') as dst:
            dst.write(content)
        
        print("✅ تم إنشاء ملف .env من المثال")
    else:
        # إنشاء ملف أساسي
        basic_env = """# إعدادات التطبيق الأساسية
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=dev-secret-key-change-in-production

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///alemis.db

# إعدادات البريد الإلكتروني (اختياري)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# إعدادات Redis (اختياري)
REDIS_URL=redis://localhost:6379/0
"""
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(basic_env)
        
        print("✅ تم إنشاء ملف .env أساسي")


def setup_virtual_environment():
    """إعداد البيئة الافتراضية"""
    print("\n🔧 إعداد البيئة الافتراضية...")
    
    venv_path = Path('venv')
    
    if venv_path.exists():
        print("✅ البيئة الافتراضية موجودة بالفعل")
        return True
    
    try:
        # إنشاء البيئة الافتراضية
        subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
        print("✅ تم إنشاء البيئة الافتراضية")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إنشاء البيئة الافتراضية: {e}")
        return False


def install_dependencies():
    """تثبيت التبعيات"""
    print("\n📦 تثبيت التبعيات...")
    
    # تحديد مسار Python في البيئة الافتراضية
    system = platform.system()
    if system == "Windows":
        python_path = Path('venv/Scripts/python.exe')
        pip_path = Path('venv/Scripts/pip.exe')
    else:
        python_path = Path('venv/bin/python')
        pip_path = Path('venv/bin/pip')
    
    if not python_path.exists():
        print("❌ البيئة الافتراضية غير موجودة")
        return False
    
    try:
        # تحديث pip
        subprocess.run([str(pip_path), 'install', '--upgrade', 'pip'], check=True)
        print("✅ تم تحديث pip")
        
        # تثبيت التبعيات
        subprocess.run([str(pip_path), 'install', '-r', 'requirements.txt'], check=True)
        print("✅ تم تثبيت التبعيات")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت التبعيات: {e}")
        return False


def initialize_database():
    """تهيئة قاعدة البيانات"""
    print("\n🗄️ تهيئة قاعدة البيانات...")
    
    # تحديد مسار Python في البيئة الافتراضية
    system = platform.system()
    if system == "Windows":
        python_path = Path('venv/Scripts/python.exe')
    else:
        python_path = Path('venv/bin/python')
    
    try:
        # تشغيل أمر تهيئة قاعدة البيانات
        subprocess.run([str(python_path), 'manage.py', 'init_db'], check=True)
        print("✅ تم تهيئة قاعدة البيانات")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تهيئة قاعدة البيانات: {e}")
        return False


def docker_setup():
    """إعداد Docker"""
    print("\n🐳 إعداد Docker...")
    
    try:
        # إنشاء ملف .env إذا لم يكن موجوداً
        create_env_file()
        
        # بناء وتشغيل الحاويات
        subprocess.run(['docker-compose', 'up', '-d', '--build'], check=True)
        print("✅ تم تشغيل Docker بنجاح")
        
        print("\n🎉 النظام جاهز!")
        print("📱 التطبيق متاح على: http://localhost")
        print("📚 توثيق API: http://localhost/api/v1/docs")
        print("📊 Grafana: http://localhost:3000")
        print("🔍 Prometheus: http://localhost:9090")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إعداد Docker: {e}")
        return False


def manual_setup():
    """الإعداد اليدوي"""
    print("\n🔧 الإعداد اليدوي...")
    
    # إنشاء ملف البيئة
    create_env_file()
    
    # إعداد البيئة الافتراضية
    if not setup_virtual_environment():
        return False
    
    # تثبيت التبعيات
    if not install_dependencies():
        return False
    
    # تهيئة قاعدة البيانات
    if not initialize_database():
        return False
    
    print("\n🎉 النظام جاهز!")
    print("🚀 لتشغيل النظام:")
    
    system = platform.system()
    if system == "Windows":
        print("   venv\\Scripts\\activate")
        print("   python run_server.py")
    else:
        print("   source venv/bin/activate")
        print("   python run_server.py")
    
    print("\n📱 التطبيق سيكون متاحاً على: http://localhost:5000")
    
    return True


def print_next_steps():
    """طباعة الخطوات التالية"""
    print("\n" + "="*60)
    print("📋 الخطوات التالية:")
    print("="*60)
    
    print("1. 👥 الحسابات الافتراضية:")
    print("   🔑 المدير: admin / admin123")
    print("   👤 الموارد البشرية: hr / admin123")
    print("   🏢 المدير العام: gm / admin123")
    print("   🧪 مدير المختبر: manager / admin123")
    
    print("\n2. ⚙️ تخصيص الإعدادات:")
    print("   📝 عدل ملف .env لتخصيص الإعدادات")
    print("   📧 أضف إعدادات البريد الإلكتروني للإشعارات")
    
    print("\n3. 📚 الموارد المفيدة:")
    print("   📖 README.md - دليل شامل")
    print("   🤝 CONTRIBUTING.md - دليل المساهمة")
    print("   📝 CHANGELOG.md - سجل التغييرات")
    
    print("\n4. 🆘 الحصول على المساعدة:")
    print("   🐛 أبلغ عن الأخطاء في GitHub Issues")
    print("   💡 اقترح ميزات جديدة")
    print("   📧 تواصل معنا: <EMAIL>")


def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # فحص إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # فحص متطلبات النظام
    docker_available = check_system_requirements()
    
    print("\n" + "="*60)
    print("🚀 خيارات الإعداد:")
    print("="*60)
    print("1. 🐳 إعداد Docker (مُوصى به)")
    print("2. 🔧 إعداد يدوي")
    print("3. ❌ إلغاء")
    
    while True:
        choice = input("\nاختر خيار الإعداد (1-3): ").strip()
        
        if choice == '1':
            if docker_available:
                if docker_setup():
                    print_next_steps()
                    break
                else:
                    print("فشل في إعداد Docker. جرب الإعداد اليدوي.")
                    continue
            else:
                print("Docker غير متاح. جرب الإعداد اليدوي.")
                continue
        
        elif choice == '2':
            if manual_setup():
                print_next_steps()
                break
            else:
                print("فشل في الإعداد اليدوي.")
                break
        
        elif choice == '3':
            print("تم إلغاء الإعداد.")
            break
        
        else:
            print("خيار غير صحيح. اختر 1، 2، أو 3.")


if __name__ == '__main__':
    main()
