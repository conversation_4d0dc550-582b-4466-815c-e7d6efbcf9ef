{% extends "base.html" %}

{% block title %}ALEMIS - طلبات تبديل الدوام{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4 title-with-red-line">طلبات تبديل الدوام</h2>
        <div class="red-line-animated"></div>
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-sync-alt me-1"></i>
                    قائمة طلبات تبديل الدوام
                </div>
                <a href="{{ url_for('new_shift_swap') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    طلب تبديل جديد
                </a>
            </div>
            <div class="card-body">
                {% if swap_requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                                <th>الموظف</th>
                                {% endif %}
                                <th>نوع التبديل</th>
                                <th>التبديل مع</th>
                                <th>التاريخ الأصلي</th>
                                <th>التاريخ الجديد</th>
                                <th>نوع الشفت</th>
                                <th>السبب</th>
                                <th>الحالة</th>
                                <th>موافقة المدير</th>
                                <th>موافقة الموارد البشرية</th>
                                <th>تاريخ الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for swap in swap_requests %}
                            <tr>
                                {% if session.role in ['admin', 'hr', 'manager', 'gm'] %}
                                <td>{{ swap.first_name }} {{ swap.last_name }}</td>
                                {% endif %}
                                <td>
                                    {% if swap.swap_type == 'with_employee' %}
                                    تبديل مع موظف
                                    {% elif swap.swap_type == 'without_employee' %}
                                    تبديل بدون موظف
                                    {% elif swap.swap_type == 'use_coverage_day' %}
                                    استغناء يوم تغطية
                                    {% endif %}
                                </td>
                                <td>
                                    {% if swap.swap_type == 'with_employee' %}
                                    {{ swap.swap_with_first_name }} {{ swap.swap_with_last_name }}
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>{{ swap.original_date }}</td>
                                <td>{{ swap.new_date }}</td>
                                <td>
                                    {% if swap.shift_type == 'morning' %}
                                    <span class="badge bg-primary">دوام صباحي (8ص-4م)</span>
                                    {% elif swap.shift_type == 'evening' %}
                                    <span class="badge bg-info">دوام مسائي (4م-12ل)</span>
                                    {% elif swap.shift_type == 'night' %}
                                    <span class="badge bg-dark">دوام ليلي (12ل-8ص)</span>
                                    {% elif swap.swap_type == 'use_coverage_day' %}
                                    <span class="badge bg-success">يوم تغطية</span>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>{{ swap.reason }}</td>
                                <td>
                                    {% if swap.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif swap.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif swap.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if swap.manager_approval == 1 %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif swap.manager_approval == -1 %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% else %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if swap.hr_approval == 1 %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif swap.hr_approval == -1 %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% else %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% endif %}
                                </td>
                                <td>{{ swap.created_at }}</td>
                                <td>
                                    {% if session.role == 'manager' and swap.manager_approval == 0 %}
                                    <button type="button" class="btn btn-sm btn-success approve-swap" data-id="{{ swap.id }}" data-type="manager" data-action="approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-swap" data-id="{{ swap.id }}" data-type="manager" data-action="reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}

                                    {% if session.role == 'hr' and swap.manager_approval == 1 and swap.hr_approval == 0 %}
                                    <button type="button" class="btn btn-sm btn-success approve-swap" data-id="{{ swap.id }}" data-type="hr" data-action="approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger reject-swap" data-id="{{ swap.id }}" data-type="hr" data-action="reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}

                                    {% if swap.user_id == session.user_id and swap.status == 'pending' %}
                                    <button type="button" class="btn btn-sm btn-danger cancel-swap" data-id="{{ swap.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    لا توجد طلبات تبديل دوام حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الموافقة على طلب التبديل
        const approveButtons = document.querySelectorAll('.approve-swap');
        approveButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');

                if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
                    fetch('{{ url_for("approve_shift_swap") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            swap_id: id,
                            approval_type: type,
                            action: 'approve'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });

        // رفض طلب التبديل
        const rejectButtons = document.querySelectorAll('.reject-swap');
        rejectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const type = this.getAttribute('data-type');

                if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
                    fetch('{{ url_for("approve_shift_swap") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            swap_id: id,
                            approval_type: type,
                            action: 'reject'
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });

        // إلغاء طلب التبديل
        const cancelButtons = document.querySelectorAll('.cancel-swap');
        cancelButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');

                if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
                    fetch('{{ url_for("cancel_shift_swap") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            swap_id: id
                        }),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('حدث خطأ: ' + data.error);
                        }
                    });
                }
            });
        });
    });
</script>
{% endblock %}
