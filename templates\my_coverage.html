{% extends "base.html" %}

{% block title %}ALEMIS - أيام التغطية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4 title-with-red-line">أيام التغطية</h2>
        <div class="red-line-animated"></div>
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom">
                <i class="fas fa-exchange-alt me-1"></i>
                قائمة أيام التغطية
            </div>
            <div class="card-body">
                {% if coverage_days %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>تاريخ التغطية</th>
                                <th>نوع الشفت</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for coverage in coverage_days %}
                            <tr>
                                <td>{{ coverage.coverage_date }}</td>
                                <td>
                                    {% if coverage.shift_type == 'morning' %}
                                    دوام صباحي (8ص-4م)
                                    {% elif coverage.shift_type == 'evening' %}
                                    دوام مسائي (4م-12ل)
                                    {% elif coverage.shift_type == 'night' %}
                                    دوام ليلي (12ل-8ص)
                                    {% endif %}
                                </td>
                                <td>
                                    {% if coverage.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif coverage.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif coverage.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% elif coverage.status == 'used' %}
                                    <span class="badge bg-secondary">تم استخدامه</span>
                                    {% endif %}
                                </td>
                                <td>{{ coverage.created_at }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-1"></i>
                    لا توجد أيام تغطية حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
