{% extends "base.html" %}

{% block title %}ALEMIS - الأذونات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">الأذونات</h2>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-clock me-1"></i>
                    قائمة طلبات الأذونات
                </div>
                <a href="{{ url_for('new_permission') }}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    طلب إذن جديد
                </a>
            </div>
            <div class="card-body">
                {% if permission_requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>وقت البداية</th>
                                <th>وقت النهاية</th>
                                <th>المدة</th>
                                <th>السبب</th>
                                <th>الحالة</th>
                                <th>تاريخ الطلب</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for permission in permission_requests %}
                            <tr>
                                <td>{{ permission.date }}</td>
                                <td>{{ permission.start_time }}</td>
                                <td>{{ permission.end_time }}</td>
                                <td>
                                    {% set start_time = permission.start_time.split(':') %}
                                    {% set end_time = permission.end_time.split(':') %}
                                    {% set start_minutes = start_time[0]|int * 60 + start_time[1]|int %}
                                    {% set end_minutes = end_time[0]|int * 60 + end_time[1]|int %}
                                    {% set duration_minutes = end_minutes - start_minutes %}
                                    {% set hours = (duration_minutes / 60)|int %}
                                    {% set minutes = duration_minutes % 60 %}
                                    {{ hours }} ساعة و {{ minutes }} دقيقة
                                </td>
                                <td>{{ permission.reason }}</td>
                                <td>
                                    {% if permission.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif permission.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif permission.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>{{ permission.created_at }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-1"></i>
                    لا توجد طلبات أذونات حتى الآن.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
