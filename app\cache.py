"""
نظام التخزين المؤقت والأداء
Caching and Performance System
"""
import json
import pickle
import logging
from datetime import datetime, timedelta
from typing import Any, Optional, Union, Dict, List, Callable
from functools import wraps
import redis
from flask import current_app, request, g
from flask_caching import Cache
import hashlib


logger = logging.getLogger(__name__)


class CacheManager:
    """مدير التخزين المؤقت الرئيسي"""
    
    def __init__(self, app=None):
        self.cache = None
        self.redis_client = None
        self.app = app
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة نظام التخزين المؤقت"""
        self.app = app
        
        # إعداد Flask-Caching
        cache_config = {
            'CACHE_TYPE': app.config.get('CACHE_TYPE', 'simple'),
            'CACHE_DEFAULT_TIMEOUT': app.config.get('CACHE_DEFAULT_TIMEOUT', 300),
        }
        
        if cache_config['CACHE_TYPE'] == 'redis':
            cache_config.update({
                'CACHE_REDIS_URL': app.config.get('CACHE_REDIS_URL', 'redis://localhost:6379/1'),
                'CACHE_KEY_PREFIX': 'alemis_cache:',
            })
        
        self.cache = Cache(app, config=cache_config)
        
        # إعداد Redis client منفصل للعمليات المتقدمة
        try:
            redis_url = app.config.get('REDIS_URL', 'redis://localhost:6379/0')
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            self.redis_client.ping()  # اختبار الاتصال
            logger.info("تم الاتصال بـ Redis بنجاح")
        except Exception as e:
            logger.warning(f"فشل الاتصال بـ Redis: {e}")
            self.redis_client = None
    
    def get(self, key: str, default: Any = None) -> Any:
        """الحصول على قيمة من التخزين المؤقت"""
        try:
            return self.cache.get(key) or default
        except Exception as e:
            logger.error(f"خطأ في الحصول على القيمة من التخزين المؤقت: {e}")
            return default
    
    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> bool:
        """تعيين قيمة في التخزين المؤقت"""
        try:
            return self.cache.set(key, value, timeout=timeout)
        except Exception as e:
            logger.error(f"خطأ في تعيين القيمة في التخزين المؤقت: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """حذف قيمة من التخزين المؤقت"""
        try:
            return self.cache.delete(key)
        except Exception as e:
            logger.error(f"خطأ في حذف القيمة من التخزين المؤقت: {e}")
            return False
    
    def clear(self) -> bool:
        """مسح جميع القيم من التخزين المؤقت"""
        try:
            return self.cache.clear()
        except Exception as e:
            logger.error(f"خطأ في مسح التخزين المؤقت: {e}")
            return False
    
    def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """الحصول على قيم متعددة"""
        try:
            return self.cache.get_many(*keys)
        except Exception as e:
            logger.error(f"خطأ في الحصول على القيم المتعددة: {e}")
            return {}
    
    def set_many(self, mapping: Dict[str, Any], timeout: Optional[int] = None) -> bool:
        """تعيين قيم متعددة"""
        try:
            return self.cache.set_many(mapping, timeout=timeout)
        except Exception as e:
            logger.error(f"خطأ في تعيين القيم المتعددة: {e}")
            return False
    
    def delete_many(self, keys: List[str]) -> bool:
        """حذف قيم متعددة"""
        try:
            return self.cache.delete_many(*keys)
        except Exception as e:
            logger.error(f"خطأ في حذف القيم المتعددة: {e}")
            return False


class SessionManager:
    """مدير الجلسات المحسن"""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.session_prefix = "alemis_session:"
        self.user_sessions_prefix = "alemis_user_sessions:"
    
    def create_session(self, user_id: int, session_data: Dict[str, Any], 
                      expires_in: int = 7200) -> str:
        """إنشاء جلسة جديدة"""
        if not self.redis_client:
            return None
        
        try:
            # إنتاج معرف جلسة فريد
            session_id = hashlib.sha256(
                f"{user_id}_{datetime.utcnow().timestamp()}".encode()
            ).hexdigest()
            
            # حفظ بيانات الجلسة
            session_key = f"{self.session_prefix}{session_id}"
            session_data.update({
                'user_id': user_id,
                'created_at': datetime.utcnow().isoformat(),
                'last_activity': datetime.utcnow().isoformat()
            })
            
            self.redis_client.setex(
                session_key, 
                expires_in, 
                json.dumps(session_data, default=str)
            )
            
            # إضافة الجلسة إلى قائمة جلسات المستخدم
            user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
            self.redis_client.sadd(user_sessions_key, session_id)
            self.redis_client.expire(user_sessions_key, expires_in)
            
            logger.info(f"تم إنشاء جلسة جديدة للمستخدم {user_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الجلسة: {e}")
            return None
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات الجلسة"""
        if not self.redis_client:
            return None
        
        try:
            session_key = f"{self.session_prefix}{session_id}"
            session_data = self.redis_client.get(session_key)
            
            if session_data:
                data = json.loads(session_data)
                # تحديث آخر نشاط
                data['last_activity'] = datetime.utcnow().isoformat()
                self.redis_client.setex(
                    session_key,
                    self.redis_client.ttl(session_key),
                    json.dumps(data, default=str)
                )
                return data
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الجلسة: {e}")
            return None
    
    def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """تحديث بيانات الجلسة"""
        if not self.redis_client:
            return False
        
        try:
            session_key = f"{self.session_prefix}{session_id}"
            current_data = self.get_session(session_id)
            
            if current_data:
                current_data.update(data)
                current_data['last_activity'] = datetime.utcnow().isoformat()
                
                ttl = self.redis_client.ttl(session_key)
                self.redis_client.setex(
                    session_key,
                    ttl if ttl > 0 else 7200,
                    json.dumps(current_data, default=str)
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في تحديث الجلسة: {e}")
            return False
    
    def delete_session(self, session_id: str) -> bool:
        """حذف جلسة"""
        if not self.redis_client:
            return False
        
        try:
            session_key = f"{self.session_prefix}{session_id}"
            session_data = self.get_session(session_id)
            
            if session_data:
                user_id = session_data.get('user_id')
                
                # حذف الجلسة
                self.redis_client.delete(session_key)
                
                # إزالة من قائمة جلسات المستخدم
                if user_id:
                    user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
                    self.redis_client.srem(user_sessions_key, session_id)
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في حذف الجلسة: {e}")
            return False
    
    def delete_user_sessions(self, user_id: int) -> bool:
        """حذف جميع جلسات المستخدم"""
        if not self.redis_client:
            return False
        
        try:
            user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
            session_ids = self.redis_client.smembers(user_sessions_key)
            
            # حذف جميع الجلسات
            for session_id in session_ids:
                session_key = f"{self.session_prefix}{session_id}"
                self.redis_client.delete(session_key)
            
            # حذف قائمة الجلسات
            self.redis_client.delete(user_sessions_key)
            
            logger.info(f"تم حذف جميع جلسات المستخدم {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حذف جلسات المستخدم: {e}")
            return False


class PerformanceMonitor:
    """مراقب الأداء"""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.metrics_prefix = "alemis_metrics:"
    
    def record_request(self, endpoint: str, method: str, duration: float, 
                      status_code: int, user_id: Optional[int] = None):
        """تسجيل طلب HTTP"""
        if not self.redis_client:
            return
        
        try:
            timestamp = datetime.utcnow()
            date_key = timestamp.strftime('%Y-%m-%d')
            hour_key = timestamp.strftime('%Y-%m-%d:%H')
            
            # إحصائيات يومية
            daily_key = f"{self.metrics_prefix}daily:{date_key}"
            self.redis_client.hincrby(daily_key, 'total_requests', 1)
            self.redis_client.hincrby(daily_key, f'status_{status_code}', 1)
            self.redis_client.hincrbyfloat(daily_key, 'total_duration', duration)
            self.redis_client.expire(daily_key, 86400 * 30)  # 30 يوم
            
            # إحصائيات ساعية
            hourly_key = f"{self.metrics_prefix}hourly:{hour_key}"
            self.redis_client.hincrby(hourly_key, 'total_requests', 1)
            self.redis_client.hincrby(hourly_key, f'status_{status_code}', 1)
            self.redis_client.hincrbyfloat(hourly_key, 'total_duration', duration)
            self.redis_client.expire(hourly_key, 86400 * 7)  # 7 أيام
            
            # إحصائيات المسارات
            endpoint_key = f"{self.metrics_prefix}endpoint:{endpoint}:{date_key}"
            self.redis_client.hincrby(endpoint_key, 'requests', 1)
            self.redis_client.hincrbyfloat(endpoint_key, 'total_duration', duration)
            self.redis_client.expire(endpoint_key, 86400 * 7)
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الطلب: {e}")
    
    def get_daily_metrics(self, date: str) -> Dict[str, Any]:
        """الحصول على إحصائيات يومية"""
        if not self.redis_client:
            return {}
        
        try:
            daily_key = f"{self.metrics_prefix}daily:{date}"
            metrics = self.redis_client.hgetall(daily_key)
            
            if metrics:
                total_requests = int(metrics.get('total_requests', 0))
                total_duration = float(metrics.get('total_duration', 0))
                
                return {
                    'total_requests': total_requests,
                    'average_duration': total_duration / total_requests if total_requests > 0 else 0,
                    'status_codes': {
                        key.replace('status_', ''): int(value)
                        for key, value in metrics.items()
                        if key.startswith('status_')
                    }
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات اليومية: {e}")
            return {}


# مُزخرفات التخزين المؤقت
def cached(timeout: int = 300, key_prefix: str = None):
    """مُزخرف للتخزين المؤقت"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # إنتاج مفتاح التخزين المؤقت
            cache_key = _generate_cache_key(f, args, kwargs, key_prefix)
            
            # محاولة الحصول على القيمة من التخزين المؤقت
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # تنفيذ الدالة وحفظ النتيجة
            result = f(*args, **kwargs)
            cache_manager.set(cache_key, result, timeout=timeout)
            
            return result
        return decorated_function
    return decorator


def cache_key(*key_parts):
    """مُزخرف لتحديد مفتاح التخزين المؤقت"""
    def decorator(f):
        f._cache_key_parts = key_parts
        return f
    return decorator


def _generate_cache_key(func: Callable, args: tuple, kwargs: dict, 
                       prefix: Optional[str] = None) -> str:
    """إنتاج مفتاح التخزين المؤقت"""
    # استخدام اسم الدالة كأساس
    base_key = prefix or func.__name__
    
    # إضافة المعاملات
    key_parts = [base_key]
    
    # إضافة المعاملات الموضعية
    for arg in args:
        if hasattr(arg, 'id'):
            key_parts.append(f"id_{arg.id}")
        else:
            key_parts.append(str(arg))
    
    # إضافة المعاملات المسماة
    for key, value in sorted(kwargs.items()):
        key_parts.append(f"{key}_{value}")
    
    # إنتاج hash للمفتاح الطويل
    key_string = ":".join(key_parts)
    if len(key_string) > 200:
        key_string = hashlib.md5(key_string.encode()).hexdigest()
    
    return f"alemis:{key_string}"


# إنشاء مثيلات المدراء
cache_manager = CacheManager()
session_manager = None
performance_monitor = None


def init_performance_systems(app):
    """تهيئة أنظمة الأداء"""
    global session_manager, performance_monitor
    
    cache_manager.init_app(app)
    
    if cache_manager.redis_client:
        session_manager = SessionManager(cache_manager.redis_client)
        performance_monitor = PerformanceMonitor(cache_manager.redis_client)
    
    # تسجيل معالج لمراقبة الأداء
    @app.before_request
    def before_request():
        g.start_time = datetime.utcnow()
    
    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time') and performance_monitor:
            duration = (datetime.utcnow() - g.start_time).total_seconds()
            user_id = getattr(g, 'current_user', {}).get('id') if hasattr(g, 'current_user') else None
            
            performance_monitor.record_request(
                endpoint=request.endpoint or 'unknown',
                method=request.method,
                duration=duration,
                status_code=response.status_code,
                user_id=user_id
            )
        
        return response
