{% extends "base.html" %}

{% block title %}ALEMIS - أرصدة الإجازات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0 title-with-red-line">
                <i class="fas fa-chart-pie text-primary me-2"></i>
                أرصدة الإجازات
            </h2>
            <div class="d-flex align-items-center">
                <span class="badge bg-info fs-6 me-2">العام {{ current_year }}</span>
                <button class="btn btn-success btn-sm" onclick="exportBalances()">
                    <i class="fas fa-download me-1"></i>
                    تصدير Excel
                </button>
            </div>
        </div>
        <div class="red-line-animated"></div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card bg-gradient-primary text-white card-red-accent">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title mb-1">إجمالي الموظفين</h6>
                                <h3 class="mb-0">{{ users|length }}</h3>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-gradient-success text-white card-red-accent">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title mb-1">أنواع الإجازات</h6>
                                <h3 class="mb-0">{{ leave_types|length }}</h3>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-gradient-warning text-white card-red-accent">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title mb-1">الأرصدة المنخفضة</h6>
                                <h3 class="mb-0" id="lowBalanceCount">0</h3>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card bg-gradient-info text-white card-red-accent">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title mb-1">الأرصدة الممتلئة</h6>
                                <h3 class="mb-0" id="fullBalanceCount">0</h3>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-battery-full fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="red-line"></div>

        <!-- فلاتر البحث -->
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-light red-border-bottom">
                <h6 class="mb-0 title-with-red-line">
                    <i class="fas fa-filter me-1"></i>
                    فلاتر البحث والتصفية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="searchEmployee" class="form-label">البحث عن موظف</label>
                            <input type="text" class="form-control" id="searchEmployee" placeholder="اكتب اسم الموظف...">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="filterDepartment" class="form-label">تصفية حسب القسم</label>
                            <select class="form-select" id="filterDepartment">
                                <option value="">جميع الأقسام</option>
                                {% set departments_list = [] %}
                                {% for user_data in users %}
                                    {% if user_data.department_name and user_data.department_name not in departments_list %}
                                        {% set departments_list = departments_list + [user_data.department_name] %}
                                    {% endif %}
                                {% endfor %}
                                {% for dept in departments_list %}
                                <option value="{{ dept }}">{{ dept }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="filterBalance" class="form-label">تصفية حسب حالة الرصيد</label>
                            <select class="form-select" id="filterBalance">
                                <option value="">جميع الأرصدة</option>
                                <option value="low">أرصدة منخفضة (أقل من 30%)</option>
                                <option value="medium">أرصدة متوسطة (30% - 60%)</option>
                                <option value="high">أرصدة عالية (أكثر من 60%)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="red-line-thick"></div>

        <!-- جدول الأرصدة -->
        <div class="card card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-table me-1"></i>
                        أرصدة الإجازات التفصيلية
                    </div>
                    <div>
                        <button class="btn btn-light btn-sm" onclick="toggleView()">
                            <i class="fas fa-th-large me-1"></i>
                            تبديل العرض
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="balancesTable">
                        <thead class="table-dark">
                            <tr>
                                <th class="sortable" data-sort="name">
                                    <i class="fas fa-user me-1"></i>
                                    الموظف
                                    <i class="fas fa-sort ms-1"></i>
                                </th>
                                <th class="sortable" data-sort="department">
                                    <i class="fas fa-building me-1"></i>
                                    القسم
                                    <i class="fas fa-sort ms-1"></i>
                                </th>
                                {% for leave_type in leave_types %}
                                <th class="text-center">
                                    <i class="fas fa-calendar-check me-1"></i>
                                    {{ leave_type.name }}
                                </th>
                                {% endfor %}
                                <th class="text-center">
                                    <i class="fas fa-cogs me-1"></i>
                                    الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user_data in users %}
                            <tr class="employee-row" data-name="{{ user_data.first_name }} {{ user_data.last_name }}" data-department="{{ user_data.department_name }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">
                                            {{ user_data.first_name[0] }}{{ user_data.last_name[0] }}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ user_data.first_name }} {{ user_data.last_name }}</div>
                                            <small class="text-muted">ID: {{ user_data.id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ user_data.department_name }}</span>
                                </td>
                                {% for leave_type in leave_types %}
                                <td class="text-center">
                                    {% if leave_type.id in user_data.balances %}
                                    {% set balance = user_data.balances[leave_type.id] %}
                                    {% set remaining_days = balance.remaining_days|float %}
                                    {% set total_days = balance.total_days|float %}
                                    {% set used_days = balance.used_days|float %}
                                    {% set percentage = (remaining_days / total_days * 100) if total_days > 0 else 0 %}

                                    <div class="balance-card" data-percentage="{{ percentage }}">
                                        <div class="balance-circle {% if percentage > 60 %}bg-success{% elif percentage > 30 %}bg-warning{% else %}bg-danger{% endif %}">
                                            <div class="balance-text">
                                                <div class="balance-remaining">{{ remaining_days|int }}</div>
                                                <div class="balance-total">من {{ total_days|int }}</div>
                                            </div>
                                        </div>
                                        <div class="balance-details mt-2">
                                            <div class="progress balance-progress">
                                                <div class="progress-bar {% if percentage > 60 %}bg-success{% elif percentage > 30 %}bg-warning{% else %}bg-danger{% endif %}"
                                                     style="width: {{ percentage }}%"
                                                     data-bs-toggle="tooltip"
                                                     title="المتبقي: {{ remaining_days }} من {{ total_days }}">
                                                </div>
                                            </div>
                                            <small class="text-muted">مستخدم: {{ used_days|int }}</small>
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="balance-card">
                                        <div class="balance-circle bg-secondary">
                                            <div class="balance-text">
                                                <div class="balance-remaining">--</div>
                                                <div class="balance-total">غير محدد</div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </td>
                                {% endfor %}
                                <td class="text-center">
                                    <button type="button" class="btn btn-primary btn-sm toggle-edit-btn"
                                            onclick="toggleEditForm('{{ user_data.id }}')"
                                            title="تعديل أرصدة {{ user_data.first_name }} {{ user_data.last_name }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <!-- صف تعديل الأرصدة (مخفي افتراضياً) -->
                            <tr class="edit-balance-row" id="editRow{{ user_data.id }}" style="display: none;">
                                <td colspan="{{ leave_types|length + 3 }}">
                                    <div class="edit-balance-container">
                                        <div class="edit-balance-header">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="avatar-circle me-3">
                                                    {{ user_data.first_name[0] }}{{ user_data.last_name[0] }}
                                                </div>
                                                <div>
                                                    <h5 class="mb-0">تعديل أرصدة الإجازات</h5>
                                                    <small class="text-muted">{{ user_data.first_name }} {{ user_data.last_name }} - {{ user_data.department_name }}</small>
                                                </div>
                                                <button type="button" class="btn btn-outline-secondary btn-sm ms-auto" onclick="toggleEditForm('{{ user_data.id }}')">
                                                    <i class="fas fa-times"></i> إغلاق
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row">
                                            {% for leave_type in leave_types %}
                                            <div class="col-md-6 mb-4">
                                                <div class="leave-type-card">
                                                    <div class="leave-type-header">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-calendar-alt me-2"></i>
                                                            {{ leave_type.name }}
                                                        </h6>
                                                    </div>
                                                    <div class="leave-type-body">
                                                        <form method="POST" action="{{ url_for('edit_leave_balance', user_id=user_data.id, leave_type_id=leave_type.id) }}" class="balance-form">
                                                            {% set current_balance = user_data.balances.get(leave_type.id, {}) %}
                                                            {% set total_days = current_balance.get('total_days', leave_type.default_days) %}
                                                            {% set used_days = current_balance.get('used_days', 0) %}
                                                            {% set remaining_days = total_days - used_days %}

                                                            <div class="row">
                                                                <div class="col-6">
                                                                    <div class="mb-3">
                                                                        <label for="total_days{{ user_data.id }}{{ leave_type.id }}" class="form-label">
                                                                            <i class="fas fa-calendar-plus text-primary me-1"></i>
                                                                            إجمالي الأيام
                                                                        </label>
                                                                        <input type="number"
                                                                               class="form-control total-days-input"
                                                                               id="total_days{{ user_data.id }}{{ leave_type.id }}"
                                                                               name="total_days"
                                                                               value="{{ total_days }}"
                                                                               min="0"
                                                                               step="0.5"
                                                                               onchange="updateRemaining('{{ user_data.id }}', '{{ leave_type.id }}')">
                                                                    </div>
                                                                </div>
                                                                <div class="col-6">
                                                                    <div class="mb-3">
                                                                        <label for="used_days{{ user_data.id }}{{ leave_type.id }}" class="form-label">
                                                                            <i class="fas fa-calendar-minus text-warning me-1"></i>
                                                                            الأيام المستخدمة
                                                                        </label>
                                                                        <input type="number"
                                                                               class="form-control used-days-input"
                                                                               id="used_days{{ user_data.id }}{{ leave_type.id }}"
                                                                               name="used_days"
                                                                               value="{{ used_days }}"
                                                                               min="0"
                                                                               step="0.5"
                                                                               onchange="updateRemaining('{{ user_data.id }}', '{{ leave_type.id }}')">
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="remaining-display mb-3">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <span class="text-muted">الرصيد المتبقي:</span>
                                                                    <span class="remaining-value badge bg-success fs-6" id="remaining{{ user_data.id }}{{ leave_type.id }}">
                                                                        {{ remaining_days }}
                                                                    </span>
                                                                </div>
                                                                <div class="progress mt-2">
                                                                    {% set percentage = (remaining_days / total_days * 100) if total_days > 0 else 0 %}
                                                                    <div class="progress-bar {% if percentage > 60 %}bg-success{% elif percentage > 30 %}bg-warning{% else %}bg-danger{% endif %}"
                                                                         id="progress{{ user_data.id }}{{ leave_type.id }}"
                                                                         style="width: {{ percentage }}%">
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                                                <i class="fas fa-save me-1"></i>
                                                                حفظ التغييرات
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                        <div class="text-center mt-3">
                                            <button type="button" class="btn btn-success" onclick="saveAllBalances('{{ user_data.id }}')">
                                                <i class="fas fa-save me-1"></i>
                                                حفظ جميع التغييرات
                                            </button>
                                            <button type="button" class="btn btn-secondary ms-2" onclick="toggleEditForm('{{ user_data.id }}')">
                                                <i class="fas fa-times me-1"></i>
                                                إلغاء
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    /* إحصائيات سريعة */
    .stats-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, #1a4cb8, #2563eb) !important;
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #059669, #10b981) !important;
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #d97706, #f59e0b) !important;
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #0891b2, #06b6d4) !important;
    }

    .stats-icon {
        opacity: 0.7;
    }

    /* أفاتار الموظفين */
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #1a4cb8, #2563eb);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }

    .avatar-circle-modal {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 16px;
    }

    /* بطاقات الأرصدة */
    .balance-card {
        text-align: center;
        padding: 10px;
    }

    .balance-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        color: white;
        font-weight: bold;
    }

    .balance-text {
        text-align: center;
    }

    .balance-remaining {
        font-size: 18px;
        font-weight: bold;
    }

    .balance-total {
        font-size: 10px;
        opacity: 0.8;
    }

    .balance-progress {
        height: 6px;
        border-radius: 3px;
    }

    /* صف تعديل الأرصدة */
    .edit-balance-row {
        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        border-top: 3px solid #dc2626;
        animation: slideDown 0.3s ease-out;
    }

    .edit-balance-container {
        padding: 25px;
        border-radius: 10px;
        background: white;
        margin: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    .edit-balance-header {
        border-bottom: 2px solid #dc2626;
        padding-bottom: 15px;
        margin-bottom: 20px;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* بطاقات أنواع الإجازات */
    .leave-type-card {
        border: 1px solid #e5e7eb;
        border-radius: 10px;
        overflow: hidden;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .leave-type-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }

    .leave-type-header {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        padding: 15px;
        border-bottom: 1px solid #e5e7eb;
        border-top: 3px solid #dc2626;
    }

    .leave-type-body {
        padding: 20px;
    }

    .remaining-display {
        background: #f8fafc;
        border-radius: 8px;
        padding: 15px;
        border: 1px solid #e5e7eb;
        border-left: 4px solid #dc2626;
    }

    /* جدول محسن */
    .table th {
        background: #1f2937 !important;
        color: white !important;
        border: none !important;
        font-weight: 600;
        padding: 15px 10px;
    }

    .table td {
        padding: 15px 10px;
        vertical-align: middle;
        border-color: #e5e7eb;
    }

    .table-hover tbody tr:hover {
        background-color: #f8fafc;
    }

    /* تحسينات إضافية للأزرار */
    .toggle-edit-btn {
        transition: all 0.3s ease;
        border-radius: 8px;
        padding: 8px 12px;
    }

    .toggle-edit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .toggle-edit-btn.btn-danger {
        background: linear-gradient(135deg, #dc2626, #991b1b);
        border: none;
    }

    .toggle-edit-btn.btn-primary {
        background: linear-gradient(135deg, #1a4cb8, #2563eb);
        border: none;
    }

    /* تحسينات للنماذج */
    .form-control:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #1a4cb8, #2563eb);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #1e40af, #1d4ed8);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-success {
        background: linear-gradient(135deg, #059669, #10b981);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #047857, #059669);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .sortable {
        cursor: pointer;
        user-select: none;
    }

    .sortable:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    /* نافذة منبثقة محسنة */
    .modal-header.bg-gradient-primary {
        background: linear-gradient(135deg, #1a4cb8, #2563eb) !important;
        border: none;
    }

    .modal-footer.bg-light {
        border-top: 1px solid #e5e7eb;
    }

    /* أزرار محسنة */
    .edit-balance-btn {
        border-radius: 8px;
        padding: 8px 12px;
        transition: all 0.3s ease;
    }

    .edit-balance-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(26, 76, 184, 0.3);
    }

    /* فلاتر البحث */
    .form-control:focus, .form-select:focus {
        border-color: #1a4cb8;
        box-shadow: 0 0 0 0.2rem rgba(26, 76, 184, 0.25);
    }

    /* تحسينات إضافية */
    .card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
    }

    .card-header {
        border-radius: 12px 12px 0 0 !important;
        border: none;
    }

    .badge {
        font-size: 0.75rem;
        padding: 6px 12px;
        border-radius: 6px;
    }

    /* تأثيرات الحركة */
    .employee-row {
        transition: all 0.3s ease;
    }

    .employee-row:hover {
        background-color: #f8fafc !important;
        transform: scale(1.01);
    }

    /* تحسينات الاستجابة */
    @media (max-width: 768px) {
        .stats-card {
            margin-bottom: 15px;
        }

        .balance-circle {
            width: 50px;
            height: 50px;
        }

        .balance-remaining {
            font-size: 14px;
        }

        .table-responsive {
            font-size: 0.9rem;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // تحديث الرصيد المتبقي عند تغيير القيم
    function updateRemaining(userId, leaveTypeId) {
        const totalInput = document.getElementById(`total_days${userId}${leaveTypeId}`);
        const usedInput = document.getElementById(`used_days${userId}${leaveTypeId}`);
        const remainingSpan = document.getElementById(`remaining${userId}${leaveTypeId}`);
        const progressBar = document.getElementById(`progress${userId}${leaveTypeId}`);

        if (!totalInput || !usedInput || !remainingSpan || !progressBar) {
            console.error('Elements not found for user:', userId, 'leaveType:', leaveTypeId);
            return;
        }

        const total = parseFloat(totalInput.value) || 0;
        const used = parseFloat(usedInput.value) || 0;
        const remaining = Math.max(0, total - used);
        const percentage = total > 0 ? (remaining / total * 100) : 0;

        // تحديث النص مع تأثير حركي
        remainingSpan.style.transform = 'scale(1.1)';
        setTimeout(() => {
            remainingSpan.textContent = remaining.toFixed(1);
            remainingSpan.style.transform = 'scale(1)';
        }, 150);

        // تحديث شريط التقدم مع تأثير حركي
        progressBar.style.transition = 'width 0.5s ease, background-color 0.3s ease';
        progressBar.style.width = percentage + '%';

        // تغيير لون الشريط حسب النسبة
        progressBar.className = 'progress-bar';
        if (percentage > 60) {
            progressBar.classList.add('bg-success');
            remainingSpan.className = 'remaining-value badge bg-success fs-6';
        } else if (percentage > 30) {
            progressBar.classList.add('bg-warning');
            remainingSpan.className = 'remaining-value badge bg-warning fs-6';
        } else {
            progressBar.classList.add('bg-danger');
            remainingSpan.className = 'remaining-value badge bg-danger fs-6';
        }

        // تحقق من صحة البيانات
        if (used > total) {
            usedInput.style.borderColor = '#dc2626';
            usedInput.style.boxShadow = '0 0 0 0.2rem rgba(220, 38, 38, 0.25)';
        } else {
            usedInput.style.borderColor = '#e5e7eb';
            usedInput.style.boxShadow = 'none';
        }
    }

    // حفظ جميع التغييرات
    function saveAllBalances(userId) {
        const forms = document.querySelectorAll(`#editRow${userId} .balance-form`);
        let savedCount = 0;

        if (forms.length === 0) {
            alert('لا توجد نماذج للحفظ');
            return;
        }

        forms.forEach(form => {
            const formData = new FormData(form);
            fetch(form.action, {
                method: 'POST',
                body: formData
            }).then(response => {
                if (response.ok) {
                    savedCount++;
                    if (savedCount === forms.length) {
                        alert('تم حفظ جميع التغييرات بنجاح!');
                        location.reload();
                    }
                }
            }).catch(error => {
                console.error('خطأ في حفظ البيانات:', error);
                alert('حدث خطأ أثناء حفظ البيانات');
            });
        });
    }

    // تصدير البيانات إلى Excel
    function exportBalances() {
        // يمكن إضافة وظيفة التصدير هنا
        alert('سيتم إضافة وظيفة التصدير قريباً');
    }

    // تبديل العرض
    function toggleView() {
        // يمكن إضافة وظيفة تبديل العرض هنا
        alert('سيتم إضافة وظيفة تبديل العرض قريباً');
    }

    // إظهار/إخفاء نموذج تعديل الأرصدة
    function toggleEditForm(userId) {
        const editRow = document.getElementById('editRow' + userId);
        const toggleBtn = document.querySelector(`[onclick="toggleEditForm('${userId}')"]`);

        if (editRow.style.display === 'none' || editRow.style.display === '') {
            // إخفاء جميع صفوف التعديل الأخرى
            document.querySelectorAll('.edit-balance-row').forEach(row => {
                if (row.id !== 'editRow' + userId) {
                    row.style.display = 'none';
                }
            });

            // إظهار صف التعديل الحالي
            editRow.style.display = 'table-row';
            toggleBtn.innerHTML = '<i class="fas fa-times"></i>';
            toggleBtn.classList.remove('btn-primary');
            toggleBtn.classList.add('btn-danger');
            toggleBtn.title = 'إلغاء التعديل';

            // تمرير سلس إلى صف التعديل
            setTimeout(() => {
                editRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 100);
        } else {
            // إخفاء صف التعديل
            editRow.style.display = 'none';
            toggleBtn.innerHTML = '<i class="fas fa-edit"></i>';
            toggleBtn.classList.remove('btn-danger');
            toggleBtn.classList.add('btn-primary');
            toggleBtn.title = 'تعديل الأرصدة';
        }
    }

    // فلترة البيانات
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchEmployee');
        const departmentFilter = document.getElementById('filterDepartment');
        const balanceFilter = document.getElementById('filterBalance');
        const rows = document.querySelectorAll('.employee-row');

        // إضافة تأثيرات حركية للصفحة
        document.body.classList.add('fade-in');

        // تحسين أزرار التعديل
        const editButtons = document.querySelectorAll('.toggle-edit-btn');
        editButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                // تأثير بصري للزر
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // تحسين الأزرار
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // حساب الإحصائيات
        let lowBalanceCount = 0;
        let fullBalanceCount = 0;

        rows.forEach(row => {
            const balanceCards = row.querySelectorAll('.balance-card');
            balanceCards.forEach(card => {
                const percentage = parseFloat(card.dataset.percentage) || 0;
                if (percentage < 30) lowBalanceCount++;
                if (percentage > 90) fullBalanceCount++;
            });
        });

        document.getElementById('lowBalanceCount').textContent = lowBalanceCount;
        document.getElementById('fullBalanceCount').textContent = fullBalanceCount;

        // البحث والفلترة
        function filterRows() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedDepartment = departmentFilter.value;
            const selectedBalance = balanceFilter.value;

            rows.forEach(row => {
                const name = row.dataset.name.toLowerCase();
                const department = row.dataset.department;
                let balanceMatch = true;

                if (selectedBalance) {
                    const balanceCards = row.querySelectorAll('.balance-card');
                    balanceMatch = false;
                    balanceCards.forEach(card => {
                        const percentage = parseFloat(card.dataset.percentage) || 0;
                        if (selectedBalance === 'low' && percentage < 30) balanceMatch = true;
                        if (selectedBalance === 'medium' && percentage >= 30 && percentage <= 60) balanceMatch = true;
                        if (selectedBalance === 'high' && percentage > 60) balanceMatch = true;
                    });
                }

                const nameMatch = name.includes(searchTerm);
                const departmentMatch = !selectedDepartment || department === selectedDepartment;

                if (nameMatch && departmentMatch && balanceMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        searchInput.addEventListener('input', filterRows);
        departmentFilter.addEventListener('change', filterRows);
        balanceFilter.addEventListener('change', filterRows);

        // ترتيب الجدول
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', function() {
                const sortType = this.dataset.sort;
                // يمكن إضافة وظيفة الترتيب هنا
            });
        });

        // تفعيل التلميحات
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
