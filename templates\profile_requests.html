{% extends "base.html" %}

{% block title %}ALEMIS - طلبات تعديل الملفات الشخصية{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
<style>
    .medical-header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        position: relative;
        overflow: hidden;
    }

    .medical-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M50 10 L60 40 L90 40 L68 58 L78 88 L50 70 L22 88 L32 58 L10 40 L40 40 Z' fill='rgba(255,255,255,0.1)'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 6s ease-in-out infinite;
    }

    .medical-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -5%;
        width: 150px;
        height: 150px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M19 8h-2v3h-3v2h3v3h2v-3h3v-2h-3V8zM4 8h2v8H4V8zm3 0h2v8H7V8zm3 0h2v8h-2V8z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite reverse;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 300;
    }

    .medical-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: rgba(255,255,255,0.8);
    }
</style>
{% endblock %}

{% block content %}
<!-- Medical Header -->
<div class="medical-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="medical-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <h1 class="page-title">طلبات تعديل الملفات الشخصية</h1>
                <p class="page-subtitle">إدارة ومراجعة طلبات تعديل البيانات الشخصية للموظفين</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="medical-stats">
                    <div class="stat-item">
                        <i class="fas fa-clipboard-check"></i>
                        <span>نظام إدارة طبي متطور</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- Pending Requests Card -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-pulse">
                            <i class="fas fa-hourglass-half medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">طلبات تعديل الملفات الشخصية المعلقة</h5>
                            <small class="opacity-75">الطلبات التي تحتاج إلى مراجعة واعتماد</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    {% if pending_requests %}
                    <div class="table-responsive medical-table-container medical-table-advanced">
                        <table class="table medical-table">
                            <thead class="medical-table-header">
                                <tr>
                                    <th><i class="fas fa-hashtag me-2"></i>#</th>
                                    <th><i class="fas fa-user me-2"></i>الموظف</th>
                                    <th><i class="fas fa-edit me-2"></i>نوع الطلب</th>
                                    <th><i class="fas fa-database me-2"></i>القيمة الحالية</th>
                                    <th><i class="fas fa-arrow-right me-2"></i>القيمة الجديدة</th>
                                    <th><i class="fas fa-comment me-2"></i>السبب</th>
                                    <th><i class="fas fa-calendar me-2"></i>تاريخ الطلب</th>
                                    <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in pending_requests %}
                                <tr class="medical-table-row medical-table-row-advanced medical-wave">
                                    <td class="fw-bold text-primary">{{ request.id }}</td>
                                    <td>
                                        <div class="employee-info">
                                            <i class="fas fa-user-circle text-muted me-2 medical-icon-advanced"></i>
                                            <span class="fw-semibold">{{ request.first_name }} {{ request.last_name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        {% if request.request_type == 'username' %}
                                        <span class="badge medical-badge medical-badge-advanced badge-username">
                                            <i class="fas fa-user-tag me-1 medical-icon-advanced"></i>تغيير اسم المستخدم
                                        </span>
                                        {% elif request.request_type == 'password' %}
                                        <span class="badge medical-badge medical-badge-advanced badge-password">
                                            <i class="fas fa-key me-1 medical-icon-advanced"></i>تغيير كلمة المرور
                                        </span>
                                        {% elif request.request_type == 'email' %}
                                        <span class="badge medical-badge medical-badge-advanced badge-email">
                                            <i class="fas fa-envelope me-1 medical-icon-advanced"></i>تغيير البريد الإلكتروني
                                        </span>
                                        {% elif request.request_type == 'personal_info' %}
                                        <span class="badge medical-badge medical-badge-advanced badge-personal">
                                            <i class="fas fa-id-card me-1 medical-icon-advanced"></i>تغيير المعلومات الشخصية
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="data-value current-value">
                                            {{ request.current_value if request.current_value else '-' }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="data-value new-value">
                                            {% if request.request_type == 'password' %}
                                            <em class="text-muted"><i class="fas fa-eye-slash me-1"></i>مخفية</em>
                                            {% else %}
                                            {{ request.new_value }}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="reason-text">
                                            <i class="fas fa-quote-left text-muted me-1"></i>
                                            {{ request.reason }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <i class="fas fa-clock text-muted me-1"></i>
                                            {{ request.created_at }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button type="button" class="btn medical-btn medical-btn-advanced medical-btn-approve medical-shadow-glow-hover" onclick="approveRequest({{ request.id }})">
                                                <i class="fas fa-check-circle me-1 medical-icon-advanced"></i>موافقة
                                            </button>
                                            <button type="button" class="btn medical-btn medical-btn-advanced medical-btn-reject medical-shadow-glow-hover" onclick="rejectRequest({{ request.id }})">
                                                <i class="fas fa-times-circle me-1 medical-icon-advanced"></i>رفض
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="medical-alert medical-alert-info">
                        <div class="alert-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="alert-content">
                            <h6 class="mb-1">لا توجد طلبات معلقة</h6>
                            <p class="mb-0">لا توجد طلبات تعديل ملفات شخصية تحتاج إلى مراجعة في الوقت الحالي.</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Processed Requests Card -->
            <div class="card medical-card medical-card-premium medical-glow fade-in medical-border-glow" style="animation-delay: 0.2s;">
                <div class="card-header medical-card-header-secondary">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-wave">
                            <i class="fas fa-history medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">سجل طلبات التعديل السابقة</h5>
                            <small class="opacity-75">الطلبات التي تم معالجتها مسبقاً</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    {% if processed_requests %}
                    <div class="table-responsive medical-table-container">
                        <table class="table medical-table">
                            <thead class="medical-table-header">
                                <tr>
                                    <th><i class="fas fa-hashtag me-2"></i>#</th>
                                    <th><i class="fas fa-user me-2"></i>الموظف</th>
                                    <th><i class="fas fa-edit me-2"></i>نوع الطلب</th>
                                    <th><i class="fas fa-database me-2"></i>القيمة الحالية</th>
                                    <th><i class="fas fa-arrow-right me-2"></i>القيمة الجديدة</th>
                                    <th><i class="fas fa-check-circle me-2"></i>الحالة</th>
                                    <th><i class="fas fa-calendar me-2"></i>تاريخ الطلب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in processed_requests %}
                                <tr class="medical-table-row">
                                    <td class="fw-bold text-muted">{{ request.id }}</td>
                                    <td>
                                        <div class="employee-info">
                                            <i class="fas fa-user-circle text-muted me-2"></i>
                                            <span class="fw-semibold">{{ request.first_name }} {{ request.last_name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        {% if request.request_type == 'username' %}
                                        <span class="badge medical-badge badge-username-processed">
                                            <i class="fas fa-user-tag me-1"></i>تغيير اسم المستخدم
                                        </span>
                                        {% elif request.request_type == 'password' %}
                                        <span class="badge medical-badge badge-password-processed">
                                            <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                                        </span>
                                        {% elif request.request_type == 'email' %}
                                        <span class="badge medical-badge badge-email-processed">
                                            <i class="fas fa-envelope me-1"></i>تغيير البريد الإلكتروني
                                        </span>
                                        {% elif request.request_type == 'personal_info' %}
                                        <span class="badge medical-badge badge-personal-processed">
                                            <i class="fas fa-id-card me-1"></i>تغيير المعلومات الشخصية
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="data-value current-value">
                                            {{ request.current_value if request.current_value else '-' }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="data-value new-value">
                                            {% if request.request_type == 'password' %}
                                            <em class="text-muted"><i class="fas fa-eye-slash me-1"></i>مخفية</em>
                                            {% else %}
                                            {{ request.new_value }}
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if request.status == 'approved' %}
                                        <span class="badge medical-status-badge status-approved">
                                            <i class="fas fa-check-circle me-1"></i>تمت الموافقة
                                        </span>
                                        {% elif request.status == 'rejected' %}
                                        <span class="badge medical-status-badge status-rejected">
                                            <i class="fas fa-times-circle me-1"></i>مرفوض
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="date-info">
                                            <i class="fas fa-clock text-muted me-1"></i>
                                            {{ request.created_at }}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="medical-alert medical-alert-info">
                        <div class="alert-icon">
                            <i class="fas fa-archive"></i>
                        </div>
                        <div class="alert-content">
                            <h6 class="mb-1">لا توجد طلبات سابقة</h6>
                            <p class="mb-0">لا توجد طلبات تعديل ملفات شخصية تم معالجتها مسبقاً.</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function approveRequest(requestId) {
        if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
            fetch('/profile/requests/approve/' + requestId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تمت الموافقة على الطلب بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء معالجة الطلب');
            });
        }
    }

    function rejectRequest(requestId) {
        if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
            fetch('/profile/requests/reject/' + requestId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم رفض الطلب بنجاح');
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء معالجة الطلب');
            });
        }
    }
</script>
{% endblock %}
