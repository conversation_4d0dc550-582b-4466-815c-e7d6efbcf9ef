"""
نظام API المحسن
Enhanced API System
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from functools import wraps
from flask import Blueprint, request, jsonify, current_app, g
from flask_restx import Api, Resource, fields, Namespace
from marshmallow import Schema, fields as ma_fields, validate, ValidationError
from werkzeug.exceptions import BadRequest, Unauthorized, Forbidden, NotFound
from .security import JWTManager, require_auth, require_role
from .models import User, LeaveRequest, LeaveType, Department, LeaveBalance
from .database import db_manager, user_repository, leave_request_repository
from .notifications import notification_manager


logger = logging.getLogger(__name__)

# إنشاء Blueprint للـ API
api_bp = Blueprint('api', __name__, url_prefix='/api/v1')

# إعداد Flask-RESTX
api = Api(
    api_bp,
    version='1.0',
    title='ALEMIS API',
    description='نظام إدارة إجازات الموظفين في المختبرات - واجهة برمجة التطبيقات',
    doc='/docs/',
    prefix='/api/v1'
)

# إنشاء Namespaces
auth_ns = Namespace('auth', description='عمليات المصادقة')
users_ns = Namespace('users', description='إدارة المستخدمين')
leaves_ns = Namespace('leaves', description='إدارة الإجازات')
reports_ns = Namespace('reports', description='التقارير')

api.add_namespace(auth_ns)
api.add_namespace(users_ns)
api.add_namespace(leaves_ns)
api.add_namespace(reports_ns)


# Schemas للتحقق من البيانات
class LoginSchema(Schema):
    """مخطط تسجيل الدخول"""
    username = ma_fields.Str(required=True, validate=validate.Length(min=3, max=80))
    password = ma_fields.Str(required=True, validate=validate.Length(min=6))
    remember_me = ma_fields.Bool(missing=False)


class UserCreateSchema(Schema):
    """مخطط إنشاء مستخدم"""
    username = ma_fields.Str(required=True, validate=validate.Length(min=3, max=80))
    email = ma_fields.Email(required=True)
    password = ma_fields.Str(required=True, validate=validate.Length(min=8))
    first_name = ma_fields.Str(required=True, validate=validate.Length(min=2, max=50))
    last_name = ma_fields.Str(required=True, validate=validate.Length(min=2, max=50))
    role = ma_fields.Str(required=True, validate=validate.OneOf(['admin', 'hr', 'manager', 'gm', 'employee']))
    department_id = ma_fields.Int(allow_none=True)
    phone = ma_fields.Str(allow_none=True)


class LeaveRequestCreateSchema(Schema):
    """مخطط إنشاء طلب إجازة"""
    leave_type_id = ma_fields.Int(required=True)
    start_date = ma_fields.Date(required=True)
    end_date = ma_fields.Date(required=True)
    reason = ma_fields.Str(allow_none=True, validate=validate.Length(max=500))
    emergency_contact = ma_fields.Str(allow_none=True, validate=validate.Length(max=100))


# Models للتوثيق
user_model = api.model('User', {
    'id': fields.Integer(description='معرف المستخدم'),
    'username': fields.String(description='اسم المستخدم'),
    'email': fields.String(description='البريد الإلكتروني'),
    'full_name': fields.String(description='الاسم الكامل'),
    'role': fields.String(description='الدور'),
    'department_id': fields.Integer(description='معرف القسم'),
    'is_active': fields.Boolean(description='نشط'),
    'created_at': fields.DateTime(description='تاريخ الإنشاء')
})

leave_request_model = api.model('LeaveRequest', {
    'id': fields.Integer(description='معرف الطلب'),
    'user_id': fields.Integer(description='معرف المستخدم'),
    'leave_type_id': fields.Integer(description='معرف نوع الإجازة'),
    'start_date': fields.Date(description='تاريخ البداية'),
    'end_date': fields.Date(description='تاريخ النهاية'),
    'days_requested': fields.Integer(description='عدد الأيام المطلوبة'),
    'reason': fields.String(description='السبب'),
    'status': fields.String(description='الحالة'),
    'created_at': fields.DateTime(description='تاريخ الإنشاء')
})


# معالجات الأخطاء
@api.errorhandler(ValidationError)
def handle_validation_error(error):
    """معالج أخطاء التحقق"""
    return {'message': 'خطأ في البيانات المدخلة', 'errors': error.messages}, 400


@api.errorhandler(BadRequest)
def handle_bad_request(error):
    """معالج الطلبات الخاطئة"""
    return {'message': 'طلب خاطئ', 'error': str(error)}, 400


@api.errorhandler(Unauthorized)
def handle_unauthorized(error):
    """معالج عدم التخويل"""
    return {'message': 'غير مخول للوصول', 'error': str(error)}, 401


@api.errorhandler(Forbidden)
def handle_forbidden(error):
    """معالج المنع"""
    return {'message': 'ممنوع الوصول', 'error': str(error)}, 403


@api.errorhandler(NotFound)
def handle_not_found(error):
    """معالج عدم الوجود"""
    return {'message': 'المورد غير موجود', 'error': str(error)}, 404


# مُزخرف للتحقق من JWT
def jwt_required(f):
    """مُزخرف للتحقق من JWT Token"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')
        
        if auth_header:
            try:
                token = auth_header.split(' ')[1]  # Bearer <token>
            except IndexError:
                return {'message': 'تنسيق رمز التخويل غير صحيح'}, 401
        
        if not token:
            return {'message': 'رمز التخويل مطلوب'}, 401
        
        try:
            jwt_manager = JWTManager(current_app.config['JWT_SECRET_KEY'])
            payload = jwt_manager.verify_token(token)
            
            if not payload:
                return {'message': 'رمز التخويل غير صالح أو منتهي الصلاحية'}, 401
            
            # الحصول على المستخدم
            user = user_repository.get_by_id(payload['user_id'])
            if not user or not user.is_active:
                return {'message': 'المستخدم غير موجود أو غير نشط'}, 401
            
            g.current_user = user
            g.jwt_payload = payload
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من JWT: {e}")
            return {'message': 'خطأ في التحقق من رمز التخويل'}, 401
        
        return f(*args, **kwargs)
    return decorated_function


# Authentication Endpoints
@auth_ns.route('/login')
class LoginResource(Resource):
    """تسجيل الدخول"""
    
    @api.expect(api.model('LoginRequest', {
        'username': fields.String(required=True, description='اسم المستخدم'),
        'password': fields.String(required=True, description='كلمة المرور'),
        'remember_me': fields.Boolean(description='تذكرني')
    }))
    def post(self):
        """تسجيل الدخول"""
        try:
            schema = LoginSchema()
            data = schema.load(request.json)
        except ValidationError as e:
            return {'message': 'بيانات غير صحيحة', 'errors': e.messages}, 400
        
        # البحث عن المستخدم
        user = user_repository.get_by_username(data['username'])
        
        if not user or not user.check_password(data['password']):
            return {'message': 'اسم المستخدم أو كلمة المرور غير صحيحة'}, 401
        
        if not user.is_active:
            return {'message': 'الحساب غير نشط'}, 401
        
        # إنتاج JWT tokens
        jwt_manager = JWTManager(current_app.config['JWT_SECRET_KEY'])
        
        access_token = jwt_manager.generate_access_token(
            user.id, 
            user.role.value,
            expires_in=3600 if not data.get('remember_me') else 86400
        )
        
        refresh_token = jwt_manager.generate_refresh_token(user.id)
        
        # تحديث آخر تسجيل دخول
        user_repository.update(user.id, last_login=datetime.utcnow())
        
        return {
            'message': 'تم تسجيل الدخول بنجاح',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': user.to_dict(),
            'expires_in': 3600 if not data.get('remember_me') else 86400
        }


@auth_ns.route('/refresh')
class RefreshTokenResource(Resource):
    """تحديث الرمز"""
    
    def post(self):
        """تحديث رمز الوصول"""
        refresh_token = request.json.get('refresh_token')
        
        if not refresh_token:
            return {'message': 'رمز التحديث مطلوب'}, 400
        
        try:
            jwt_manager = JWTManager(current_app.config['JWT_SECRET_KEY'])
            payload = jwt_manager.verify_token(refresh_token)
            
            if not payload or payload.get('type') != 'refresh':
                return {'message': 'رمز التحديث غير صالح'}, 401
            
            user = user_repository.get_by_id(payload['user_id'])
            if not user or not user.is_active:
                return {'message': 'المستخدم غير موجود أو غير نشط'}, 401
            
            # إنتاج رمز وصول جديد
            new_access_token = jwt_manager.generate_access_token(
                user.id, 
                user.role.value
            )
            
            return {
                'access_token': new_access_token,
                'expires_in': 3600
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحديث الرمز: {e}")
            return {'message': 'خطأ في تحديث الرمز'}, 401


@auth_ns.route('/logout')
class LogoutResource(Resource):
    """تسجيل الخروج"""
    
    @jwt_required
    def post(self):
        """تسجيل الخروج"""
        # في التطبيق الحقيقي، يمكن إضافة الرمز إلى قائمة سوداء
        return {'message': 'تم تسجيل الخروج بنجاح'}


# Users Endpoints
@users_ns.route('/')
class UsersListResource(Resource):
    """قائمة المستخدمين"""
    
    @jwt_required
    @api.marshal_list_with(user_model)
    def get(self):
        """الحصول على قائمة المستخدمين"""
        # التحقق من الصلاحيات
        if g.current_user.role.value not in ['admin', 'hr', 'manager', 'gm']:
            return {'message': 'ليس لديك صلاحية لعرض المستخدمين'}, 403
        
        # معاملات الاستعلام
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        role = request.args.get('role')
        department_id = request.args.get('department_id', type=int)
        
        # بناء الاستعلام
        with db_manager.session_scope() as session:
            query = session.query(User)
            
            if role:
                query = query.filter(User.role == role)
            if department_id:
                query = query.filter(User.department_id == department_id)
            
            # تطبيق التصفح
            offset = (page - 1) * per_page
            users = query.offset(offset).limit(per_page).all()
            total = query.count()
            
            return {
                'users': [user.to_dict() for user in users],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            }


@users_ns.route('/<int:user_id>')
class UserResource(Resource):
    """مستخدم محدد"""
    
    @jwt_required
    @api.marshal_with(user_model)
    def get(self, user_id):
        """الحصول على مستخدم محدد"""
        # التحقق من الصلاحيات
        if (g.current_user.id != user_id and 
            g.current_user.role.value not in ['admin', 'hr', 'manager', 'gm']):
            return {'message': 'ليس لديك صلاحية لعرض هذا المستخدم'}, 403
        
        user = user_repository.get_by_id(user_id)
        if not user:
            return {'message': 'المستخدم غير موجود'}, 404
        
        return user.to_dict()


# Leave Requests Endpoints
@leaves_ns.route('/requests')
class LeaveRequestsResource(Resource):
    """طلبات الإجازة"""
    
    @jwt_required
    def get(self):
        """الحصول على طلبات الإجازة"""
        # معاملات الاستعلام
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        user_id = request.args.get('user_id', type=int)
        
        with db_manager.session_scope() as session:
            query = session.query(LeaveRequest)
            
            # تطبيق المرشحات حسب الدور
            if g.current_user.role.value == 'employee':
                query = query.filter(LeaveRequest.user_id == g.current_user.id)
            elif g.current_user.role.value == 'manager':
                # المدير يرى طلبات قسمه فقط
                query = query.join(User).filter(User.department_id == g.current_user.department_id)
            # Admin, HR, GM يرون جميع الطلبات
            
            if status:
                query = query.filter(LeaveRequest.status == status)
            if user_id and g.current_user.role.value in ['admin', 'hr', 'gm']:
                query = query.filter(LeaveRequest.user_id == user_id)
            
            # تطبيق التصفح
            offset = (page - 1) * per_page
            requests = query.offset(offset).limit(per_page).all()
            total = query.count()
            
            return {
                'requests': [req.to_dict() for req in requests],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total,
                    'pages': (total + per_page - 1) // per_page
                }
            }
    
    @jwt_required
    def post(self):
        """إنشاء طلب إجازة جديد"""
        try:
            schema = LeaveRequestCreateSchema()
            data = schema.load(request.json)
        except ValidationError as e:
            return {'message': 'بيانات غير صحيحة', 'errors': e.messages}, 400
        
        # التحقق من صحة التواريخ
        if data['end_date'] < data['start_date']:
            return {'message': 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'}, 400
        
        # حساب عدد الأيام
        days_requested = (data['end_date'] - data['start_date']).days + 1
        
        try:
            # إنشاء الطلب
            leave_request = leave_request_repository.create(
                user_id=g.current_user.id,
                leave_type_id=data['leave_type_id'],
                start_date=data['start_date'],
                end_date=data['end_date'],
                days_requested=days_requested,
                reason=data.get('reason'),
                emergency_contact=data.get('emergency_contact'),
                status='submitted',
                submitted_at=datetime.utcnow()
            )
            
            # إرسال إشعار
            notification_manager.send_leave_request_notification(leave_request, 'submitted')
            
            return {
                'message': 'تم إنشاء طلب الإجازة بنجاح',
                'request': leave_request.to_dict()
            }, 201
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء طلب الإجازة: {e}")
            return {'message': 'خطأ في إنشاء طلب الإجازة'}, 500
