{% extends "base.html" %}

{% block title %}ALEMIS - طلب إجازة جديدة{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
<style>
    .new-leave-header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        position: relative;
        overflow: hidden;
    }

    .new-leave-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 6s ease-in-out infinite;
    }

    .new-leave-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -5%;
        width: 150px;
        height: 150px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite reverse;
    }

    .form-group-medical {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-label-medical {
        font-weight: 600;
        color: var(--medical-dark);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-control-medical {
        border: 2px solid rgba(14, 165, 233, 0.2);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background: rgba(14, 165, 233, 0.02);
    }

    .form-control-medical:focus {
        border-color: var(--medical-primary);
        box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.25);
        background: white;
    }

    .form-select-medical {
        border: 2px solid rgba(14, 165, 233, 0.2);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background: rgba(14, 165, 233, 0.02);
    }

    .form-select-medical:focus {
        border-color: var(--medical-primary);
        box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.25);
        background: white;
    }

    .coverage-days-container {
        background: linear-gradient(145deg, #f8fafc, #e2e8f0);
        border: 2px solid rgba(14, 165, 233, 0.1);
        border-radius: 15px;
        padding: 1.5rem;
        max-height: 250px;
        overflow-y: auto;
    }

    .coverage-day-item {
        background: white;
        border: 2px solid rgba(14, 165, 233, 0.1);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        position: relative;
    }

    .coverage-day-item:hover {
        border-color: var(--medical-primary);
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(14, 165, 233, 0.1);
    }

    .coverage-day-item.selected {
        background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(14, 165, 233, 0.05));
        border-color: var(--medical-primary);
    }

    .form-check-medical {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-check-input-medical {
        width: 20px;
        height: 20px;
        border: 2px solid var(--medical-primary);
        border-radius: 4px;
    }

    .form-check-input-medical:checked {
        background-color: var(--medical-primary);
        border-color: var(--medical-primary);
    }

    .shift-type-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
        margin-right: 0.5rem;
    }

    .shift-morning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    .shift-evening {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
    }

    .shift-night {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        color: white;
    }

    .days-counter {
        background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(14, 165, 233, 0.05));
        border: 2px solid rgba(14, 165, 233, 0.2);
        border-radius: 15px;
        padding: 1rem;
        text-align: center;
        font-weight: 600;
        color: var(--medical-primary-dark);
        margin-top: 1rem;
    }

    .submit-section {
        background: linear-gradient(145deg, #f8fafc, #e2e8f0);
        border-radius: 15px;
        padding: 2rem;
        margin-top: 2rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<!-- New Leave Header -->
<div class="new-leave-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="leaves-icon">
                    <i class="fas fa-calendar-plus" style="font-size: 3rem; margin-bottom: 1rem; color: rgba(255,255,255,0.8);"></i>
                </div>
                <h1 style="font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); margin-bottom: 0.5rem;">طلب إجازة جديدة</h1>
                <p style="font-size: 1.1rem; opacity: 0.9; font-weight: 300;">تقديم طلب إجازة جديد بسهولة ويسر</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="leaves-stats">
                    <div class="stat-item">
                        <i class="fas fa-file-medical"></i>
                        <span>نموذج طبي متطور</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- Leave Form Card -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-pulse">
                            <i class="fas fa-edit medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">نموذج طلب إجازة</h5>
                            <small class="opacity-75">املأ البيانات المطلوبة لتقديم طلب الإجازة</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    <form method="POST" action="{{ url_for('new_leave') }}" enctype="multipart/form-data">
                        <div class="row mb-3">
                            <div class="col-md-6 form-group-medical">
                                <label for="leave_type_id" class="form-label-medical">
                                    <i class="fas fa-tags text-primary"></i>
                                    نوع الإجازة
                                </label>
                                <select class="form-select form-select-medical" id="leave_type_id" name="leave_type_id" required>
                                    <option value="" selected disabled>اختر نوع الإجازة</option>
                                    {% for leave_type in leave_types %}
                                    <option value="{{ leave_type.id }}">{{ leave_type.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 form-group-medical" id="coverage_days_div" style="display: none;">
                                <label class="form-label-medical">
                                    <i class="fas fa-calendar-week text-info"></i>
                                    أيام التغطية المتاحة
                                </label>
                                <div class="coverage-days-container">
                                    {% for coverage_day in coverage_days %}
                                    <div class="coverage-day-item">
                                        <div class="form-check form-check-medical">
                                            <input class="form-check-input form-check-input-medical coverage-day-checkbox" type="checkbox"
                                                   value="{{ coverage_day.id }}" id="coverage_day_{{ coverage_day.id }}"
                                                   name="coverage_day_ids" data-date="{{ coverage_day.coverage_date }}">
                                            <label class="form-check-label" for="coverage_day_{{ coverage_day.id }}">
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <div>
                                                        <i class="fas fa-calendar-day text-primary me-2"></i>
                                                        <strong>{{ coverage_day.coverage_date }}</strong>
                                                    </div>
                                                    <div>
                                                        {% if coverage_day.shift_type == 'morning' %}
                                                        <span class="shift-type-badge shift-morning">
                                                            <i class="fas fa-sun me-1"></i>دوام صباحي
                                                        </span>
                                                        {% elif coverage_day.shift_type == 'evening' %}
                                                        <span class="shift-type-badge shift-evening">
                                                            <i class="fas fa-cloud-sun me-1"></i>دوام مسائي
                                                        </span>
                                                        {% elif coverage_day.shift_type == 'night' %}
                                                        <span class="shift-type-badge shift-night">
                                                            <i class="fas fa-moon me-1"></i>دوام ليلي
                                                        </span>
                                                        {% else %}
                                                        <span class="shift-type-badge shift-morning">
                                                            <i class="fas fa-clock me-1"></i>{{ coverage_day.shift_type_ar }}
                                                        </span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                    {% if not coverage_days %}
                                    <div class="medical-alert medical-alert-info">
                                        <div class="alert-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="alert-content">
                                            <h6 class="mb-1">لا توجد أيام تغطية</h6>
                                            <p class="mb-0">لا توجد أيام تغطية متاحة لديك حالياً</p>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="form-text mt-2">
                                    <i class="fas fa-info-circle text-info me-1"></i>
                                    يمكنك اختيار أكثر من يوم تغطية
                                </div>
                                <div id="selected-coverage-info" class="mt-2"></div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6 form-group-medical">
                                <label for="start_date" class="form-label-medical">
                                    <i class="fas fa-calendar-day text-success"></i>
                                    تاريخ البداية
                                </label>
                                <input type="date" class="form-control form-control-medical" id="start_date" name="start_date" required>
                            </div>
                            <div class="col-md-6 form-group-medical">
                                <label for="end_date" class="form-label-medical">
                                    <i class="fas fa-calendar-check text-danger"></i>
                                    تاريخ النهاية
                                </label>
                                <input type="date" class="form-control form-control-medical" id="end_date" name="end_date" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12 form-group-medical">
                                <label for="reason" class="form-label-medical">
                                    <i class="fas fa-comment-alt text-warning"></i>
                                    سبب الإجازة
                                </label>
                                <textarea class="form-control form-control-medical" id="reason" name="reason" rows="4" required
                                          placeholder="اكتب سبب طلب الإجازة بوضوح..."></textarea>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12 form-group-medical">
                                <label for="document" class="form-label-medical">
                                    <i class="fas fa-file-upload text-info"></i>
                                    رفع مستند داعم (اختياري)
                                </label>
                                <input type="file" class="form-control form-control-medical" id="document" name="document"
                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                <div class="form-text mt-2">
                                    <i class="fas fa-info-circle text-muted me-1"></i>
                                    يمكنك رفع مستند داعم للطلب (PDF, Word, أو صورة) - الحد الأقصى 5 ميجابايت
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="medical-alert medical-alert-info">
                                    <div class="alert-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="alert-content">
                                        <h6 class="mb-1">ملاحظة مهمة</h6>
                                        <p class="mb-0">يجب الموافقة على طلب الإجازة من قبل مدير القسم وقسم الموارد البشرية قبل اعتمادها.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="submit-section">
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="submit" class="btn medical-btn-advanced medical-shadow-glow-hover me-3">
                                        <i class="fas fa-paper-plane me-1 medical-icon-advanced"></i>
                                        تقديم الطلب
                                    </button>
                                    <a href="{{ url_for('dashboard') }}" class="btn medical-btn-advanced"
                                       style="background: linear-gradient(135deg, #6b7280, #4b5563);">
                                        <i class="fas fa-times me-1 medical-icon-advanced"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calculate days between start and end dates
        var startDateInput = document.getElementById('start_date');
        var endDateInput = document.getElementById('end_date');
        var leaveTypeSelect = document.getElementById('leave_type_id');
        var coverageDaysDiv = document.getElementById('coverage_days_div');
        var coverageDaySelect = document.getElementById('coverage_day_id');

        // Show/hide coverage days select based on leave type
        leaveTypeSelect.addEventListener('change', function() {
            var selectedOption = this.options[this.selectedIndex];
            var leaveTypeName = selectedOption.textContent;

            if (leaveTypeName === 'بدل يوم تغطية') {
                coverageDaysDiv.style.display = 'block';

                // Handle multiple coverage days selection
                var coverageCheckboxes = document.querySelectorAll('.coverage-day-checkbox');
                var selectedInfo = document.getElementById('selected-coverage-info');

                coverageCheckboxes.forEach(function(checkbox) {
                    checkbox.addEventListener('change', function() {
                        updateSelectedCoverageDays();
                    });
                });

                function updateSelectedCoverageDays() {
                    var selectedCheckboxes = document.querySelectorAll('.coverage-day-checkbox:checked');
                    var selectedDates = [];

                    selectedCheckboxes.forEach(function(checkbox) {
                        selectedDates.push(checkbox.getAttribute('data-date'));
                    });

                    if (selectedDates.length > 0) {
                        selectedDates.sort();
                        var startDate = selectedDates[0];
                        var endDate = selectedDates[selectedDates.length - 1];

                        startDateInput.value = startDate;
                        endDateInput.value = endDate;

                        selectedInfo.innerHTML = '<div class="medical-alert medical-alert-info"><div class="alert-icon"><i class="fas fa-check-circle"></i></div><div class="alert-content"><h6 class="mb-1">تم الاختيار</h6><p class="mb-0">تم اختيار ' + selectedDates.length + ' يوم تغطية</p></div></div>';
                        updateDays();
                    } else {
                        selectedInfo.innerHTML = '';
                        startDateInput.value = '';
                        endDateInput.value = '';
                    }
                }
            } else {
                coverageDaysDiv.style.display = 'none';
                // Clear any selected coverage days
                var coverageCheckboxes = document.querySelectorAll('.coverage-day-checkbox');
                coverageCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = false;
                });
                document.getElementById('selected-coverage-info').innerHTML = '';
            }
        });

        function updateDays() {
            var startDate = new Date(startDateInput.value);
            var endDate = new Date(endDateInput.value);

            if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
                // Calculate the difference in days
                var timeDiff = endDate.getTime() - startDate.getTime();
                var dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // Include both start and end days

                if (dayDiff > 0) {
                    // Show days count
                    var daysInfo = document.getElementById('days-info');
                    if (!daysInfo) {
                        daysInfo = document.createElement('div');
                        daysInfo.id = 'days-info';
                        daysInfo.className = 'days-counter';
                        endDateInput.parentNode.appendChild(daysInfo);
                    }
                    daysInfo.innerHTML = '<i class="fas fa-calendar-day me-2"></i> عدد أيام الإجازة: <strong>' + dayDiff + '</strong> يوم';
                }
            }
        }

        startDateInput.addEventListener('change', updateDays);
        endDateInput.addEventListener('change', updateDays);

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            var selectedLeaveType = leaveTypeSelect.options[leaveTypeSelect.selectedIndex].textContent;

            if (selectedLeaveType === 'بدل يوم تغطية') {
                var selectedCheckboxes = document.querySelectorAll('.coverage-day-checkbox:checked');
                if (selectedCheckboxes.length === 0) {
                    e.preventDefault();
                    alert('يرجى اختيار يوم تغطية واحد على الأقل');
                    return;
                }
            }
        });
    });
</script>
{% endblock %}
