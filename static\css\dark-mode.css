/* Dark Mode Styles */
[data-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #ffffff;
    --bs-card-bg: #2d2d2d;
    --bs-border-color: #404040;
    --bs-navbar-bg: #2d2d2d;
    --bs-dropdown-bg: #2d2d2d;
    --bs-dropdown-border-color: #404040;
    --bs-table-bg: #2d2d2d;
    --bs-table-striped-bg: #333333;
    --bs-modal-bg: #2d2d2d;
    --bs-form-control-bg: #333333;
    --bs-form-control-border-color: #404040;
    --bs-btn-bg: #404040;
    --bs-btn-border-color: #404040;
    --primary-color: #60a5fa;
    --primary-dark: #3b82f6;
    --secondary-color: #E30613;
    --body-bg: #1a1a1a;
    --card-bg: #2d2d2d;
}

[data-theme="dark"] body {
    background-color: var(--bs-body-bg) !important;
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .navbar {
    background-color: var(--bs-navbar-bg) !important;
    border-bottom: 1px solid var(--bs-border-color);
}

[data-theme="dark"] .card {
    background-color: var(--bs-card-bg) !important;
    border: 1px solid var(--bs-border-color) !important;
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .card-header {
    background-color: rgba(96, 165, 250, 0.1) !important;
    border-bottom: 1px solid var(--bs-border-color) !important;
}

[data-theme="dark"] .table {
    background-color: var(--bs-table-bg) !important;
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td,
[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: var(--bs-table-striped-bg) !important;
}

[data-theme="dark"] .dropdown-menu {
    background-color: var(--bs-dropdown-bg) !important;
    border: 1px solid var(--bs-dropdown-border-color) !important;
}

[data-theme="dark"] .dropdown-item {
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: rgba(96, 165, 250, 0.1) !important;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--bs-form-control-bg) !important;
    border: 1px solid var(--bs-form-control-border-color) !important;
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--bs-form-control-bg) !important;
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25) !important;
}

[data-theme="dark"] .modal-content {
    background-color: var(--bs-modal-bg) !important;
    border: 1px solid var(--bs-border-color) !important;
}

[data-theme="dark"] .modal-header {
    border-bottom: 1px solid var(--bs-border-color) !important;
}

[data-theme="dark"] .modal-footer {
    border-top: 1px solid var(--bs-border-color) !important;
}

[data-theme="dark"] .alert {
    border: 1px solid var(--bs-border-color) !important;
}

[data-theme="dark"] .alert-info {
    background-color: rgba(96, 165, 250, 0.1) !important;
    border-color: rgba(96, 165, 250, 0.3) !important;
    color: #93c5fd !important;
}

[data-theme="dark"] .alert-success {
    background-color: rgba(34, 197, 94, 0.1) !important;
    border-color: rgba(34, 197, 94, 0.3) !important;
    color: #86efac !important;
}

[data-theme="dark"] .alert-warning {
    background-color: rgba(245, 158, 11, 0.1) !important;
    border-color: rgba(245, 158, 11, 0.3) !important;
    color: #fbbf24 !important;
}

[data-theme="dark"] .alert-danger {
    background-color: rgba(227, 6, 19, 0.1) !important;
    border-color: rgba(227, 6, 19, 0.3) !important;
    color: #fca5a5 !important;
}

[data-theme="dark"] .badge {
    border: 1px solid var(--bs-border-color);
}

[data-theme="dark"] .pagination .page-link {
    background-color: var(--bs-card-bg) !important;
    border: 1px solid var(--bs-border-color) !important;
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .pagination .page-link:hover {
    background-color: rgba(96, 165, 250, 0.1) !important;
}

[data-theme="dark"] .pagination .page-item.active .page-link {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

/* Theme Toggle Styles */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
    margin-left: 1rem;
}

.theme-switch {
    display: inline-block;
    height: 34px;
    position: relative;
    width: 60px;
}

.theme-switch input {
    display: none;
}

.slider {
    background-color: #ccc;
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
    border-radius: 34px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
}

.slider:before {
    background-color: #fff;
    bottom: 4px;
    content: "";
    height: 26px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 26px;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.sun-icon, .moon-icon {
    font-size: 14px;
    z-index: 1;
    position: relative;
}

.sun-icon {
    color: #f59e0b;
}

.moon-icon {
    color: #1e293b;
}

[data-theme="dark"] .sun-icon {
    color: #fbbf24;
}

[data-theme="dark"] .moon-icon {
    color: #e2e8f0;
}

/* Footer Dark Mode */
[data-theme="dark"] .medical-footer {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%) !important;
    border-top: 1px solid var(--bs-border-color) !important;
}

[data-theme="dark"] .footer-content {
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .footer-title,
[data-theme="dark"] .footer-section-title {
    color: var(--bs-body-color) !important;
}

[data-theme="dark"] .footer-links a,
[data-theme="dark"] .footer-contact span {
    color: #94a3b8 !important;
}

[data-theme="dark"] .footer-links a:hover {
    color: var(--primary-color) !important;
}

[data-theme="dark"] .footer-bottom {
    border-top: 1px solid var(--bs-border-color) !important;
}
