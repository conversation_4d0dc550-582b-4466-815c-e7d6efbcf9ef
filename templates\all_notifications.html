{% extends "base.html" %}

{% block title %}جميع الإشعارات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-bell me-1"></i>
                    جميع الإشعارات
                </div>
                <div class="card-body">
                    {% if notifications %}
                    <div class="row">
                        <div class="col-md-12">
                            <p class="text-muted mb-3">
                                <i class="fas fa-info-circle me-1"></i>
                                إجمالي الإشعارات: {{ notifications|length }}
                            </p>
                        </div>
                    </div>

                    <div class="list-group">
                        {% for notification in notifications %}
                        <div class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="badge bg-{{ notification.color }} me-2">
                                            <i class="{{ notification.icon }} me-1"></i>
                                            {{ notification.type|replace('_', ' ')|title }}
                                        </span>
                                        <h6 class="mb-0">{{ notification.title }}</h6>
                                    </div>
                                    <p class="mb-1">{{ notification.message }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ notification.date }}
                                    </small>
                                </div>
                                <div class="ms-3">
                                    <a href="{{ notification.url }}" class="btn btn-outline-{{ notification.color }} btn-sm">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- إحصائيات الإشعارات -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-pie me-1"></i>
                                        إحصائيات الإشعارات
                                    </h6>
                                    <div class="row">
                                        {% set leave_count = notifications|selectattr('type', 'equalto', 'leave_request')|list|length %}
                                        {% set coverage_count = notifications|selectattr('type', 'equalto', 'coverage_request')|list|length %}
                                        {% set swap_count = notifications|selectattr('type', 'equalto', 'swap_request')|list|length %}
                                        
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <div class="h4 text-primary">{{ leave_count }}</div>
                                                <small class="text-muted">طلبات إجازة</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <div class="h4 text-success">{{ coverage_count }}</div>
                                                <small class="text-muted">طلبات تغطية</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <div class="h4 text-warning">{{ swap_count }}</div>
                                                <small class="text-muted">طلبات تبديل</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد إشعارات جديدة</h5>
                        <p class="text-muted">جميع الطلبات تم مراجعتها</p>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-home me-1"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% if notifications %}
<!-- أزرار الإجراءات السريعة -->
<div class="row mt-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-1"></i>
                إجراءات سريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('leave_requests') }}" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-calendar-alt me-1"></i>
                            مراجعة طلبات الإجازة
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('coverage_requests') }}" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-exchange-alt me-1"></i>
                            مراجعة طلبات التغطية
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('shift_swap_requests') }}" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-sync-alt me-1"></i>
                            مراجعة طلبات التبديل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الصفحة كل 30 ثانية للحصول على إشعارات جديدة
    setInterval(function() {
        // يمكن إضافة AJAX هنا لتحديث الإشعارات دون إعادة تحميل الصفحة
        console.log('تحديث الإشعارات...');
    }, 30000);
    
    // إضافة تأثيرات بصرية للإشعارات
    const notifications = document.querySelectorAll('.list-group-item');
    notifications.forEach((notification, index) => {
        notification.style.animationDelay = `${index * 0.1}s`;
        notification.classList.add('fade-in');
    });
});
</script>

<style>
.fade-in {
    animation: fadeInUp 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.list-group-item:hover {
    background-color: rgba(96, 165, 250, 0.05);
    transform: translateX(5px);
    transition: all 0.3s ease;
}
</style>
{% endblock %}
