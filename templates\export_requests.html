{% extends "base.html" %}

{% block title %}ALEMIS - تصدير الطلبات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h2 class="mb-4">تصدير الطلبات إلى PDF</h2>
        
        <!-- أدوات التصفية -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter me-1"></i>
                تصفية الطلبات
            </div>
            <div class="card-body">
                <form id="filter-form" method="GET" action="{{ url_for('export_requests') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="request_type" class="form-label">نوع الطلب</label>
                                <select class="form-select" id="request_type" name="request_type">
                                    <option value="all" {% if request_type == 'all' %}selected{% endif %}>جميع الطلبات</option>
                                    <option value="leave" {% if request_type == 'leave' %}selected{% endif %}>طلبات الإجازة</option>
                                    <option value="coverage" {% if request_type == 'coverage' %}selected{% endif %}>طلبات التغطية</option>
                                    <option value="shift_swap" {% if request_type == 'shift_swap' %}selected{% endif %}>طلبات تبديل الدوام</option>
                                    <option value="profile" {% if request_type == 'profile' %}selected{% endif %}>طلبات تعديل الملف الشخصي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" {% if status == 'all' %}selected{% endif %}>جميع الحالات</option>
                                    <option value="approved" {% if status == 'approved' %}selected{% endif %}>تمت الموافقة</option>
                                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                    <option value="rejected" {% if status == 'rejected' %}selected{% endif %}>مرفوض</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="date_range" class="form-label">الفترة الزمنية</label>
                                <select class="form-select" id="date_range" name="date_range">
                                    <option value="all" {% if date_range == 'all' %}selected{% endif %}>جميع الفترات</option>
                                    <option value="today" {% if date_range == 'today' %}selected{% endif %}>اليوم</option>
                                    <option value="week" {% if date_range == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                                    <option value="month" {% if date_range == 'month' %}selected{% endif %}>هذا الشهر</option>
                                    <option value="year" {% if date_range == 'year' %}selected{% endif %}>هذه السنة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- قائمة الطلبات -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-list me-1"></i>
                قائمة الطلبات المعتمدة
            </div>
            <div class="card-body">
                {% if requests %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>نوع الطلب</th>
                                <th>تاريخ الطلب</th>
                                <th>الحالة</th>
                                <th>تفاصيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in requests %}
                            <tr>
                                <td>
                                    {% if request.request_type == 'leave' %}
                                    <span class="badge bg-primary">طلب إجازة</span>
                                    {% elif request.request_type == 'coverage' %}
                                    <span class="badge bg-success">طلب تغطية</span>
                                    {% elif request.request_type == 'shift_swap' %}
                                    <span class="badge bg-info">طلب تبديل دوام</span>
                                    {% elif request.request_type == 'profile' %}
                                    <span class="badge bg-warning">طلب تعديل ملف شخصي</span>
                                    {% endif %}
                                </td>
                                <td>{{ request.created_at }}</td>
                                <td>
                                    {% if request.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif request.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif request.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if request.request_type == 'leave' %}
                                    {{ request.leave_type_name }} ({{ request.start_date }} - {{ request.end_date }})
                                    {% elif request.request_type == 'coverage' %}
                                    {{ request.coverage_date }} ({{ request.coverage_type_name }})
                                    {% elif request.request_type == 'shift_swap' %}
                                    {% if request.swap_duration == 'one_day' %}
                                    {{ request.swap_date }}
                                    {% else %}
                                    {{ request.start_date }} - {{ request.end_date }}
                                    {% endif %}
                                    {% elif request.request_type == 'profile' %}
                                    {{ request.profile_change_type }}
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('export_request_pdf', request_type=request.request_type, request_id=request.id) }}" class="btn btn-sm btn-primary" target="_blank">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        تصدير PDF
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد طلبات تطابق معايير البحث.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // يمكن إضافة أي سكريبت إضافي هنا إذا لزم الأمر
    });
</script>
{% endblock %}
