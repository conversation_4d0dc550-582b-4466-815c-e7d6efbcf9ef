{% extends "base.html" %}

{% block title %}ALEMIS - لوحة المعلومات التفاعلية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2 class="mb-4 title-with-red-line">
            <i class="fas fa-chart-pie me-2"></i>
            لوحة المعلومات التفاعلية
        </h2>
        <div class="red-line-animated"></div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white mb-4 card-red-accent">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-75 small">إجمالي الموظفين</div>
                        <div class="text-lg fw-bold">{{ stats.total_employees }}</div>
                    </div>
                    <div class="fa-3x opacity-50">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white mb-4 card-red-accent">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-75 small">في إجازة اليوم</div>
                        <div class="text-lg fw-bold">{{ stats.on_leave_today }}</div>
                    </div>
                    <div class="fa-3x opacity-50">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card bg-success text-white mb-4 card-red-accent">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-75 small">طلبات معلقة</div>
                        <div class="text-lg fw-bold">{{ stats.pending_requests }}</div>
                    </div>
                    <div class="fa-3x opacity-50">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white mb-4 card-red-accent">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="text-white-75 small">طلبات هذا الشهر</div>
                        <div class="text-lg fw-bold">{{ stats.monthly_requests }}</div>
                    </div>
                    <div class="fa-3x opacity-50">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row">
    <!-- رسم بياني لأنواع الإجازات -->
    <div class="col-xl-6 col-lg-6">
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-primary text-white red-border-bottom">
                <i class="fas fa-chart-pie me-1"></i>
                توزيع أنواع الإجازات
            </div>
            <div class="card-body">
                <canvas id="leaveTypesChart" width="100%" height="50"></canvas>
            </div>
        </div>
    </div>

    <!-- رسم بياني للطلبات الشهرية -->
    <div class="col-xl-6 col-lg-6">
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-success text-white red-border-bottom">
                <i class="fas fa-chart-bar me-1"></i>
                الطلبات الشهرية
            </div>
            <div class="card-body">
                <canvas id="monthlyRequestsChart" width="100%" height="50"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- رسم بياني لحالات الطلبات -->
    <div class="col-xl-6 col-lg-6">
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-warning text-white red-border-bottom">
                <i class="fas fa-chart-doughnut me-1"></i>
                حالات الطلبات
            </div>
            <div class="card-body">
                <canvas id="requestStatusChart" width="100%" height="50"></canvas>
            </div>
        </div>
    </div>

    <!-- رسم بياني للأقسام -->
    <div class="col-xl-6 col-lg-6">
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-info text-white red-border-bottom">
                <i class="fas fa-chart-area me-1"></i>
                الطلبات حسب القسم
            </div>
            <div class="card-body">
                <canvas id="departmentChart" width="100%" height="50"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- جدول الطلبات الحديثة -->
<div class="row">
    <div class="col-12">
        <div class="card mb-4 card-red-accent">
            <div class="card-header bg-dark text-white red-border-bottom">
                <i class="fas fa-list me-1"></i>
                آخر الطلبات
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>نوع الطلب</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in recent_requests %}
                            <tr>
                                <td>{{ request.employee_name }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ request.request_type }}</span>
                                </td>
                                <td>{{ request.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if request.status == 'approved' %}
                                        <span class="badge bg-success">موافق عليه</span>
                                    {% elif request.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوض</span>
                                    {% else %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// بيانات الرسوم البيانية
const leaveTypesData = {{ leave_types_data | safe }};
const monthlyRequestsData = {{ monthly_requests_data | safe }};
const requestStatusData = {{ request_status_data | safe }};
const departmentData = {{ department_data | safe }};

// رسم بياني لأنواع الإجازات
const leaveTypesCtx = document.getElementById('leaveTypesChart').getContext('2d');
new Chart(leaveTypesCtx, {
    type: 'pie',
    data: {
        labels: leaveTypesData.labels,
        datasets: [{
            data: leaveTypesData.data,
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني للطلبات الشهرية
const monthlyRequestsCtx = document.getElementById('monthlyRequestsChart').getContext('2d');
new Chart(monthlyRequestsCtx, {
    type: 'bar',
    data: {
        labels: monthlyRequestsData.labels,
        datasets: [{
            label: 'عدد الطلبات',
            data: monthlyRequestsData.data,
            backgroundColor: '#36A2EB'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// رسم بياني لحالات الطلبات
const requestStatusCtx = document.getElementById('requestStatusChart').getContext('2d');
new Chart(requestStatusCtx, {
    type: 'doughnut',
    data: {
        labels: requestStatusData.labels,
        datasets: [{
            data: requestStatusData.data,
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#ffc107'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني للأقسام
const departmentCtx = document.getElementById('departmentChart').getContext('2d');
new Chart(departmentCtx, {
    type: 'line',
    data: {
        labels: departmentData.labels,
        datasets: [{
            label: 'عدد الطلبات',
            data: departmentData.data,
            borderColor: '#17a2b8',
            backgroundColor: 'rgba(23, 162, 184, 0.1)',
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
