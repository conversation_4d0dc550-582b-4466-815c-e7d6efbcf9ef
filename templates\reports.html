{% extends "base.html" %}

{% block title %}ALEMIS - التقارير{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
<style>
    .reports-header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        position: relative;
        overflow: hidden;
    }

    .reports-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 6s ease-in-out infinite;
    }

    .reports-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -5%;
        width: 150px;
        height: 150px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite reverse;
    }

    .report-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        border: 2px solid rgba(14, 165, 233, 0.1);
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .report-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #0ea5e9, #06b6d4, #0891b2);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .report-card:hover::before {
        transform: scaleX(1);
    }

    .report-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(14, 165, 233, 0.2);
        border-color: var(--medical-primary);
    }

    .report-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        transition: all 0.3s ease;
        position: relative;
    }

    .report-icon-leaves {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .report-icon-employees {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
    }

    .report-icon-statistics {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    .report-card:hover .report-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .report-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--medical-dark);
        margin-bottom: 1rem;
    }

    .report-description {
        color: var(--medical-secondary);
        font-size: 0.95rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .report-btn {
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        border: none;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .report-btn:hover {
        background: linear-gradient(135deg, #0284c7, #0369a1);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(14, 165, 233, 0.3);
        color: white;
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(14, 165, 233, 0.1);
        border: 2px solid rgba(14, 165, 233, 0.1);
        position: relative;
        overflow: hidden;
    }

    .chart-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #0ea5e9, #06b6d4, #0891b2);
    }

    .report-chart {
        height: 300px;
        position: relative;
    }

    .chart-table {
        margin-top: 1.5rem;
    }

    .chart-table table {
        background: rgba(14, 165, 233, 0.02);
        border-radius: 10px;
        overflow: hidden;
    }

    .chart-table th {
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        color: white;
        font-weight: 600;
        padding: 1rem;
        border: none;
    }

    .chart-table td {
        padding: 0.75rem 1rem;
        border: none;
        border-bottom: 1px solid rgba(14, 165, 233, 0.1);
    }

    .chart-table tr:hover {
        background: rgba(14, 165, 233, 0.05);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .monthly-chart-container {
        grid-column: 1 / -1;
    }
</style>
{% endblock %}

{% block content %}
<!-- Reports Header -->
<div class="reports-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="reports-icon">
                    <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 1rem; color: rgba(255,255,255,0.8);"></i>
                </div>
                <h1 style="font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); margin-bottom: 0.5rem;">التقارير والإحصائيات</h1>
                <p style="font-size: 1.1rem; opacity: 0.9; font-weight: 300;">تقارير شاملة وإحصائيات تفصيلية لجميع العمليات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="reports-stats">
                    <div class="stat-item">
                        <i class="fas fa-analytics"></i>
                        <span>تحليلات متقدمة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <!-- Report Navigation -->
            <div class="card medical-card medical-card-premium medical-glow medical-particles mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-pulse">
                            <i class="fas fa-chart-bar medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">أنواع التقارير</h5>
                            <small class="opacity-75">اختر نوع التقرير المطلوب</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="report-card">
                                <div class="report-icon report-icon-leaves">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <h5 class="report-title">تقرير الإجازات</h5>
                                <p class="report-description">عرض تفاصيل طلبات الإجازات مع إمكانية التصفية حسب القسم ونوع الإجازة والحالة.</p>
                                <a href="{{ url_for('leave_report') }}" class="report-btn">
                                    <i class="fas fa-eye"></i>
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="report-card">
                                <div class="report-icon report-icon-employees">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h5 class="report-title">تقرير الموظفين</h5>
                                <p class="report-description">عرض إحصائيات الموظفين حسب القسم والدور مع معلومات عن أرصدة الإجازات.</p>
                                <a href="{{ url_for('employee_report') }}" class="report-btn">
                                    <i class="fas fa-eye"></i>
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="report-card">
                                <div class="report-icon report-icon-statistics">
                                    <i class="fas fa-chart-pie"></i>
                                </div>
                                <h5 class="report-title">تقرير الإحصائيات</h5>
                                <p class="report-description">عرض إحصائيات عامة عن الإجازات والتغطية والأذونات مع رسوم بيانية توضيحية.</p>
                                <a href="{{ url_for('statistics_report') }}" class="report-btn">
                                    <i class="fas fa-eye"></i>
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Overview -->
            <div class="stats-grid">
                <div class="chart-container">
                    <div class="d-flex align-items-center mb-3">
                        <div class="header-icon medical-pulse me-3">
                            <i class="fas fa-building medical-icon-advanced" style="color: var(--medical-primary);"></i>
                        </div>
                        <div>
                            <h5 class="mb-0" style="color: var(--medical-dark);">الإجازات حسب القسم</h5>
                            <small class="text-muted">توزيع الإجازات على الأقسام المختلفة</small>
                        </div>
                    </div>
                    <div class="report-chart">
                        <canvas id="departmentChart"></canvas>
                    </div>
                    <div class="chart-table">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-building me-2"></i>القسم</th>
                                    <th><i class="fas fa-chart-bar me-2"></i>عدد الإجازات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in dept_stats %}
                                <tr>
                                    <td>{{ stat.department_name }}</td>
                                    <td><span class="badge" style="background: linear-gradient(135deg, #0ea5e9, #0284c7); color: white;">{{ stat.leave_count }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="d-flex align-items-center mb-3">
                        <div class="header-icon medical-pulse me-3">
                            <i class="fas fa-list-alt medical-icon-advanced" style="color: var(--medical-primary);"></i>
                        </div>
                        <div>
                            <h5 class="mb-0" style="color: var(--medical-dark);">الإجازات حسب النوع</h5>
                            <small class="text-muted">توزيع أنواع الإجازات المختلفة</small>
                        </div>
                    </div>
                    <div class="report-chart">
                        <canvas id="leaveTypeChart"></canvas>
                    </div>
                    <div class="chart-table">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-tag me-2"></i>نوع الإجازة</th>
                                    <th><i class="fas fa-chart-pie me-2"></i>عدد الإجازات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in type_stats %}
                                <tr>
                                    <td>{{ stat.leave_type_name }}</td>
                                    <td><span class="badge" style="background: linear-gradient(135deg, #10b981, #059669); color: white;">{{ stat.leave_count }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="chart-container monthly-chart-container">
                <div class="d-flex align-items-center mb-3">
                    <div class="header-icon medical-pulse me-3">
                        <i class="fas fa-calendar medical-icon-advanced" style="color: var(--medical-primary);"></i>
                    </div>
                    <div>
                        <h5 class="mb-0" style="color: var(--medical-dark);">الإجازات حسب الشهر</h5>
                        <small class="text-muted">اتجاه الإجازات على مدار السنة</small>
                    </div>
                </div>
                <div class="report-chart">
                    <canvas id="monthlyChart"></canvas>
                </div>
                <div class="chart-table">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar-alt me-2"></i>الشهر</th>
                                <th><i class="fas fa-chart-line me-2"></i>عدد الإجازات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stat in month_stats %}
                            <tr>
                                <td>{{ stat.month_name }}</td>
                                <td><span class="badge" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">{{ stat.leave_count }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Medical color palette
        const medicalColors = {
            primary: '#0ea5e9',
            secondary: '#0284c7',
            success: '#10b981',
            warning: '#f59e0b',
            danger: '#ef4444',
            info: '#06b6d4',
            purple: '#8b5cf6',
            pink: '#ec4899'
        };

        // Department Chart
        var deptCtx = document.getElementById('departmentChart').getContext('2d');
        var deptChart = new Chart(deptCtx, {
            type: 'bar',
            data: {
                labels: [{% for stat in dept_stats %}'{{ stat.department_name }}',{% endfor %}],
                datasets: [{
                    label: 'عدد الإجازات',
                    data: [{% for stat in dept_stats %}{{ stat.leave_count }},{% endfor %}],
                    backgroundColor: medicalColors.primary,
                    borderColor: medicalColors.secondary,
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(14, 165, 233, 0.9)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: medicalColors.secondary,
                        borderWidth: 1,
                        cornerRadius: 8
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                            color: '#6b7280'
                        },
                        grid: {
                            color: 'rgba(14, 165, 233, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#6b7280'
                        },
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Leave Type Chart
        var typeCtx = document.getElementById('leaveTypeChart').getContext('2d');
        var typeChart = new Chart(typeCtx, {
            type: 'doughnut',
            data: {
                labels: [{% for stat in type_stats %}'{{ stat.leave_type_name }}',{% endfor %}],
                datasets: [{
                    data: [{% for stat in type_stats %}{{ stat.leave_count }},{% endfor %}],
                    backgroundColor: [
                        medicalColors.primary,
                        medicalColors.success,
                        medicalColors.warning,
                        medicalColors.danger,
                        medicalColors.info,
                        medicalColors.purple,
                        medicalColors.pink
                    ],
                    borderWidth: 3,
                    borderColor: '#ffffff',
                    hoverBorderWidth: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            color: '#6b7280'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(14, 165, 233, 0.9)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: medicalColors.secondary,
                        borderWidth: 1,
                        cornerRadius: 8
                    }
                }
            }
        });

        // Monthly Chart
        var monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        var monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: [{% for stat in month_stats %}'{{ stat.month_name }}',{% endfor %}],
                datasets: [{
                    label: 'عدد الإجازات',
                    data: [{% for stat in month_stats %}{{ stat.leave_count }},{% endfor %}],
                    backgroundColor: 'rgba(14, 165, 233, 0.1)',
                    borderColor: medicalColors.primary,
                    borderWidth: 3,
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: medicalColors.primary,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 3,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointHoverBackgroundColor: medicalColors.secondary,
                    pointHoverBorderColor: '#ffffff',
                    pointHoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(14, 165, 233, 0.9)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: medicalColors.secondary,
                        borderWidth: 1,
                        cornerRadius: 8
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                            color: '#6b7280'
                        },
                        grid: {
                            color: 'rgba(14, 165, 233, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#6b7280'
                        },
                        grid: {
                            color: 'rgba(14, 165, 233, 0.1)'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    });
</script>
{% endblock %}
